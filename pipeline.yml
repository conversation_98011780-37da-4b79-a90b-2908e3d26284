version: "1.1"
name: ""
stages:
  - stage:
      - git-checkout:
          alias: git-checkout
          description: 代码仓库克隆
  - stage:
      - java-build:
          alias: contract-deploy
          description: 针对 java 工程的编译打包任务
          version: "1.0"
          params:
            build_cmd:
              - mvn clean deploy -Dmaven.test.skip -Dtrantor.deploy --settings ((MAVEN_SETTING_FILE)) -P ((BUILD_PROFILE)) -U
            jdk_version: 8
            workdir: ${git-checkout}
          resources:
            mem: 4096
            cpu: 2
  - stage:
      - java:
          alias: ep-contract-runtime
          description: 针对 java 工程的编译打包任务，产出可运行镜像
          params:
            build_type: maven
            container_type: spring-boot
            target: ./target/ep-contract-runtime.jar
            workdir: ${git-checkout}/ep-contract-runtime
          resources:
            cpu: 2
  - stage:
      - api-test:
          alias: 创建元数据发布计划
          description: 执行单个接口测试。上层可以通过 pipeline.yml 编排一组接口测试的执行顺序。
          version: "2.0"
          params:
            asserts:
              - arg: code
                operator: =
                value: "200"
            body:
              content: |-
                {
                    "actionKey":"meta_store_management_PreInstallModulePlan_PreInstallAction::createResourcePublish",
                    "context":{
                        "modelKey":"meta_store_management_PreInstallModulePlan",
                        "actionLabel":"提交",
                        "record":[
                            {
                                "name":"contract",
                                "productSpecificVersion":{
                                    "product":{
                                        "_PARENT_ID":"09cf213ad1f8ce79c8de4fe643d9595b",
                                        "name":"gaia",
                                        "id":"c6a653bdd930b5aa9b54aed390ce008b"
                                    },
                                    "id":"09cf213ad1f8ce79c8de4fe643d9595b",
                                    "versionUniqueTag":"gaia_2.3.0"
                                },
                                "type":"RESOURCE_PUBLISH",
                                "resourceType":"MODULE_RESOURCE",
                                "versions":[
                                    {
                                        "versionUniqueTag":"contract_1.0.0.ZJ01",
                                        "id":"a6f55a89bc733872f711c07462a9fa7d"
                                    },
                                    {
                                        "versionUniqueTag":"b2b_contract_1.0.0.ZJ01",
                                        "id":"f05cea227a186c9ff62ef86c615154a3"
                                    }
                                ],
                                "targetEnvironment":{
                                    "id":((envId))
                                },
                                "content":null
                            }
                        ]
                    }
                }
              type: JSON(application/json)
            headers:
              - key: trantor-project
                value: "1"
              - key: trantor-tenant
                value: "1"
              - key: x-trantor-app
                value: t-resources-publish
            method: POST
            name: 创建交易元数据发布计划
            out_params:
              - expression: status
                key: code
                source: status
            url: ((executePlanUrl))
  - stage:
      - api-test:
          alias: 查询最新的元数据发布计划
          description: 执行单个接口测试。上层可以通过 pipeline.yml 编排一组接口测试的执行顺序。
          version: "2.0"
          params:
            asserts:
              - arg: planId
                operator: not_empty
                value: ""
            body:
              content: |-
                {
                	"singleResult": false,
                	"targetModel": "meta_store_management_PreInstallModulePlan",
                	"sourceModel": "meta_store_management_PreInstallModulePlan",
                	"searchValues": {
                		"name": {
                			"type": "One",
                			"value": "contract"
                		},
                		"status": {
                			"type": "Collection",
                			"values": ["UNCONFIRMED"]
                		}
                	},
                	"dataSource": {
                		"actionKey": "meta_store_management_PreInstallModulePlan_toListResourcePublish"
                	},
                	"result": {
                		"fields": [{
                			"fieldName": "id"
                		}, {
                			"fieldName": "name"
                		}, {
                			"fieldName": "type"
                		}, {
                			"fieldName": "status"
                		}, {
                			"fieldName": "targetEnvironment",
                			"fields": [{
                				"fieldName": "id"
                			}, {
                				"fieldName": "key"
                			}, {
                				"fieldName": "securityProcessEnabled"
                			}]
                		}, {
                			"fieldName": "createdBy",
                			"fields": [{
                				"fieldName": "id"
                			}, {
                				"fieldName": "username"
                			}]
                		}, {
                			"fieldName": "resourceType"
                		}, {
                			"fieldName": "createdAt"
                		}, {
                			"fieldName": "updatedBy",
                			"fields": [{
                				"fieldName": "id"
                			}, {
                				"fieldName": "username"
                			}]
                		}, {
                			"fieldName": "updatedAt"
                		}]
                	},
                	"paging": {
                		"no": 1,
                		"size": 10
                	},
                	"order": {
                		"by": "updatedAt",
                		"isAsc": false
                	}
                }
              type: JSON(application/json)
            headers:
              - desc: ""
                key: trantor-project
                value: "1"
              - desc: ""
                key: trantor-tenant
                value: "1"
            id: ""
            method: POST
            name: 查询最新的元数据发布计划
            out_params:
              - expression: res.data[0].id
                key: planId
                source: body:json
            params: []
            url: ((queryPlanUrl))
  - stage:
      - custom-script:
          alias: 等待5秒
          description: 运行自定义命令
          version: "1.0"
          commands:
            - sleep 5s
          resources:
            cpu: 0.1
  - stage:
      - api-test:
          alias: 执行发布计划
          description: 执行单个接口测试。上层可以通过 pipeline.yml 编排一组接口测试的执行顺序。
          version: "2.0"
          params:
            asserts: []
            body:
              content: |-
                {
                  "actionKey": "meta_store_management_PreInstallModulePlan_PreInstallAction::preInstall",
                  "context": {
                    "modelKey": "meta_store_management_PreInstallModulePlan",
                    "actionLabel": "????",
                    "record": [
                      {
                        "id": ${{ outputs.查询最新的元数据发布计划.planId }}
                      }
                    ]
                  }
                }
              type: JSON(application/json)
            headers:
              - desc: ""
                key: trantor-project
                value: "1"
              - desc: ""
                key: trantor-tenant
                value: "1"
              - desc: ""
                key: x-trantor-app
                value: t-resources-publish
            id: ""
            method: POST
            name: 执行发布计划
            out_params: []
            params: []
            url: ((executePlanUrl))
  - stage:
      - release:
          alias: release
          description: 用于打包完成时，向dicehub 提交完整可部署的dice.yml。用户若没在pipeline.yml里定义该action，CI会自动在pipeline.yml里插入该action
          params:
            dice_yml: ${git-checkout}/dice.yml
            image:
              ep-contract-runtime: ${ep-contract-runtime:OUTPUT:image}
  - stage:
      - dice:
          alias: dice
          description: 用于 Erda 平台部署应用服务
          params:
            release_id: ${release:OUTPUT:releaseID}