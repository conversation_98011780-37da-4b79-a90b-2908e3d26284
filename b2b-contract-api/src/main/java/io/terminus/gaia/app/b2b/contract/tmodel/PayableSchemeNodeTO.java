package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.gaia.app.b2b.contract.dict.PayableSchemePeriodDict;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.DictionaryMeta;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-09-05
 * @descrition
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "payble amt to")
public class PayableSchemeNodeTO extends BaseModel<Long> {

    @Field(name = "应收比例")
    private BigDecimal payableRatio;

    @Field(name = "款项补充说明")
    private String payableInfo;

    @Field(name = "应收账期")
    private Integer payablePeriod;

    @Field(name = "账期类型")
    @DictionaryMeta(PayableSchemePeriodDict.class)
    private String periodType;

//    @Field(name = "json")
//    private String schemes;



}
