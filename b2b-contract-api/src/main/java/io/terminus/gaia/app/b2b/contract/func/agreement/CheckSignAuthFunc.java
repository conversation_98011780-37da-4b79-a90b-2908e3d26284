package io.terminus.gaia.app.b2b.contract.func.agreement;

import io.terminus.gaia.app.b2b.contract.tmodel.AgreementSigntureTaskTO;
import io.terminus.gaia.app.b2b.trade.model.fulfillment.query.QDeliveryOrderV2BO;
import io.terminus.gaia.md.model.signature.SignatureAuthInfoBO;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21
 * @description 检查是否授权
 */
@Function
public interface CheckSignAuthFunc {
    List<SignatureAuthInfoBO> execute(AgreementSigntureTaskTO agreementSigntureTaskTO);
}
