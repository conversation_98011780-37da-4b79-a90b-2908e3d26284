package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * <AUTHOR>
 * @time 2025/7/9 09:41
 */
@Function
public interface SupplierConfirmAskPriceFunc {

    BooleanResult execute(AskSupplierPriceBO req);

}
