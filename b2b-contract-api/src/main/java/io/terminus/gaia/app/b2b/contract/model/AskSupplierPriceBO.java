package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.gaia.app.b2b.contract.dict.AskSupplierPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceReplyTO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.gaia.organization.model.ext.ProjectBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.*;
import io.terminus.trantorframework.api.annotation.typemeta.DictionaryMeta;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import io.terminus.trantorframework.api.annotation.typemeta.TextMeta;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 供应商-报价单信息
 *
 * 供应商报价单信息（供应商报价合价、供应商信息、协议信息、第n轮次询价报价、供应商询价报价状态）-（供应商询价报价明细清单）
 *
 * <AUTHOR>
 */
@Model(
        name = "供应商-报价单信息",
        config = @ModelConfig(
                enableExport = true)
)
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AskSupplierPriceBO extends BaseModel<Long> {
    @Field(name = "需求池", desc = "需求池")
    @LinkMeta
    private RequestPurchaseBO requestPurchaseBO;

    @Field(name = "询价单", desc = "询价单")
    @LinkMeta
    private AskPriceBO askPriceBO;

    @Field(name = "供应方", desc = "供应方id")
    @LinkMeta
    private EntityBO supplierEntity;

    @Field(name = "状态", desc = "状态")
    @DictionaryMeta(value = AskSupplierPriceStatusDict.class)
    private String status;

    @Field(name = "当前轮数", desc = "当前轮数")
    private Integer currentRound;

    @Field(name = "询价结果回执", desc = "询价结果回执", type = FieldType.Json)
    private List<AskSupplierPriceReplyTO> replyTOList;

    /**
     * @param id id
     * @return AskSupplierPriceBO
     */
    public static AskSupplierPriceBO of(Long id) {
        if (id == null) {
            return null;
        }
        AskSupplierPriceBO askSupplierPriceBO = new AskSupplierPriceBO();
        askSupplierPriceBO.setId(id);
        return askSupplierPriceBO;
    }

}
