package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceImportTO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 导入询价清单
 *
 * <AUTHOR>
 * @time 2025/7/9 17:00
 */
@Function(name = "导入询价清单")
public interface ImportAskSupplierPriceFunc {
    
    BooleanResult execute(AskSupplierPriceImportTO importTO);
}
