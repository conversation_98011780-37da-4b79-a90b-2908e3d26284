package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.gaia.app.b2b.contract.dict.*;
import io.terminus.gaia.app.b2b.contract.tmodel.YzContactTO;
import io.terminus.gaia.md.dict.ProcurementTypeDict;
import io.terminus.gaia.md.dict.SyncStatusDict;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.model.ext.ProjectBO;
import io.terminus.gaia.organization.tmodel.PaymentOverdueRuleTO;
import io.terminus.gaia.settings.dict.YesOrNoDict;
import io.terminus.trantor.module.base.model.User;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.*;
import io.terminus.trantorframework.api.annotation.typemeta.*;
import io.terminus.trantorframework.api.type.Currency;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Model(name = "合同",
        mainField = YzContractBO.contractName_field,
        config = @ModelConfig(enableExport = true),
        indexes = {
                @Index(columns = "contractCode"),
                @Index(columns = "contractName"),
                @Index(columns = "externalCode"),
                @Index(columns = "contractTypeDict"),
                @Index(columns = "contractStatusDict"),
                @Index(columns = YzContractBO.signAt_field),
        })
public class YzContractBO extends BaseModel<Long> {
    @Field(name = "合同编号", nullable = false, desc = "合同编号")
    private String contractCode;

    @Field(name = "合同uuid(IPM使用)", nullable = false, desc = "合同uuid(IPM使用)")
    private String contractUUID;

    @Field(name = "外部合同编号", desc = "外部合同编号(云筑)")
    private String externalCode;

    @Field(name = "背靠背外部合同编号", desc = "背靠背外部合同编号")
    private String backExternalCode;

    @Field(name = "合同名称", nullable = false, desc = "合同名称")
    private String contractName;

    @Field(name = "合同类型", nullable = false, desc = "合同类型")
    @DictionaryMeta(YzContractTypeDict.class)
    private String contractTypeDict;

    @Field(name = "状态", nullable = false, desc = "状态")
    @DictionaryMeta(YzContractStatusDict.class)
    private String contractStatusDict;

    /**
     * 1：在施，2：已完工，3：已结算，4：已保修，5：已作废（ipm以外成本系统对照：已完成->在施，已作废->已作废）
     */
    @Field(name = "履约状态", nullable = false, desc = "履约状态")
    private Integer performanceStatus;

    /**
     * 合同主体
     * 合同签约的甲方
     */
    @Field(name = "合同甲方", desc = "合同甲方id")
    @LinkMeta
    private EntityBO partyA;

    @Field(name = "合同乙方", desc = "合同乙方id")
    @LinkMeta
    private EntityBO partyB;

    @Field(name = "付款方", type = FieldType.Json)
    @Transient
    private EntityBO payer;

    @Field(name = "分类", desc = "分类id")
    @LinkMeta
    private CategoryBO category;


    @Field(name = "供应商联系人", type = FieldType.Json, desc = "供应商联系人")
    private YzContactTO partBContact;

    @Field(name = "项目分期", desc = "项目id")
    @LinkMeta
    private ProjectBO projectStage;

    @Field(name = "合同签订日期", desc = "合同签订日期")
    private Date signAt;

    @Field(name = "开工日期", desc = "开工日期")
    private Date startDate;

    @Field(name = "竣工日期", desc = "竣工日期")
    private Date completedDate;

    /**
     * 未税签约金额
     * 合同的不含税签约金额
     */
    @Field(name = "合同金额（不含税）", desc = "合同金额（不含税）")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency contractAmt;

    @Field(name = "合同金额（含税）", desc = "合同金额（含税）")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency contractTaxAmt;

    @Field(name = "税额", desc = "税额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency taxAmt;

    @Field(name = "业务类型", desc = "业务类型")
    @DictionaryMeta(value = YzContractBusinessTypeDict.class)
    private String businessType;

    @Field(name = "已下单金额", desc = "可下单金额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency placedAmt;

    @Field(name = "可下单金额", desc = "可下单金额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency placableAmt;

    @Field(name = "需支付金额", desc = "需要支付金额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency needPayAmt;

    @Field(name = "已支付金额", desc = "已支付金额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency paidAmt;

    @Field(name = "关联协议", desc = "关联协议id")
    @LinkMeta
    private AgreementBO agreementBO;

    /**
     * 是否大宗合同
     */
    @Field(name = "是否大宗合同",desc = "是否大宗合同")
    private Boolean bulkContract;

    @Field(name = "关联主合同", desc = "关联主合同id")
    @LinkMeta
    private YzContractBO mainContractBO;

    @Field(name = "是否补充合同", desc = "是否补充合同")
    private Boolean isSupply;

    @Field(name = "是否集付", desc = "是否集付")
    private Boolean isCenterPay;

    @Field(name = "云筑网合同编号", desc = "云筑网合同编号")
    private String yzwContractNo;

    @Field(name = "是否商城支付", desc = "是否商城支付")
    private Boolean isMallPay;

    /**
     * 成本系统
     *     CSCEC_ONE(1, "一局总部"),
     *     CSCEC_ONE_THREE(2, "一局三"),
     *     CSCEC_ONE_DEVELOP(3, "一局发展"),
     *     CSCEC_ONE_LAND(4, "一局智地");
     * */
    @Field(name = "来源系统", desc = "来源系统")
    private Integer sourceSystem;

    @Field(name = "付款方案", type = FieldType.Json, desc = "付款方案")
    private PaymentSchemeBO paymentScheme;

    @Field(name = "关联补充合同", desc = "关联补充合同")
    @LookupMeta(linkField = YzContractBO.mainContractBO_field)
    private List<YzContractBO> subContracts;

    @Field(name = "关联合同行", desc = "关联合同行")
    @LookupMeta(linkField = ContractLineBO.contract_field)
    private List<ContractLineBO> lines;

    @Field(name = "同步状态", desc = "同步状态")
    @DictionaryMeta(value = SyncStatusDict.class)
    private String syncStatus;

    @Field(name = "同步备注", desc = "同步备注")
    @TextMeta(length = 4096)
    private String syncRemark;

    @Field(name = "提交人", desc = "提交人")
    @LinkMeta
    private User submitBy;

    @Field(name = "提交人", desc = "提交人名称")
    private String submitByName;

    // ############################## 临时字段 #######################################

    @Transient
    @Field(name = "同步动作")
    private Integer syncAction;

    @Transient
    @Field(name = "云筑详情地址-运营端")
    private String yzAdminDetailUrl;

    @Transient
    @Field(name = "云筑详情地址-采购商")
    private String yzPurchaserDetailUrl;

    @Transient
    @Field(name = "云筑详情地址-供应商")
    private String yzSupplierDetailUrl;

    @Field(name = "关联合同行")
    @Transient
    private List<ContractLineBO> deleteLines;


    // ############################### 付款申请金额相关 #######################################
    @Field(name = "上次对账日期", desc = "上次对账日期")
    private Date lastReconciliationDate;

    @Field(name = "累积对账金额A", desc = "累积对账金额A")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumReconciliationAmt;

    @Field(name = "累积对账金额A不含税", desc = "累积对账金额A不含税")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumReconciliationNoTaxAmt;

    @Field(name = "累积对账金额A税额", desc = "累积对账金额A税额")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal sumReconciliationTax;

    /**
     * 只有IPM流程的创建了应付单才更新
     */
    @Field(name = "累计应付金额", desc = "累计应付金额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumPayableAmt;

    @Field(name = "累计应付金额不含税", desc = "累计应付金额不含税")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumPayableNoTaxAmt;

    @Field(name = "累计应付税额", desc = "累计应付税额")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal sumPayableTax;

    @Field(name = "累计申请预付金额", desc = "累计申请预付金额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumRequestPreAmt;

    @Field(name = "累积付款申请金额A", desc = "累积付款申请金额A")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumRequestAmt;

    @Field(name = "累积付款申请金额A不含税", desc = "累积付款申请金额A不含税")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumRequestTaxAmt;

    @Field(name = "预付款金额", desc = "预付款金额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumPrepayAmt;

    @Field(name = "预付款金额不含税", desc = "预付款金额不含税")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumPrepayNoTaxAmt;

    @Field(name = "进度款金额", desc = "进度款金额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumProgressAmt;

    @Field(name = "进度款金额不含税", desc = "进度款金额不含税")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumProgressNoTaxAmt;

    @Field(name = "预付款已抵扣金额（不含税）", desc = "预付款已抵扣金额（不含税）")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency prepaidDeductedAmountNoTaxAmt;

    @Field(name = "累计已付金额", desc = "累计已付金额")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumPaidAmt;

    @Field(name = "累计已付金额（不含税）", desc = "累计已付金额（不含税）")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency sumPaidAmtNoTaxAmt;

    @Field(name = "累计收货金额含税", desc = "累计收货金额含税")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal sumReceiveAmtTx;

    @Field(name = "累计收货金额不含税", desc = "累计收货金额不含税")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal sumReceiveAmtWithoutTx;

    @Field(name = "过程收货金额含税", defaultValue = "0", desc = "过程收货金额含税")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal sumProcessReceiveAmtTx;

    @Field(name = "过程收货金额不含税", defaultValue = "0", desc = "过程收货金额不含税")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal sumProcessReceiveAmtWithoutTx;

    @Field(name = "过程累计发货金额含税", defaultValue = "0", desc = "过程累计发货金额含税")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumProcessDeliveryAmtTx;

    @Field(name = "累计发货金额不含税", defaultValue = "0", desc = "累计发货金额不含税")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal sumProcessDeliveryAmtWithoutTx;

    @Field(name = "已发货数量", desc = "已发货数量", defaultValue = "0")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal sentQty;

    @Field(name = "验收数量", desc = "验收数量")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal receivedQty;

    @Field(name = "签约数量")
    @Transient
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal signedQty;

    @Field(name = "已下单数量")
    @Transient
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal placedQty;

    @Field(name = "是否新合同", desc = "是否新合同")
    private Boolean createNewContract;

    @Field(name = "是否发送过未履约提醒", desc = "是否发送过未履约提醒", defaultValue = "false")
    private Boolean haveSendNonComplianceRemind;

    @Field(name = "租赁报价方式")
    @DictionaryMeta(RentPeriodDict.class)
    private String rentPeriod;

    @Field(name = "租赁开始时间", desc = "租赁开始时间")
    private Date leaseStartTime;

    @Field(name = "租赁结束时间", desc = "租赁结束时间")
    private Date leaseEndTime;

    /**
     * 是否自动创建 1-是 0-否
     */
    @Field(name = "是否自动创建", desc = "是否自动创建")
    private Integer autoCreate;

    /**
     * 超发比例
     */
    @Field(name = "超发比例", desc = "超发比例")
    private BigDecimal exceedProportion;

    /**
     * 1按合同金额 2按合同金额比例
     */
    @Field(name = "超采类型", desc = "超采类型")
    private Integer controlMode;

    /**
     * 税率（0.13）
     */
    @Field(name = "税率", desc = "税率")
    private BigDecimal vatRate;

    /**
     * 主账期id
     */
    @Field(name = "主账期id", desc = "主账期id")
    private Long mainPayableSchemeId;

    /**
     * 合同账期json
     */
    @Field(name = "主账期id", desc = "主账期id", type = FieldType.Json)
    private List<PayableSchemeTemplateBO> payablePeriod;

    /**
     * 付款日(枚举 1：每个月5日，2：每个月月底)
     */
    @Field(name = "付款日", desc = "付款日")
    private String paymentDays;

    /**
     * 每月付款日(如：5,12,28)
     */
    @Field(name = "每月付款日", desc = "每月付款日")
    private String paymentMonthlyDay;

    /**
     * 每月对账日(如：5,12,28)
     */
    @Field(name = "每月对账日", desc = "每月对账日")
    private String reconciledDays;

    /**
     * 逾期规则配置，滞纳金及跳档
     */
    @Field(name = "逾期规则配置", desc = "滞纳金及跳档规则配置", type = FieldType.Json)
    private PaymentOverdueRuleTO paymentOverdueRule;

    @Field(name = "临时主账期id", desc = "临时主账期id", type = FieldType.Json)
    @Transient
    private PayableSchemeTemplateBO tmpPayableSchemeTemplateBO;

    @Field(name = "是否是历史合同", desc = "是否是历史合同")
    @Transient
    private Boolean isBuildHistory;

    /**
     * 是否直入直出
     */
    @Field(name = "是否是直入直出", desc = "是否是直入直出" , type = FieldType.Boolean)
    private Boolean isStraightInAndOut;

    @Field(name = "能否修改直入直出选项" , desc = "能否修改直入直出选项" , type = FieldType.Boolean)
    @Transient
    private Boolean canModifyStraightInAndOut;


    /**
     * 入库物资类型
     */
    @Field(name = "入库物资类型" , desc = "入库物资类型")
    @DictionaryMeta(IncomingMaterialTypeDict.class)
    @Transient
    private Integer incomingMaterialType;

    /**
     * 滞纳金跳档试用标记
     */
    @Field(name = "滞纳金跳档试用标记", desc = "滞纳金跳档试用标记")
    @DictionaryMeta(YesOrNoDict.class)
    private String isOverdueMock;

    /**
     * 合同计价模式 1含税模式 2不含税模式
     */
    @Field(name = "合同计价模式", desc = "合同计价模式")
    private Integer priceMode;

    @Field(name = "采购类型", desc = "1-集采 2-非集采")
    @DictionaryMeta(ProcurementTypeDict.class)
    private String procurementType;

    /**
     * 是否是补录合同意向 0否 1.是
     */
    @Field(name = "是否是补录合同意向", desc = "是否是补录合同意向")
    private String isReplenish;

    @Field(name = "对应成本管理系统", desc = "对应成本管理系统")
    private Integer costManagementSystem;

    @Field(name = "过程超结比例", desc = "过程超结比例")
    private BigDecimal processExceedProportion;

    public static YzContractBO of(Long id) {
        YzContractBO contractBO = new YzContractBO();
        contractBO.setId(id);
        return contractBO;
    }
}
