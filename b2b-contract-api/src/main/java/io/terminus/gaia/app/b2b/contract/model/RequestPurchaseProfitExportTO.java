package io.terminus.gaia.app.b2b.contract.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 需求销售价格导出Excel模板
 *
 * <AUTHOR>
 */
@Data
public class RequestPurchaseProfitExportTO {

    // 隐藏字段，用于导入时识别记录
    @ExcelProperty("明细ID")
    private Long lineId;

    @ExcelProperty("材料名称")
    private String needLineName;

    @ExcelProperty("规格型号")
    private String thingSizeDesc;

    @ExcelProperty("数量")
    private BigDecimal needNum;

    @ExcelProperty("计量单位")
    private String unitName;

    @ExcelProperty("协议名称")
    private String agreementName;

    @ExcelProperty("协议编号")
    private String agreementCode;

    @ExcelProperty("供应商")
    private String supplierName;

    @ExcelProperty("关联标品编号")
    private String spuCode;

    @ExcelProperty("标品名称")
    private String spuName;

    @ExcelProperty("含铜量")
    private BigDecimal purRawMaterialContent;

    @ExcelProperty("采购协议铜基价")
    private BigDecimal purCopperBasicPrice;

    @ExcelProperty("采购辅材及其他价格")
    private BigDecimal purOtherCosts;

    @ExcelProperty("采购综合含税单价")
    private BigDecimal purTaxPrice;

    @ExcelProperty("采购延米铜价")
    private BigDecimal purCopperPrice;

    @ExcelProperty("采购折扣系数")
    private BigDecimal purDiscountFactor;

    @ExcelProperty(value = "销售辅材及其他价格")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 13)
    private BigDecimal saleOtherCosts;

    @ExcelProperty(value = "销售折扣系数")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 13)
    private BigDecimal saleDiscountFactor;
} 