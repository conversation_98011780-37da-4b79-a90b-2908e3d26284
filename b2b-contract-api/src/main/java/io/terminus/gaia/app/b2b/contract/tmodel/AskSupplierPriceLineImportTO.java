package io.terminus.gaia.app.b2b.contract.tmodel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 询价清单导入Excel解析TO
 *
 * <AUTHOR>
 * @time 2025/7/9 17:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "询价清单导入Excel解析TO")
public class AskSupplierPriceLineImportTO extends BaseModel<Long> {

    @ExcelProperty(value = "物料名称", index = 0)
    @Field(name = "物料名称")
    private String materialName;

    @ExcelProperty(value = "分类", index = 1)
    @Field(name = "分类")
    private String categoryName;

    @ExcelProperty(value = "规格", index = 2)
    @Field(name = "规格")
    private String specification;

    @ExcelProperty(value = "品牌", index = 3)
    @Field(name = "品牌")
    private String brandName;

    @ExcelProperty(value = "数量", index = 4)
    @Field(name = "数量")
    private BigDecimal quantity;

    @ExcelProperty(value = "单位", index = 5)
    @Field(name = "单位")
    private String unitName;

    @ExcelProperty(value = "含铜量", index = 6)
    @Field(name = "含铜量")
    private BigDecimal copperContent;

    @ExcelProperty(value = "铜基价", index = 7)
    @Field(name = "铜基价")
    private BigDecimal copperBasePrice;

    @ExcelProperty(value = "延米铜价", index = 8)
    @Field(name = "延米铜价")
    private BigDecimal copperPricePerMeter;

    @ExcelProperty(value = "含税单价（必填）", index = 9)
    @Field(name = "含税单价（必填）")
    private BigDecimal taxInclusiveUnitPrice;
}
