package io.terminus.gaia.app.b2b.contract.dict;

import io.terminus.trantorframework.api.annotation.Dictionary;
import io.terminus.trantorframework.api.annotation.DictionaryItem;

/**
 * <AUTHOR>
 */
@Dictionary
public interface PriceModeDict {

    /**
     * 通过含税单价计算不含税：不含税价单价 = 含税单价 / (1+税率)
     * */
    @DictionaryItem(value = "含税模式")
    String WITH_TAX_MODE = "withTaxMode";

    /**
     * 通过不含税单价计算含税单价：含税价单价 = 不含税单价 * (1+税率)
     * */
    @DictionaryItem(value = "不含税模式")
    String WITHOUT_TAX_MODE = "withoutTaxMode";
}
