package io.terminus.gaia.app.b2b.contract.tmodel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 询价清单导出TO
 *
 * <AUTHOR>
 * @time 2025/7/9 16:30
 */
@Data
@TransientModel(name = "询价清单导出TO")
@ContentRowHeight(20)
@HeadRowHeight(25)
public class AskSupplierPriceLineExportTO {

    @ExcelProperty(value = "ID", order = 3)
    @ColumnWidth(20)
    private Long id;

    @ExcelProperty(value = "物料名称", order = 5)
    @ColumnWidth(20)
    private String materialName;

    @ExcelProperty(value = "分类", order = 10)
    @ColumnWidth(15)
    private String categoryName;

    @ExcelProperty(value = "规格", order = 20)
    @ColumnWidth(25)
    private String specification;

    @ExcelProperty(value = "品牌", order = 30)
    @ColumnWidth(15)
    private String brandName;

    @ExcelProperty(value = "数量", order = 40)
    @ColumnWidth(12)
    private BigDecimal quantity;

    @ExcelProperty(value = "单位", order = 50)
    @ColumnWidth(10)
    private String unitName;

    @ExcelProperty(value = "含铜量", order = 60)
    @ColumnWidth(12)
    private BigDecimal copperContent;

    @ExcelProperty(value = "铜基价", order = 70)
    @ColumnWidth(15)
    private BigDecimal copperBasePrice;

    @ExcelProperty(value = "延米铜价", order = 80)
    @ColumnWidth(15)
    private BigDecimal copperPricePerMeter;

    @ExcelProperty(value = "含税单价（必填）", order = 90)
    @ColumnWidth(18)
    private BigDecimal taxInclusiveUnitPrice;
}
