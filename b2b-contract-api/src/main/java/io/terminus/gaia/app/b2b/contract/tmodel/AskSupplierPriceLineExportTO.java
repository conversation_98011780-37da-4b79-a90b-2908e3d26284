package io.terminus.gaia.app.b2b.contract.tmodel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 询价清单导出TO
 *
 * <AUTHOR>
 * @time 2025/7/9 16:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "询价清单导出TO")
@ContentRowHeight(20)
@HeadRowHeight(25)
public class AskSupplierPriceLineExportTO extends BaseModel<Long> {

    @ExcelProperty(value = "物料名称", index = 0)
    @ColumnWidth(20)
    @Field(name = "物料名称")
    private String materialName;

    @ExcelProperty(value = "分类", index = 1)
    @ColumnWidth(15)
    @Field(name = "分类")
    private String categoryName;

    @ExcelProperty(value = "规格", index = 2)
    @ColumnWidth(25)
    @Field(name = "规格")
    private String specification;

    @ExcelProperty(value = "品牌", index = 3)
    @ColumnWidth(15)
    @Field(name = "品牌")
    private String brandName;

    @ExcelProperty(value = "数量", index = 4)
    @ColumnWidth(12)
    @Field(name = "数量")
    private BigDecimal quantity;

    @ExcelProperty(value = "单位", index = 5)
    @ColumnWidth(10)
    @Field(name = "单位")
    private String unitName;

    @ExcelProperty(value = "含铜量", index = 6)
    @ColumnWidth(12)
    @Field(name = "含铜量")
    private BigDecimal copperContent;

    @ExcelProperty(value = "铜基价", index = 7)
    @ColumnWidth(15)
    @Field(name = "铜基价")
    private BigDecimal copperBasePrice;

    @ExcelProperty(value = "延米铜价", index = 8)
    @ColumnWidth(15)
    @Field(name = "延米铜价")
    private BigDecimal copperPricePerMeter;

    @ExcelProperty(value = "含税单价（必填）", index = 9)
    @ColumnWidth(18)
    @Field(name = "含税单价（必填）")
    private BigDecimal taxInclusiveUnitPrice;
}
