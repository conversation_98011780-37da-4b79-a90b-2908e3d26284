package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.gaia.app.b2b.contract.dict.PayableSchemePeriodDict;
import io.terminus.gaia.app.b2b.contract.dict.PayableSchemeTypeDict;
import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.DictionaryMeta;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import io.terminus.trantorframework.api.annotation.typemeta.TextMeta;
import lombok.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-05
 * @descrition
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "payble amt to")
public class PayableSchemeTO extends BaseModel<Long> {

    /**
     * 用作合并展示
     */
    @Field(name = "应收节点")
    private String title;

    @Field(name = "应收账期")
    private String content;

    @Field(name = "编号")
    private String code;

    @Field(name = "方案描述")
    @TextMeta(length = 2048)
    private String mark;

    @Field(name = "应收账款方案", type = FieldType.Json)
    private List<PayableSchemeNodeTO> schemeNodes;

    @Field(name = "类型")
    @DictionaryMeta(PayableSchemeTypeDict.class)
    private String type;

    @Field(name = "关联模版id")
    @LinkMeta
    private PayableSchemeTemplateBO payableSchemeTemplateBO;
}
