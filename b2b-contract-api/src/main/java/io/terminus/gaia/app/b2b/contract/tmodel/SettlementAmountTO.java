package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.FloatMeta;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TransientModel
public class SettlementAmountTO extends RootModel<Long> {

    @Field(name = "累积对账金额A")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumReconciliationAmt;

    @Field(name = "累积对账金额A不含税")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumReconciliationNoTaxAmt;

    /**
     * 只有IPM流程的创建了应付单才更新
     */
    @Field(name = "累计应付金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumPayableAmt;

    @Field(name = "累计应付金额不含税")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumPayableNoTaxAmt;


    @Field(name = "累计申请预付金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumRequestPreAmt;

    @Field(name = "累积付款申请金额A")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumRequestAmt;

    @Field(name = "累积付款申请金额A不含税")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumRequestTaxAmt;

    @Field(name = "预付款金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumPrepayAmt;

    @Field(name = "预付款金额不含税")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumPrepayNoTaxAmt;

    @Field(name = "预付款已抵扣金额（不含税）")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal prepaidDeductedAmountNoTaxAmt;

    @Field(name = "累计已付金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumPaidAmt;

    @Field(name = "累计已付金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumPaidAmtNoTaxAmt;

    @Field(name = "累积对账税额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumSettleTaxAmt;

    @Field(name = "累计收货金额含税")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumReceiveAmtTx;

    @Field(name = "累计收货金额不含税")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal sumReceiveAmtWithoutTx;

    @Field(name = "累计下单金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal placedAmt;

    @Field(name = "累计下单金额（不含税）")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal placedAmtWithoutTax;

}
