package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 询价清单导入TO
 *
 * <AUTHOR>
 * @time 2025/7/9 17:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "询价清单导入TO")
public class AskSupplierPriceImportTO extends BaseModel<Long> {

    @Field(name = "供应商报价单ID")
    private Long askSupplierPriceId;

    @Field(name = "文件URL")
    private String fileUrl;
}
