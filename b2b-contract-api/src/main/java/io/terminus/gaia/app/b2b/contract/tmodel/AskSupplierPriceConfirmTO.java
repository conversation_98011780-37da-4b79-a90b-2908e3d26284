package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import lombok.*;

/**
 * <AUTHOR>
 * @time 2025/7/7 17:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "AskSupplierPriceReplyTO")
public class AskSupplierPriceConfirmTO extends BaseModel<Long> {

    @Field(name = "询价单id")
    private Long askPriceId;

    @Field(name = "账期模版")
    @LinkMeta
    private PayableSchemeTemplateBO payableSchemeTemplateBO;

    @Field(name = "供应商", type = FieldType.Json)
    @LinkMeta
    private EntityBO entityBO;
}
