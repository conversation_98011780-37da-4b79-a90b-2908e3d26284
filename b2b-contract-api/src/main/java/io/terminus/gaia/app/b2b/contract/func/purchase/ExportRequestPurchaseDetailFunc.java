package io.terminus.gaia.app.b2b.contract.func.purchase;

import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.trantorframework.api.annotation.Function;
import io.terminus.trantor.module.base.model.result.StringResult;

/**
 * 导出需求明细
 * 
 * <AUTHOR>
 */
@Function(name = "导出需求明细")
public interface ExportRequestPurchaseDetailFunc {
    StringResult execute(RequestPurchaseBO requestPurchaseBO);
} 