package io.terminus.gaia.app.b2b.contract.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.terminus.gaia.app.b2b.contract.dict.*;
import io.terminus.gaia.app.b2b.contract.tmodel.JcBidInfoTO;
import io.terminus.gaia.app.b2b.contract.tmodel.PayableSchemeTO;
import io.terminus.gaia.app.b2b.contract.tmodel.SettlementAmountTO;
import io.terminus.gaia.contract.dict.common.ApproveStatusDict;
import io.terminus.gaia.md.dict.SyncStatusDict;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.md.model.TaxRateBO;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.gaia.organization.model.EmployeeBO;
import io.terminus.gaia.organization.model.ext.ProjectBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.*;
import io.terminus.trantorframework.api.annotation.typemeta.*;
import io.terminus.trantorframework.api.type.Attachment;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Model(
        name = "协议",
        mainField = "name",
        fieldGroups = {
                @FieldGroup(type = FieldGroupType.DEFAULT_SHOW, fieldName = {"code", "name", "type"})
        },
        config = @ModelConfig(
                enableExport = true),
        indexes = {
                @Index(columns = "code", unique = true),
                @Index(columns = "name"),
                @Index(columns = "externalCode"),
                @Index(columns = "externalNo"),
                @Index(columns = "status"),
                @Index(columns = "bidTaskCode"),
                @Index(columns = "bidTaskName"),
        }
)
@Data
@EqualsAndHashCode(callSuper = true)
public class AgreementBO extends BaseModel<Long> {
    @Field(name = "商城协议编码", desc = "商城协议编码")
    @TextMeta(rule = "STRING(CO)+TIMES(YYMMdd)+INCRE(1,8,4,1)")
    private String code;

    @Field(name = "名称", desc = "名称")
    private String name;

    @Field(name = "别名", desc = "别名")
    private String aliasName;

    @Field(name = "协议类型", desc = "协议类型")
    @DictionaryMeta(value = AgreementTypeDict.class)
    private String type;

    @Field(name = "覆盖范围", desc = "覆盖范围")
    @DictionaryMeta(value = AgreementCoverAreaTypeDict.class)
    private String coverAreaType;

    @Field(name = "计价模式", desc = "计价模式")
    @DictionaryMeta(value = PriceModeDict.class)
    private String priceMode;

    @Field(name = "云筑协议编码", desc = "云筑协议编码")
    private String externalNo;

    @Field(name = "外部协议ID", desc = "外部协议ID")
    private String externalCode;

    @Field(name = "含税金额", desc = "含税金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal taxAmt;

    @Field(name = "不含税金额", desc = "不含税金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal noTaxAmt;

    @Field(name = "税额", desc = "税额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal taxPrc;

    @Field(name = "补充协议含税金额", desc = "补充协议含税金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal supplyTaxAmt;

    @Field(name = "补充协议不含税金额", desc = "补充协议不含税金额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal supplyNoTaxAmt;

    @Field(name = "补充协议税额", desc = "补充协议税额")
    @FloatMeta(intDigits = 20, decimalDigits = 6, displayDigits = 4)
    private BigDecimal supplyTaxPrc;


    @Field(name = "税率", desc = "税率id")
    @LinkMeta
    private TaxRateBO taxRateBO;

    @Field(name = "协议甲方", desc = "协议甲方id")
    @LinkMeta
    private EntityBO jfCompanyBO;

    @Field(name = "协议乙方", desc = "协议乙方id")
    @LinkMeta
    private EntityBO yfCompanyBO;

    @Field(name = "经销商", desc = "经销商")
    @TextMeta(length = 1024)
    private String dealerStr;

    @Field(name = "使用单位", desc = "使用单位id")
    @LinkMeta
    private List<DepartmentBO> departments;

    @Field(name = "使用单位", desc = "使用单位")
    @TextMeta(length = 1024)
    private String departmentStr;

    @Field(name = "适用项目类型", desc = "适用项目类型")
    @DictionaryMeta(value = AgreementProjectTypeDict.class)
    private List<String> projectTypes;

    @Field(name = "分类", desc = "分类id")
    @LinkMeta
    private CategoryBO category;

    @Field(name = "签约日期", desc = "签约日期")
    @DateTimeMeta(unit = TimeUnit.DAYS)
    private Date signAt;

    @Field(name = "生效日期", desc = "生效日期")
    @DateTimeMeta(unit = TimeUnit.DAYS)
    private Date effectiveAt;

    @Field(name = "终止日期", desc = "终止日期")
    @DateTimeMeta(unit = TimeUnit.DAYS)
    private Date expireAt;

    @Field(name = "状态", desc = "状态")
    @DictionaryMeta(value = AgreementStatusDict.class)
    private String status;

    @Field(name = "状态", desc = "状态")
    @DictionaryMeta(value = AgreementStatusDict.class)
    private String statusBefore;

    @Field(name = "同步状态")
    @DictionaryMeta(value = SyncStatusDict.class)
    private String syncStatus;

    @Field(name = "同步备注")
    @TextMeta(length = 4096)
    private String syncRemark;

    @Field(name = "备注")
    private String remark;

    @Field(name = "附件", desc = "附件")
    @AttachmentMeta(countLimit = 12, maxSize = 1024 * 1024)
    private Attachment attachment;

    @Field(name = "经办人", desc = "经办人")
    @LinkMeta
    private EmployeeBO operator;

    @Field(name = "经办单位", desc = "经办单位")
    @LinkMeta
    private DepartmentBO department;

    @Field(name = "创建方式")
    @DictionaryMeta(AgreementSourceDict.class)
    private String sourceDict;

    @Field(name = "招标任务ID", desc = "招标任务ID")
    private Long bidTaskId;

    @Field(name = "招标任务编码", desc = "招标任务编码")
    private String bidTaskCode;

    @Field(name = "招标任务名称", desc = "招标任务名称")
    private String bidTaskName;

    @Field(name = "是否补充协议", desc = "是否补充协议")
    private Boolean isSupply;

    /**
     * 是否子公司协议
     */
    @Field(name = "分子公司小框架协议", desc = "分子公司小框架协议")
    private Boolean isSubCompany;

    @Field(name = "是否集中支付", desc = "是否集中支付")
    private Boolean isCenterPay;

    @Field(name = "是否关联清单", desc = "是否关联清单")
    private Boolean canRelateDetail;

    @Field(name = "集采招标信息", type = FieldType.Json, desc = "集采招标信息")
    private JcBidInfoTO jcBidInfo;

    @Field(name = "结算金额信息", type = FieldType.Json, desc = "结算金额信息")
    private SettlementAmountTO settlementAmount;

    @Field(name = "应收账款方案", type = FieldType.Json)
    private List<PayableSchemeTO> payableSchemeList;

    @Field(name = "上次对账日期", desc = "上次对账日期")
    private Date lastReconciliationDate;

    @Field(name = "关联主协议", desc = "关联主协议id")
    @LinkMeta
    private AgreementBO mainAgreement;

    /**
     * 发展公司框架协议时用到
     */
    @Field(name = "关联协议", desc = "关联协议id")
    @LinkMeta
    private AgreementBO relateAgreement;

    @Field(name = "联系人", desc = "联系人")
    @LookupMeta(linkField = AgreementContactBO.agreementBO_field)
    private List<AgreementContactBO> contacts;

    @Field(name = "经销商", desc = "经销商")
    @LookupMeta(linkField = AgreementDealerBO.agreementBO_field)
    private List<AgreementDealerBO> dealers;

    @Field(name = "经销商明细", desc = "经销商明细")
    @LookupMeta(linkField = AgreementDealerLineBO.agreementBO_field)
    private List<AgreementDealerLineBO> dealerLines;

    @Field(name = "付款方案", desc = "付款方案")
    @LookupMeta(linkField = PaymentSchemeBO.agreementBO_field)
    private List<PaymentSchemeBO> paymentSchemes;

    //@Field(name = "付款方案", desc = "付款方案")
    //@LinkMeta
    //private List<PaymentSchemeV2BO> paymentSchemeV2;

    @Field(name = "覆盖范围", desc = "覆盖范围")
    @LookupMeta(linkField = AgreementCoverAreaBO.agreementBO_field)
    private List<AgreementCoverAreaBO> coverAreas;

    @Field(name = "协议清单", desc = "协议清单")
    @LookupMeta(linkField = AgreementDetailBO.agreementBO_field)
    private List<AgreementDetailBO> details;

    @Field(name = "补充协议清单", desc = "补充协议清单")
    @LookupMeta(linkField = AgreementDetailBO.supplementAgreementBO_field)
    private List<AgreementDetailBO> supplementDetails;

    @Field(name = "覆盖范围明细", desc = "覆盖范围明细")
    @LookupMeta(linkField = AgreementCoverAreaLineBO.agreementBO_field)
    private List<AgreementCoverAreaLineBO> coverAreaLines;

    @Field(name = "指定自动对账日期", desc = "指定自动对账日期")
    @LookupMeta(linkField = AgreementRegularReconciliationBO.agreementBO_field)
    private List<AgreementRegularReconciliationBO> regularReconciliations;

    @Field(name = "使用单位", desc = "使用单位")
    @LookupMeta(linkField = AgreementUsingDepartmentBO.agreementBO_field)
    private List<AgreementUsingDepartmentBO> usingDepartments;

    // ===================== 瞬时字段 ===================
    @Field(name = "删除的联系人")
    @Transient
    private List<AgreementContactBO> deleteContacts;

    @Field(name = "删除的经销商")
    @Transient
    private List<AgreementDealerBO> deleteDealers;

    @Field(name = "删除的经销商行")
    @Transient
    private List<AgreementDealerLineBO> deleteDealerLines;

    @Field(name = "删除的付款方案")
    @Transient
    private List<PaymentSchemeBO> deletePaymentSchemes;

    @Field(name = "删除的覆盖范围")
    @Transient
    private List<AgreementCoverAreaBO> deleteCoverAreas;

    @Field(name = "删除的覆盖范围行")
    @Transient
    private List<AgreementCoverAreaLineBO> deleteCoverAreaLines;

    @Field(name = "删除的使用单位")
    @Transient
    private List<AgreementUsingDepartmentBO> deleteUsingDepartments;

    @Field(name = "删除的账期定时设置")
    @Transient
    private List<AgreementRegularReconciliationBO> deleteRegularReconciliations;

    @Field(name = "项目列表")
    @Transient
    private List<ProjectBO> projects;

    @Field(name = "项目ID")
    @Transient
    private Long projectId;

    @Field(name = "商品ID")
    @Transient
    private Long itemId;

    @Field(name = "地址ID")
    @Transient
    private Long districtId;

    @Field(name = "单位ID")
    @Transient
    private Long departmentId;

    @Field(name = "是否查询可用项目")
    @Transient
    private Boolean queryEnableProject;

    /**
     * admin/supplier/purchaser
     */
    @Field(name = "来源端")
    @Transient
    private String fromSite;

    @Field(name = "使用单位能否为空")
    @Transient
    private Boolean departmentCanNull ;

    @Field(name = "已存在的协议")
    @Transient
    private AgreementBO existAgreement;

    @Field(name = "经销商")
    @LinkMeta
    @Transient
    private EntityBO dealer;

    @Field(name = "选中协议", type = FieldType.Json)
    @Transient
    private List<AgreementBO> selectData;

    @TextMeta(length = 4096)
    @Field(name = "补充说明", type = FieldType.RichText)
    private String remarks;


    @Field(name = "业务类型")
    @DictionaryMeta(YzContractTypeDict.class)
    private String bizType;


    @Field(name = "租赁报价方式")
    @DictionaryMeta(RentPeriodDict.class)
    private String rentPeriod;

    @Field(name = "协调人")
    private String optEmployeesStr;

    @Field(name = "协调人")
    @LinkMeta
    @Transient
    private List<EmployeeBO> optEmployees;

    @Field(name = "责任单位", type = FieldType.Json)
    @LinkMeta
    private List<DepartmentBO> optDepartments;

    @Field(name = "责任单位", type = FieldType.Json)
    private List<String> optDepartmentIdList;

    @Field(name = "责任单位操作权限")
    @Transient
    private Boolean editPermission = false;


    @Field(name = "是否维护完整")
    private Boolean maintainCompleted;

    @Field(name = "ids")
    @Transient
    private String ids;

    @Field(name = "合同数量")
    @Transient
    private Integer contractQuantity;

    @Field(name = "补充协议数量")
    @Transient
    private Integer supplementaryAgreedQuantity;

    @Field(name = "是否最终结算", defaultValue = "false")
    private Boolean isFinalSettlement;

    @Field(name = "是否已使用模版维护", defaultValue = "false")
    private Boolean useTemplateMaintain;

    @Field(name = "使用的模版")
    @LinkMeta
    private AgreementTemplateBO agreementTemplateBO;

    @Field(name = "审批流Id")
    private String summaryId;

    @Field(name = "审批提交人")
    private String summaryName;

    @Field(name = "审批状态", desc = "审批状态")
    @DictionaryMeta(ApproveStatusDict.class)
    private String approveStatusDict;

    /**
     * bpm信息创建/更新标识（处理历史数据）
     *
     * true  创建bpm行
     * false 更新bpm行
     * */
    @Field(name = "bpm信息创建/更新标识")
    @Transient
    private Boolean needCreateBpmLine;

    @Field(name = "审批表单-补充信息", desc = "审批表单-补充信息")
    @LinkMeta
    private AgreementBpmBO agreementBpmBO;

    @Field(name = "signatureTask", desc = "电子签署任务")
    @LinkMeta
    private SignatureTaskBO signatureTask;

    @Field(name = "合同是否允许修改付款方案", desc = "合同是否允许修改付款方案")
    private Boolean contractCanEditPayScheme;



    /**
     * init
     * @param id
     * @return
     */
    public static AgreementBO of(Long id) {
        if (id == null) {
            return null;
        }
        AgreementBO agreementBO = new AgreementBO();
        agreementBO.setId(id);
        return agreementBO;
    }

    /**
     * 是否同步成功
     *
     * @return
     */
    @JsonIgnore
    public boolean isSyncSuccess() {
        return SyncStatusDict.SUCCESS.equals(syncStatus);
    }
}
