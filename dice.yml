version: '2.0'
services:
  ep-contract-runtime:
    ports:
      - 8080
    resources:
      cpu: 1
      mem: 4096
      network:
        mode: container
    deployments:
      replicas: 2
    expose:
      - 8080
    health_check:
      http:
        port: 8080
        path: /actuator/health
        duration: 3000
    envs:
      JAVA_OPTS: >-
        -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8001
        -XX:+PrintGCDetails -XX:+PrintGCDateStamps
        -XX:+PrintTenuringDistribution -XX:+PrintHeapAtGC 
        -XX:+HeapDumpOnOutOfMemoryError  -XX:HeapDumpPath=/container/data.hprof
        -XX:+UseContainerSupport  -XX:InitialRAMPercentage=50.0
        -XX:MaxRAMPercentage=85.0
    volumes:
      - storage: nfs
        path: /container/data
addons:
  api-gateway:
    plan: api-gateway:basic
  registercenter:
    plan: registercenter:basic
  redis:
    plan: redis:basic
    options:
      version: 3.2.12
  trantor-env:
    plan: custom:basic
    options:
      version: 1.0.0
  rocketmq:
    plan: rocketmq:basic
    options:
      version: 4.2.0
  oss-test:
    plan: custom:basic
    options:
      version: 1.0.0
envs: {}