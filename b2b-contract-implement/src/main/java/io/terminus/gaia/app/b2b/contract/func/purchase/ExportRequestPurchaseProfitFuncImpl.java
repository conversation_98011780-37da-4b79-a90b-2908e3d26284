package io.terminus.gaia.app.b2b.contract.func.purchase;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import io.terminus.gaia.app.b2b.contract.func.purchase.ExportRequestPurchaseProfitFunc;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseProfitExportTO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.taurus.common.exception.BizException;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantor.module.base.model.result.StringResult;
import io.terminus.trantorframework.sdk.sql.DS;
import io.terminus.trantorframework.sdk.upload.OSSClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 导出需求销售价格清单实现
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class ExportRequestPurchaseProfitFuncImpl implements ExportRequestPurchaseProfitFunc {

    private final OSSClient ossClient;

    @Override
    public StringResult execute(RequestPurchaseBO requestPurchaseBO) {
        log.info("开始导出需求销售价格清单，需求单ID: {}", requestPurchaseBO.getId());

        Long requestPurchaseId = requestPurchaseBO.getId();
        if (requestPurchaseId == null) {
            throw new BizException("需求单ID不能为空");
        }

        // 查询需求明细数据，包含关联的协议、供应商、标品等信息
        List<RequestPurchaseLineBO> requestPurchaseLines = DS.findAll(RequestPurchaseLineBO.class, 
                "*,brandBO.*,unit.*,agreement.*,supplier.*,spu.*,agreementDetailBO.*", 
                "requestPurchaseBO.id = ? ORDER BY sort ASC",
                requestPurchaseId);

        if (CollectionUtils.isEmpty(requestPurchaseLines)) {
            throw new BizException("该需求单没有明细数据，无法导出");
        }

        // 按品牌分组
        Map<BrandBO, List<RequestPurchaseLineBO>> brandGroupMap = requestPurchaseLines.stream()
                .collect(Collectors.groupingBy(RequestPurchaseLineBO::getBrandBO));

        log.info("按品牌分组完成，共{}个品牌", brandGroupMap.size());

        // 生成Excel文件
        String fileName = StrUtil.format("trantor/attachments/需求销售价格清单-{}.xlsx", 
                DatePattern.PURE_DATETIME_FORMAT.format(new Date()));

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        try {
            // 使用 ExcelWriter 来管理多个 sheet
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

            int sheetIndex = 0;
            // 为每个品牌创建一个sheet页
            for (Map.Entry<BrandBO, List<RequestPurchaseLineBO>> entry : brandGroupMap.entrySet()) {
                BrandBO brandBO = entry.getKey();
                List<RequestPurchaseLineBO> lineList = entry.getValue();
                
                String sheetName = brandBO.getBrandName();
                log.info("正在处理品牌: {} 的数据，共{}条记录", sheetName, lineList.size());

                // 转换为导出TO
                List<RequestPurchaseProfitExportTO> exportData = convertToExportTO(lineList);
                
                // 创建 WriteSheet
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, sheetName)
                        .head(RequestPurchaseProfitExportTO.class)
                        .build();
                
                // 写入数据
                excelWriter.write(exportData, writeSheet);
                
                log.info("品牌: {} 的数据写入完成", sheetName);
                sheetIndex++;
            }
            
            // 关闭 ExcelWriter
            excelWriter.finish();

            // 上传到OSS
            ByteArrayInputStream inputStream = IoUtil.toStream(outputStream);
            ossClient.upload(fileName, inputStream);

            String url = ossClient.getUrlWithoutSignature(fileName);
            if (StrUtil.startWith(url, "http:")) {
                url = StrUtil.replace(url, "http:", "https:");
            }

            log.info("导出完成，文件地址: {}", url);
            return new StringResult(url);

        } catch (Exception e) {
            log.error("导出需求销售价格清单失败", e);
            throw new BizException("导出失败: " + e.getMessage());
        } finally {
            // 关闭输出流
            IoUtil.close(outputStream);
        }
    }

    /**
     * 转换为导出TO
     */
    private List<RequestPurchaseProfitExportTO> convertToExportTO(List<RequestPurchaseLineBO> lineList) {
        List<RequestPurchaseProfitExportTO> exportList = new ArrayList<>();
        
        for (RequestPurchaseLineBO line : lineList) {
            RequestPurchaseProfitExportTO exportTO = new RequestPurchaseProfitExportTO();
            
            // 基础信息
            exportTO.setLineId(line.getId());
            exportTO.setNeedLineName(line.getNeedLineName());
            exportTO.setThingSizeDesc(line.getThingSizeDesc());
            exportTO.setNeedNum(line.getNeedNum());
            exportTO.setUnitName(line.getUnit() != null ? line.getUnit().getUnitName() : "");
            
            // 协议信息
            if (line.getAgreement() != null) {
                exportTO.setAgreementName(line.getAgreement().getName());
                exportTO.setAgreementCode(line.getAgreement().getCode());
            }
            
            // 供应商信息
            if (line.getSupplier() != null) {
                exportTO.setSupplierName(line.getSupplier().getEntityName());
            }
            
            // 标品信息
            if (line.getSpu() != null) {
                exportTO.setSpuCode(line.getSpu().getSpuCode());
                exportTO.setSpuName(line.getSpu().getName());
            }
            
            // 采购相关价格
            exportTO.setPurRawMaterialContent(line.getPurRawMaterialContent());
            exportTO.setPurCopperBasicPrice(line.getPurCopperBasicPrice());
            exportTO.setPurOtherCosts(line.getPurOtherCosts());
            exportTO.setPurTaxPrice(line.getPurTaxPrice());
            exportTO.setPurCopperPrice(line.getPurCopperPrice());
            exportTO.setPurDiscountFactor(line.getPurDiscountFactor());
            
            // 销售相关价格（必填项）
            exportTO.setSaleOtherCosts(line.getSaleOtherCosts());
            exportTO.setSaleDiscountFactor(line.getSaleDiscountFactor());
            
            exportList.add(exportTO);
        }
        
        return exportList;
    }
} 