package io.terminus.gaia.app.b2b.contract.func.contract.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.google.common.base.MoreObjects;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import io.terminus.gaia.app.b2b.contract.func.contract.read.PageContractLineFunc;
import io.terminus.gaia.app.b2b.contract.model.ContractLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QContractLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QYzContractBO;
import io.terminus.gaia.app.b2b.contract.properties.ContractImportAttributeProperties;
import io.terminus.gaia.app.b2b.contract.tmodel.ContractLineImportRequestTO;
import io.terminus.gaia.app.b2b.contract.util.ContractExportHelper;
import io.terminus.gaia.common.utils.EnhanceDS;
import io.terminus.gaia.item.dict.item.ItemTypeNewDict;
import io.terminus.gaia.item.model.sku.SkuBO;
import io.terminus.gaia.item.tmodel.AttributeTO;
import io.terminus.gaia.md.dict.AttributeConfirmStageDict;
import io.terminus.gaia.md.model.UnitBO;
import io.terminus.trantor.module.base.model.result.StringResult;
import io.terminus.trantorframework.Page;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.type.Currency;
import io.terminus.trantorframework.querymodel.type.support.QLongId;
import io.terminus.trantorframework.sdk.flow.LF;
import io.terminus.trantorframework.sdk.upload.OSSClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class ContractLineExportFuncImpl implements ContractLineExportFunc {

    private final OSSClient ossClient;
    private final ContractImportAttributeProperties properties;
    private final PageContractLineFunc pageContractLineFunc;

    @Override
    public StringResult execute(ContractLineImportRequestTO request) {
        List<ContractLineBO> lineList = getContractLineList(request);

        /*Map<String, List<AttributeTO>> attributeListMap = lineList.stream().map(ContractLineBO::getSkuJson)
                .collect(Collectors.toMap(SkuBO::getSkuCode, sku -> MoreObjects.firstNonNull(sku.getNewAttributes(), Collections.emptyList())));*/
        Map<String, List<AttributeTO>> attributeListMap = lineList.stream()
                .map(ContractLineBO::getSkuJson)
                .collect(Collectors.groupingBy(
                        SkuBO::getSkuCode,
                        Collectors.mapping(
                                sku -> MoreObjects.firstNonNull(sku.getNewAttributes(), Collections.emptyList()),
                                Collectors.reducing(
                                        Collections.emptyList(),
                                        (list1, list2) -> {
                                            List<AttributeTO> combined = new ArrayList<>(list1);
                                            combined.addAll(list2);
                                            return combined;
                                        }
                                )
                        )
                ));
        ContractExportHelper helper = new ContractExportHelper(attributeListMap, AttributeConfirmStageDict.TRADE, properties);

        List<String> head = new ArrayList<>();
        head.add("商品名称");
        head.add("商品编码");
        head.add("标品编码");
        head.add("属性");
        head.addAll(helper.getAttributeHeadList());
        head.add("含税单价（元）");
        head.add("税率（%）");
        head.add("本次下单数量");
        head.add("开始租期（格式：xxxx/xx/xx）");
        head.add("结束租期（格式：xxxx/xx/xx）");
        head.add("签约数量");
        head.add("已下单/租赁中数量");
        head.add("单位");
        head.add("小计");
        head.add("备注");

        // 租赁类
        List<String> leaseTypes = ImmutableList.of(ItemTypeNewDict.EQUIPMENT_LEASE, ItemTypeNewDict.MATERIAL_LEASE);
        // Excel数据
        List<List<String>> dataList = new ArrayList<>();
        // 计算公式
        List<CellWriteHandler> formulaHandlerList = new ArrayList<>();

        CollUtil.forEach(lineList, (line, idx) -> {
            List<String> data = new ArrayList<>();
            data.add(line.getSku().getName());
            data.add(line.getSku().getSkuCode());
            data.add(line.getSpu().getSpuCode());
            data.add(getAttributeStr(line.getSkuJson().getNewAttributes()));
            data.addAll(helper.getAttributeValueList(line.getSku().getSkuCode()));
            data.add(Optional.ofNullable(line.getPrcWithTax()).map(Currency::getValue).map(BigDecimal::toPlainString).orElse("0"));
            data.add(Optional.ofNullable(line.getTaxRate()).map(BigDecimal::toPlainString).orElse("0") + "%");
            data.add("0");
            data.add(leaseTypes.contains(line.getSku().getItemType()) ? null : "-");
            data.add(leaseTypes.contains(line.getSku().getItemType()) ? null : "-");
            data.add(Optional.ofNullable(line.getSignedQty()).map(BigDecimal::toPlainString).orElse("0"));
            data.add(Optional.ofNullable(line.getPlacedQty()).map(BigDecimal::toPlainString).orElse("0"));
            data.add(Optional.ofNullable(line.getSkuJson().getStockUnit()).map(UnitBO::getUnitName).orElse("-"));
            data.add("0");
            data.add("");
            dataList.add(data);

            String quantity = "{本次下单数量}{row}";
            String price = "{含税单价（元）}{row}";
            String startDate = "{开始租期（格式：xxxx/xx/xx）}{row}";
            String endDate = "{结束租期（格式：xxxx/xx/xx）}{row}";
            String formula = leaseTypes.contains(line.getSku().getItemType())
                    ? StrUtil.format("IF(OR(ISBLANK({}),ISBLANK({})),0,{}*{}*({}-{}+1))", startDate, endDate, quantity, price, endDate, startDate)
                    : StrUtil.format("{}*{}", quantity, price);
            Map<String, String> formulas = ImmutableMap.of("小计", formula);
            formulaHandlerList.add(helper.createFormulaHandler(head, formulas, idx + 1, idx + 1));
        });

        // 下拉框
        SheetWriteHandler validationHandler = helper.createValidationHandler(head, 1, dataList.size());

        List<String> editableColumnList = new ArrayList<>();
        editableColumnList.add("本次下单数量");
        editableColumnList.add("开始租期（格式：xxxx/xx/xx）");
        editableColumnList.add("结束租期（格式：xxxx/xx/xx）");
        editableColumnList.add("备注");
        editableColumnList.addAll(helper.getAttributeHeadList());
        CellWriteHandler columnStyleHandler = helper.createColumnStyleHandler(editableColumnList);

        String fileName = StrUtil.format("trantor/attachments/合同清单模板-{}.xlsx", DatePattern.PURE_DATETIME_FORMAT.format(new Date()));
        String sheetName = "合同清单";

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriterBuilder builder = EasyExcel.write(outputStream);
        formulaHandlerList.forEach(builder::registerWriteHandler);
        builder.registerWriteHandler(validationHandler).registerWriteHandler(columnStyleHandler)
                .sheet(sheetName).head(LF.map(head, Collections::singletonList)).doWrite(dataList);

        ByteArrayInputStream inputStream = IoUtil.toStream(outputStream);
        ossClient.upload(fileName, inputStream);

        String url = ossClient.getUrlWithoutSignature(fileName);
        if (StrUtil.startWith(url, "http:")) {
            url = StrUtil.replace(url, "http:", "https:");
        }
        return new StringResult(url);
    }

    private List<ContractLineBO> getContractLineList(ContractLineImportRequestTO request) {
        QContractLineBO qContractLineBO = new QContractLineBO();
        qContractLineBO.getQueryParams().setPage(new Page(1, Integer.MAX_VALUE));
        EnhanceDS.addQueryField(qContractLineBO, "spu.*");

        QYzContractBO qYzContractBO = new QYzContractBO();
        qYzContractBO.setId(new QLongId(request.getContractId()));
        qContractLineBO.setContract(qYzContractBO);

        return pageContractLineFunc.execute(qContractLineBO).getData();
    }

    private String getAttributeStr(List<AttributeTO> attributes) {
        return CollUtil.join(attributes, " | ", e -> e.getAttributeName() + ":" + StrUtil.blankToDefault(e.getRealAttributeValueStr(), "无"));
    }
}
