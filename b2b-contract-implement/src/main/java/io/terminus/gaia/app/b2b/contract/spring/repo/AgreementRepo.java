package io.terminus.gaia.app.b2b.contract.spring.repo;

import cn.hutool.core.collection.CollUtil;
import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.gaia.app.b2b.contract.dict.YzContractTypeDict;
import io.terminus.gaia.app.b2b.contract.model.*;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantorframework.json.Json;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AgreementRepo {
    /**
     * 新建
     *
     * @param agreementBO
     * @return
     */
    @DSTransaction
    public AgreementBO createAgreement(AgreementBO agreementBO) {
        log.info("AgreementRepo createAgreement:{}", Json.toJson(agreementBO));
        DS.create(agreementBO);
        DS.create(agreementBO.getContacts());
        Map<Boolean, List<PaymentSchemeBO>> paymentSchemesMap = agreementBO.getPaymentSchemes().stream().collect(Collectors.partitioningBy(it -> Objects.isNull(it.getCode())));

        for (List<PaymentSchemeBO> value : paymentSchemesMap.values()) {
            if (CollUtil.isNotEmpty(value)) {
                DS.create(value);
            }
        }
        DS.create(agreementBO.getCoverAreas());
        DS.create(agreementBO.getCoverAreaLines());
        DS.create(agreementBO.getUsingDepartments());
        DS.create(agreementBO.getDealers());
        DS.create(agreementBO.getDealerLines());
        DS.create(agreementBO.getRegularReconciliations());

        if (Objects.nonNull(agreementBO.getAgreementBpmBO())){
            DS.create(agreementBO.getAgreementBpmBO());
        }
        return agreementBO;
    }

    /**
     * 更新协议
     *
     * @param agreementBO
     * @return
     */
    @DSTransaction
    public boolean updateAgreement(AgreementBO agreementBO) {
        log.info("AgreementRepo updateAgreement:{}", Json.toJson(agreementBO));
        DS.update(agreementBO);
        return true;
    }

    /**
     * 更新协议和关联模型
     *
     * @param agreementBO
     * @return
     */
    @DSTransaction
    public boolean updateAgreementAndRel(AgreementBO agreementBO) {
        log.info("AgreementRepo updateAgreementAndRel:{}", Json.toJson(agreementBO));

        DS.update(agreementBO);
        DS.update(agreementBO.getDetails());

        //历史数据特殊处理
        if (agreementBO.getAgreementBpmBO() != null) {
            if (agreementBO.getNeedCreateBpmLine()) {
                DS.create(agreementBO.getAgreementBpmBO());
            } else {
                DS.update(agreementBO.getAgreementBpmBO());
            }
        }

        AgreementBO baseAgreement = AgreementBO.of(agreementBO.getId());
        // 使用单位
        if (agreementBO.getUsingDepartments() != null) {
            DS.create(agreementBO.getUsingDepartments().stream().filter(e -> e.getId() == null)
                    .peek(e -> e.setAgreementBO(baseAgreement)).collect(Collectors.toList()));
            DS.update(agreementBO.getUsingDepartments().stream().filter(e -> e.getId() != null).collect(Collectors.toList()));
        }
        // 联系人
        if (agreementBO.getContacts() != null) {
            DS.create(agreementBO.getContacts().stream().filter(e -> e.getId() == null)
                    .peek(e -> e.setAgreementBO(baseAgreement)).collect(Collectors.toList()));
            DS.update(agreementBO.getContacts().stream().filter(e -> e.getId() != null).collect(Collectors.toList()));
        }
        // 覆盖范围
        if (agreementBO.getCoverAreas() != null) {
            DS.create(agreementBO.getCoverAreas().stream().filter(e -> e.getId() == null || Boolean.TRUE.equals(e.getNeedCreate()))
                    .peek(e -> e.setAgreementBO(baseAgreement)).collect(Collectors.toList()));
            DS.update(agreementBO.getCoverAreas().stream().filter(e -> e.getId() != null && !Boolean.TRUE.equals(e.getNeedCreate())).collect(Collectors.toList()));
        }
        // 覆盖范围行
        if (agreementBO.getCoverAreaLines() != null) {
            DS.create(agreementBO.getCoverAreaLines().stream().filter(e -> e.getId() == null)
                    .peek(e -> e.setAgreementBO(baseAgreement)).collect(Collectors.toList()));
            DS.update(agreementBO.getCoverAreaLines().stream().filter(e -> e.getId() != null).collect(Collectors.toList()));
        }
        // 经销商
        if (agreementBO.getDealers() != null) {
            DS.create(agreementBO.getDealers().stream().filter(e -> e.getId() == null || Boolean.TRUE.equals(e.getNeedCreate()))
                    .peek(e -> e.setAgreementBO(baseAgreement)).collect(Collectors.toList()));
            DS.update(agreementBO.getDealers().stream().filter(e -> e.getId() != null && !Boolean.TRUE.equals(e.getNeedCreate())).collect(Collectors.toList()));
        }
        // 经销商行
        if (agreementBO.getDealerLines() != null) {
            DS.create(agreementBO.getDealerLines().stream().filter(e -> e.getId() == null)
                    .peek(e -> e.setAgreementBO(baseAgreement)).collect(Collectors.toList()));
            DS.update(agreementBO.getDealerLines().stream().filter(e -> e.getId() != null).collect(Collectors.toList()));
        }
        // 付款方案
        if (agreementBO.getPaymentSchemes() != null) {

            Map<Boolean, List<PaymentSchemeBO>> listMap = agreementBO.getPaymentSchemes().stream().filter(e -> e.getId() == null)
                    .peek(e -> e.setAgreementBO(baseAgreement)).collect(Collectors.partitioningBy(it -> Objects.isNull(it.getCode())));

            // 将是否应用都置为false
            DS.update(PaymentSchemeBO.class, "apply = ?", "agreementBO=?", false, agreementBO.getId());

            for (List<PaymentSchemeBO> value : listMap.values()) {
                if (CollUtil.isNotEmpty(value)) {
                    DS.create(value);
                }
            }

            DS.update(agreementBO.getPaymentSchemes().stream().filter(e -> e.getId() != null).collect(Collectors.toList()));
        }
        // 定时账期设置
        if (agreementBO.getRegularReconciliations() != null) {
            DS.create(agreementBO.getRegularReconciliations().stream().filter(e -> e.getId() == null)
                    .peek(e -> e.setAgreementBO(baseAgreement)).collect(Collectors.toList()));
            DS.update(agreementBO.getRegularReconciliations().stream().filter(e -> e.getId() != null).collect(Collectors.toList()));

        }

        DS.delete(agreementBO.getDeleteContacts());
        DS.delete(agreementBO.getDeletePaymentSchemes());
        DS.delete(agreementBO.getDeleteCoverAreas());
        DS.delete(agreementBO.getDeleteCoverAreaLines());
        DS.delete(agreementBO.getDeleteDealers());
        DS.delete(agreementBO.getDeleteDealerLines());
        DS.delete(agreementBO.getDeleteUsingDepartments());
        DS.delete(agreementBO.getDeleteRegularReconciliations());

        // 删除价格方案和付款单关联关系
        List<PaymentSchemeBO> existPaymentSchemeBOList = DS.findAll(PaymentSchemeBO.class, "id", "agreementBO=?", agreementBO.getId());
        if (CollUtil.isNotEmpty(existPaymentSchemeBOList)) {
            Set<Long> existPaymentSchemeIdSet = existPaymentSchemeBOList.stream().map(PaymentSchemeBO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
            Query query = TSQL.delete(PaymentRelatePriceSchemeBO.class).where(TSQL.field(PaymentRelatePriceSchemeBO.agreementBO_field).eq(agreementBO.getId())).and(TSQL.field(PaymentRelatePriceSchemeBO.paymentScheme_field).notIn(existPaymentSchemeIdSet));
            DS.delete(query);
        }


        return true;
    }

    /**
     * 删除协议和关联模型
     *
     * @param agreementBO
     * @return
     */
    @DSTransaction
    public boolean deleteAgreementAndRel(AgreementBO agreementBO) {
        log.info("AgreementRepo deleteAgreementAndRel:{}", Json.toJson(agreementBO));
        DS.delete(agreementBO);
        DS.delete(agreementBO.getDeleteContacts());
        DS.delete(agreementBO.getDeleteCoverAreas());
        DS.delete(agreementBO.getDeleteCoverAreaLines());
        DS.delete(agreementBO.getDeletePaymentSchemes());
        DS.delete(agreementBO.getDeleteUsingDepartments());
        DS.delete(agreementBO.getDeleteDealers());
        DS.delete(agreementBO.getDeleteDealerLines());
        return true;
    }

    /**
     * 同步协议保存
     *
     * @param syncAgreement
     * @return
     */
    @DSTransaction
    public boolean saveSyncAgreement(AgreementBO syncAgreement) {
        log.info("AgreementRepo saveSyncAgreement:{}", Json.toJson(syncAgreement));
        Long id = null;
        if (syncAgreement.getId() == null) {
            id = DS.nextId(AgreementBO.class);
            syncAgreement.setId(id);
            // 新增的协议默认分供
            syncAgreement.setBizType(YzContractTypeDict.SUB_SUPPLY);
            DS.create(syncAgreement);
        } else {
            id = syncAgreement.getId();
            DS.update(syncAgreement);
        }
        // 关联模型 仅处理新增
        AgreementBO baseAgreement = AgreementBO.of(id);
        // 联系人
        if (CollectionUtils.isNotEmpty(syncAgreement.getContacts())) {
            for (AgreementContactBO contact : syncAgreement.getContacts()) {
                contact.setAgreementBO(baseAgreement);
            }
            DS.create(syncAgreement.getContacts());
        }
        // 覆盖范围
        if (CollectionUtils.isNotEmpty(syncAgreement.getCoverAreas())) {
            List<AgreementCoverAreaLineBO> coverAreaLines = new ArrayList<>();
            for (AgreementCoverAreaBO coverArea : syncAgreement.getCoverAreas()) {
                coverArea.setAgreementBO(baseAgreement);
                if (coverArea.getId() == null) {
                    coverArea.setId(DS.nextId(AgreementCoverAreaBO.class));
                }
                if (CollectionUtils.isNotEmpty(coverArea.getLines())) {
                    coverArea.getLines().forEach(line -> {
                        line.setCoverAreaBO(AgreementCoverAreaBO.of(coverArea.getId()));
                        line.setAgreementBO(coverArea.getAgreementBO());
                    });
                    coverAreaLines.addAll(coverArea.getLines());
                }
            }
            DS.create(syncAgreement.getCoverAreas());
            DS.create(coverAreaLines);
        }
        // 清单
        if (CollectionUtils.isNotEmpty(syncAgreement.getDetails())) {
            if (syncAgreement.getIsSupply() && Objects.nonNull(syncAgreement.getMainAgreement())) {
                for (AgreementDetailBO detail : syncAgreement.getDetails()) {
                    // 关联主协议
                    detail.setAgreementBO(syncAgreement.getMainAgreement());
                    // 保存补充协议关联关系
                    detail.setSupplementAgreementBO(syncAgreement);
                }
            } else {
                for (AgreementDetailBO detail : syncAgreement.getDetails()) {
                    detail.setAgreementBO(baseAgreement);
                }
            }
            DS.create(syncAgreement.getDetails());
        }
        // 使用单位
        if (CollectionUtils.isNotEmpty(syncAgreement.getUsingDepartments())) {
            for (AgreementUsingDepartmentBO usingDepartmentBO : syncAgreement.getUsingDepartments()) {
                usingDepartmentBO.setAgreementBO(baseAgreement);
            }
            DS.create(syncAgreement.getUsingDepartments().stream().filter(e -> e.getId() == null).collect(Collectors.toList()));
            DS.update(syncAgreement.getUsingDepartments().stream().filter(e -> e.getId() != null).collect(Collectors.toList()));
        }
        // 删除的使用单位
        if (CollectionUtils.isNotEmpty(syncAgreement.getDeleteUsingDepartments())) {
            DS.delete(syncAgreement.getDeleteUsingDepartments());
        }
        return true;
    }
}
