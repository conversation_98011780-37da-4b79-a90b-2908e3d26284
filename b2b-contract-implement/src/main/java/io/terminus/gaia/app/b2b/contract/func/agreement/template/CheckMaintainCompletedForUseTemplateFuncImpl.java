package io.terminus.gaia.app.b2b.contract.func.agreement.template;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.terminus.gaia.app.b2b.contract.dict.AgreementCoverAreaTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementDetailStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementTypeDict;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.model.PaymentRelatePriceSchemeBO;
import io.terminus.gaia.app.b2b.item.dict.category.LinkPriceSchemeDict;
import io.terminus.gaia.app.b2b.item.model.CategoryAgreementConfigBO;
import io.terminus.gaia.item.model.price.PriceSchemeBO;
import io.terminus.gaia.md.dict.SyncStatusDict;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/04
 */
@FunctionImpl
public class CheckMaintainCompletedForUseTemplateFuncImpl implements CheckMaintainCompletedForUseTemplateFunc{
    @Override
    public BooleanResult execute(List<AgreementBO> agreementBOList) {

        // 协议的分类配置map
        Map<Long, CategoryAgreementConfigBO> configMap = getCategoryAgreementConfigBOMap(agreementBOList);


        for (AgreementBO agreementBO : agreementBOList) {
            boolean basicInfoSatisfy = validateRequired(agreementBO);
            if (!basicInfoSatisfy) {
                AgreementBO agreementNew = new AgreementBO();
                agreementNew.setId(agreementBO.getId());
                agreementNew.setMaintainCompleted(false);
                DS.update(agreementNew);
            } else {
                // 基础信息&&价格方案配置
                validateConfig(configMap, agreementBO);
            }
        }

        return BooleanResult.TRUE;
    }

    private Map<Long, CategoryAgreementConfigBO> getCategoryAgreementConfigBOMap(List<AgreementBO> data) {
        List<Long> categoryIds = data.stream().map(AgreementBO::getCategory).filter(Objects::nonNull)
                .map(CategoryBO::getId).distinct().collect(Collectors.toList());

        List<CategoryAgreementConfigBO> agreementConfigBOS = DS.findAll(CategoryAgreementConfigBO.class,
                "*", "categoryBO in (?) ", categoryIds);

        Map<Long, CategoryAgreementConfigBO> configMap = agreementConfigBOS.stream()
                .collect(Collectors.toMap(configBO -> configBO.getCategoryBO().getId(), Function.identity(), (v1, v2) -> v2));

        return configMap;
    }

    private void validateConfig(Map<Long, CategoryAgreementConfigBO> configMap, AgreementBO agreement) {

        CategoryAgreementConfigBO config = configMap.get(agreement.getCategory().getId());
        String isLinkPriceScheme = Optional.ofNullable(config).map(CategoryAgreementConfigBO::getIsLinkPriceScheme).orElse(LinkPriceSchemeDict.NO);

        AgreementBO agreementNew = new AgreementBO();
        agreementNew.setId(agreement.getId());
        if (!isLinkPriceScheme.equals(LinkPriceSchemeDict.YES)) {
            agreementNew.setMaintainCompleted(true);
            agreementNew.setStatus(AgreementStatusDict.ENABLED);
            agreementNew.setSyncStatus(SyncStatusDict.SUCCESS);
        } else {
            boolean complete = true;
            List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class, "*", "agreementBO = ? and statusDict = ?",
                    agreement.getId(), AgreementDetailStatusDict.START_USING);

            if (Objects.equals(agreement.getType(), AgreementTypeDict.MATCH_MAKING)) {
                List<PaymentRelatePriceSchemeBO> paymentRelatePriceSchemeBOS = DS.findAll(PaymentRelatePriceSchemeBO.class, "*", "agreementBO=?", agreement.getId());
                Map<Long, List<PaymentRelatePriceSchemeBO>> paymentRelatePriceSchemeGroup = paymentRelatePriceSchemeBOS.stream().collect(Collectors.groupingBy(it -> it.getAgreementDetail().getId()));

                complete = details.stream().allMatch(it -> Objects.nonNull(it.getPriceSchemeBO()) || paymentRelatePriceSchemeGroup.containsKey(it.getId()));
            } else {
                complete = details.stream().allMatch(it -> Objects.nonNull(it.getPriceSchemeBO()));
            }

            agreementNew.setMaintainCompleted(complete);
            agreementNew.setStatus(AgreementStatusDict.ENABLED);
            agreementNew.setSyncStatus(SyncStatusDict.SUCCESS);
        }

        DS.update(agreementNew);
    }


    private boolean validateRequired(AgreementBO agreement) {

        boolean a = StrUtil.isNotBlank(agreement.getName());

        boolean b = agreement.getJfCompanyBO() != null;

        boolean c = agreement.getYfCompanyBO() != null;

        boolean d = CollUtil.isNotEmpty(agreement.getDepartments());

        boolean e = agreement.getCategory() != null;


        boolean f = agreement.getBizType() != null;

        boolean g = false;
        if (agreement.getCoverAreaType() != null) {
            if (agreement.getCoverAreaType().equals(AgreementCoverAreaTypeDict.ALL)) {
                g = true;
            } else {
                g =  CollUtil.isNotEmpty(agreement.getCoverAreas());
            }
        }

        boolean h = CollUtil.isNotEmpty(agreement.getPaymentSchemes());

        boolean i = agreement.getEffectiveAt() != null;

        boolean j = agreement.getExpireAt() != null;

        if (a && b && c && d && e && f && g && h && i && j) {
            return true;
        } else {
            return false;
        }

    }
}
