package io.terminus.gaia.app.b2b.contract.spring.fill;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import io.terminus.gaia.app.b2b.contract.dict.*;
import io.terminus.gaia.app.b2b.contract.model.*;
import io.terminus.gaia.app.b2b.contract.util.B2bContractCodeGenerator;
import io.terminus.gaia.app.b2b.contract.util.DistrictUtil;
import io.terminus.gaia.contract.dict.common.ApproveStatusDict;
import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.context.UserCompanyContext;
import io.terminus.gaia.organization.context.UserInfoContext;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AgreementFiller {

    private final UserInfoContext userInfoContext;
    private final UserCompanyContext userCompanyContext;

    /**
     * 协议创建
     *
     * @param agreementBO
     */
    public void fillCreate(AgreementBO agreementBO) {
        agreementBO.setId(DS.nextId(AgreementBO.class));
        agreementBO.setCode(DS.nextCode(AgreementBO.class, "code"));
        agreementBO.setStatus(AgreementStatusDict.DRAFT);
        agreementBO.setApproveStatusDict(ApproveStatusDict.APPROVED_PENDING);
        agreementBO.setSourceDict(AgreementSourceDict.MANUAL);
        agreementBO.setIsSupply(ObjectUtil.defaultIfNull(agreementBO.getIsSupply(), false));
        agreementBO.setIsSubCompany(ObjectUtil.defaultIfNull(agreementBO.getIsSubCompany(), false));
        if (Objects.nonNull(userCompanyContext.getCompanyInfo().getDepartmentBO())) {
            DepartmentBO departmentBO = DS.findById(DepartmentBO.class, userCompanyContext.getCompanyInfo().getDepartmentBO().getId());
            agreementBO.setOptDepartments(ListUtil.toList(departmentBO));
        }
        fillSave(agreementBO, null);
    }

    /**
     * 补充协议创建
     *
     * @param supplyAgreement
     */
    public void fillSupplyCreate(AgreementBO supplyAgreement, AgreementBO mainAgreement) {
        supplyAgreement.setId(DS.nextId(AgreementBO.class));
        // 编码表自增+1
        B2bContractCodeGenerator.increaseAgreementName(mainAgreement, AgreementNameIncreaseType.SUPPLY);
        supplyAgreement.setStatus(AgreementStatusDict.DRAFT);
        supplyAgreement.setSourceDict(AgreementSourceDict.MANUAL);
        supplyAgreement.setIsSupply(true);
        supplyAgreement.setIsSubCompany(false);

        fillSave(supplyAgreement, null);
    }

    /**
     * 分子公司协议创建
     *
     * @param subCompanyAgreement
     */
    public void fillSubCompanyCreate(AgreementBO subCompanyAgreement, AgreementBO relateAgreement) {
        subCompanyAgreement.setId(DS.nextId(AgreementBO.class));
        // 编码表自增+1
        B2bContractCodeGenerator.increaseAgreementName(relateAgreement, AgreementNameIncreaseType.SUB_COMPANY);
        subCompanyAgreement.setStatus(AgreementStatusDict.DRAFT);
        subCompanyAgreement.setSourceDict(AgreementSourceDict.MANUAL);
        subCompanyAgreement.setIsSupply(false);
        subCompanyAgreement.setIsSubCompany(true);
        subCompanyAgreement.setCanRelateDetail(false);

        fillSave(subCompanyAgreement, null);
    }

    /**
     * 编辑
     *
     * @param agreementBO
     */
    public void fillEdit(AgreementBO agreementBO, AgreementBO existAgreement) {
        fillSave(agreementBO, existAgreement);
    }

    /**
     * 维护协议信息
     *
     * @param agreementBO
     */
    public void fillEditExtendInfo(AgreementBO agreementBO, AgreementBO existAgreement) {
        fillSave(agreementBO, existAgreement);
    }


    /**
     * 创建/编辑
     * 注：各个关联模型必须全部传入，空则删除；如果只编辑部分模型，不可使用该方法
     *
     * @param agreementBO
     */
    private void fillSave(AgreementBO agreementBO, AgreementBO existAgreement) {
        if (agreementBO.getId() == null) {
            agreementBO.setId(DS.nextId(AgreementBO.class));
        }
        // 使用单位
        fillUsingDepartments(agreementBO, existAgreement);
        // 联系人
        if (CollectionUtils.isNotEmpty(agreementBO.getContacts())) {
            agreementBO.getContacts().forEach(contact -> {
                contact.setAgreementBO(AgreementBO.of(agreementBO.getId()));
                contact.setDistrictStr(DistrictUtil.buildDistrictStr(contact.getDistricts()));
            });
        } else {
            if (existAgreement != null) {
                agreementBO.setDeleteContacts(DS.findAll(AgreementContactBO.class, "id", "agreementBO = ?", agreementBO.getId()));
            }
        }
        // 覆盖范围
        fillCoverArea(agreementBO, existAgreement);
        // 审批表单-补充信息
        this.fillAgreementBpmBO(agreementBO);
        // 经销商
        fillDealer(agreementBO, existAgreement);
        // 定时对账日期
        fillRegularReconciliations(agreementBO);
        // 付款方案
        if (CollectionUtils.isNotEmpty(agreementBO.getPaymentSchemes())) {
            agreementBO.getPaymentSchemes().forEach(scheme -> {
                scheme.setAgreementBO(AgreementBO.of(agreementBO.getId()));
            });
        } else {
            if (existAgreement != null) {
                agreementBO.setDeletePaymentSchemes(DS.findAll(PaymentSchemeBO.class, "id", "agreementBO = ?", agreementBO.getId()));
            }
        }
        // expire时间即为23:59:59
        if (agreementBO.getExpireAt() != null) {
            agreementBO.setExpireAt(DateUtil.endOfDay(agreementBO.getExpireAt()));
        }
        // 经办人员工信息 上下文获取
        if (agreementBO.getOperator() == null) {
            agreementBO.setOperator(userInfoContext.getUserInfo().getEmployee());
        }

        if (existAgreement == null) {
            agreementBO.setCanRelateDetail(ObjectUtil.defaultIfNull(agreementBO.getCanRelateDetail(), false));
            agreementBO.setIsCenterPay(ObjectUtil.defaultIfNull(agreementBO.getIsCenterPay(), false));
            agreementBO.setIsSupply(ObjectUtil.defaultIfNull(agreementBO.getIsSupply(), false));
            agreementBO.setIsSubCompany(ObjectUtil.defaultIfNull(agreementBO.getIsSubCompany(), false));
        }

        if (!agreementBO.getBizType().equals(YzContractTypeDict.RENT)) {
            agreementBO.setRentPeriod(null);
        }

        if (CollUtil.isNotEmpty(agreementBO.getPayableSchemeList())) {
            agreementBO.getPayableSchemeList().forEach(it -> {
                String type = agreementBO.getType();

                if (Objects.equals(type, AgreementTypeDict.VIRTUAL)) {
                    it.setType(PayableSchemeTypeDict.RECEIVE);
                } else if (Objects.equals(type, AgreementTypeDict.SELF)) {
                    it.setType(PayableSchemeTypeDict.PAY);
                }
            });
        }
    }

    /**
     *
     * （1）创建场景：需要设置id，需要创建bpm行
     * （2）更新场景（历史数据）：没有id，需要设置id，需要创建bpm行
     * （3）更新场景新数据：有id，只需要更新bpm行
     * （4）非自营场景：不用审批，不创建也不更新
     * */
    private void fillAgreementBpmBO(AgreementBO agreementBO) {
        //一、非自营场景：不用审批，不创建也不更新bpm表
        if (agreementBO.getAgreementBpmBO() == null) {
            agreementBO.setNeedCreateBpmLine(false);//执行时自己判断下agreementBpmBO为空吧
            return;
        }
        //二、更新场景（历史数据）：没有id，需要设置id，需要创建bpm行
        if (agreementBO.getAgreementBpmBO().getId() == null) {
            agreementBO.getAgreementBpmBO().setId(DS.nextId(AgreementBpmBO.class));
            agreementBO.setNeedCreateBpmLine(true);
            return;
        }
        //三、更新场景（新数据）：有id，不一定有bpm行
        AgreementBpmBO agreementBpmBO = DS.findById(AgreementBpmBO.class, agreementBO.getAgreementBpmBO().getId());
        //(3.1)、有id，但没有bpm行，则需要创建bpm行
        if (agreementBpmBO == null) {
            agreementBO.setNeedCreateBpmLine(true);
            return;
        }
        //(3.2)、有id，有bpm行，只需要更新bpm行
        agreementBO.setNeedCreateBpmLine(false);
    }

    /**
     * 定时生成对账日期
     *
     * @param agreementBO
     */
    public void fillRegularReconciliations(AgreementBO agreementBO) {
        if (CollUtil.isNotEmpty(agreementBO.getRegularReconciliations())) {
            agreementBO.getRegularReconciliations().forEach(agreementRegularReconciliationBO -> {
                agreementRegularReconciliationBO.setAgreementBO(agreementBO);
            });

        }
    }

    /**
     * 使用单位
     *
     * @param agreementBO
     * @param existAgreement
     */
    public void fillUsingDepartments(AgreementBO agreementBO, AgreementBO existAgreement) {
        List<AgreementUsingDepartmentBO> existDepts = existAgreement == null ? new ArrayList<>() :
                DS.findAll(AgreementUsingDepartmentBO.class, "*", "agreementBO = ?", agreementBO.getId());
        List<DepartmentBO> requestDepts = CollUtil.defaultIfEmpty(agreementBO.getDepartments(), new ArrayList<>());
        List<AgreementUsingDepartmentBO> newUsingDepts = requestDepts.stream()
                .map(requestDept -> {
                    return existDepts.stream().filter(e -> ObjectUtil.equal(e.getDepartmentBO().getId(), requestDept.getId())).findFirst()
                            .orElseGet(() -> AgreementUsingDepartmentBO.of(AgreementBO.of(agreementBO.getId()), requestDept));
                }).collect(Collectors.toList());
        agreementBO.setUsingDepartments(newUsingDepts);
        // 删除的使用单位
        List<AgreementUsingDepartmentBO> deleteDepts = existDepts.stream()
                .filter(existDept -> requestDepts.stream().noneMatch(e -> e.getId().equals(existDept.getDepartmentBO().getId()))).collect(Collectors.toList());
        agreementBO.setDeleteUsingDepartments(deleteDepts);
        agreementBO.setDepartmentStr(requestDepts.stream().map(DepartmentBO::getDepartmentName).collect(Collectors.joining("、")));
    }

    /**
     * 覆盖范围
     *
     * @param agreementBO
     * @param existAgreement
     */
    public void fillCoverArea(AgreementBO agreementBO, AgreementBO existAgreement) {
        if (CollectionUtils.isNotEmpty(agreementBO.getCoverAreas())) {
            List<AgreementCoverAreaLineBO> coverAreaLines = new ArrayList<>();
            List<AgreementCoverAreaLineBO> deleteAreaLines = new ArrayList<>();
            agreementBO.getCoverAreas().forEach(coverArea -> {
                if (coverArea.getId() == null) {
                    coverArea.setId(DS.nextId(AgreementCoverAreaBO.class));
                    coverArea.setNeedCreate(true);
                }
                coverArea.setAgreementBO(AgreementBO.of(agreementBO.getId()));
                List<AgreementCoverAreaLineBO> existLines = existAgreement == null ? new ArrayList<>() :
                        DS.findAll(AgreementCoverAreaLineBO.class, "*", "agreementBO = ?", agreementBO.getId());
                List<DistrictBO> requestDistricts = CollUtil.defaultIfEmpty(coverArea.getDistricts(), new ArrayList<>());
                coverArea.setDistrictStr(DistrictUtil.buildDistrictStr(requestDistricts));
                List<AgreementCoverAreaLineBO> newCoverLines = requestDistricts.stream().map(requestDistrict -> {
                    return existLines.stream().filter(existLine ->
                                    ObjectUtil.equal(existLine.getDistrict().getId(), requestDistrict.getId())).findFirst()
                            .orElseGet(() -> AgreementCoverAreaLineBO.of(agreementBO, coverArea, coverArea.getRegion(), requestDistrict));
                }).collect(Collectors.toList());
                coverAreaLines.addAll(newCoverLines);
                deleteAreaLines.addAll(existLines.stream().filter(existLine ->
                                requestDistricts.stream().noneMatch(e -> e.getId().equals(existLine.getDistrict().getId())))
                        .collect(Collectors.toList()));
            });
            agreementBO.setCoverAreaLines(coverAreaLines);
            agreementBO.setDeleteCoverAreaLines(deleteAreaLines);
        } else {
            if (existAgreement != null) {
                agreementBO.setDeleteCoverAreas(DS.findAll(AgreementCoverAreaBO.class, "id", "agreementBO = ?", agreementBO.getId()));
                agreementBO.setDeleteCoverAreaLines(DS.findAll(AgreementCoverAreaLineBO.class, "id", "agreementBO = ?", agreementBO.getId()));
            }
        }
    }

    /**
     * 经销商
     *
     * @param agreementBO
     * @param existAgreement
     */
    public void fillDealer(AgreementBO agreementBO, AgreementBO existAgreement) {
        if (CollectionUtils.isNotEmpty(agreementBO.getDealers())) {
            List<AgreementDealerLineBO> dealerLines = new ArrayList<>();
            List<AgreementDealerLineBO> deleteDealerLines = new ArrayList<>();
            agreementBO.getDealers().forEach(dealer -> {
                if (dealer.getId() == null) {
                    dealer.setId(DS.nextId(AgreementDealerBO.class));
                    dealer.setNeedCreate(true);
                }
                dealer.setAgreementBO(AgreementBO.of(agreementBO.getId()));
                dealer.setDealerName(dealer.getDealer().getEntityName());
                List<AgreementDealerLineBO> existLines = existAgreement == null ? new ArrayList<>() :
                        DS.findAll(AgreementDealerLineBO.class, "*", "agreementBO = ?", agreementBO.getId());
                List<DistrictBO> requestDistricts = CollUtil.defaultIfEmpty(dealer.getDistricts(), new ArrayList<>());
                dealer.setDistrictStr(DistrictUtil.buildDistrictStr(requestDistricts));
                List<AgreementDealerLineBO> newCoverLines = requestDistricts.stream().map(requestDistrict -> {
                    return existLines.stream().filter(existLine ->
                                    ObjectUtil.equal(existLine.getDistrict().getId(), requestDistrict.getId())).findFirst()
                            .orElseGet(() -> AgreementDealerLineBO.of(agreementBO, dealer, dealer.getRegion(), requestDistrict));
                }).collect(Collectors.toList());
                dealerLines.addAll(newCoverLines);
                deleteDealerLines.addAll(existLines.stream().filter(existLine ->
                                requestDistricts.stream().noneMatch(e -> e.getId().equals(existLine.getDistrict().getId())))
                        .collect(Collectors.toList()));
            });
            agreementBO.setDealerLines(dealerLines);
            agreementBO.setDeleteDealerLines(deleteDealerLines);
        } else {
            if (existAgreement != null) {
                agreementBO.setDeleteDealers(DS.findAll(AgreementDealerBO.class, "id", "agreementBO = ?", agreementBO.getId()));
                agreementBO.setDeleteDealerLines(DS.findAll(AgreementDealerLineBO.class, "id", "agreementBO = ?", agreementBO.getId()));
            }
        }
        agreementBO.setDealerStr(CollectionUtils.isEmpty(agreementBO.getDealers()) ? "" :
                agreementBO.getDealers().stream().map(AgreementDealerBO::getDealer)
                        .filter(Objects::nonNull).map(EntityBO::getEntityName).distinct().collect(Collectors.joining("、")));
    }


    /**
     * 协议模版创建
     */
    public void fillTemplateCreate(AgreementTemplateBO agreementTemplateBO) {
        AgreementBO agreementBO = agreementTemplateBO.getAgreementBO();
        agreementTemplateBO.setAttachment(agreementBO.getAttachment());
        agreementTemplateBO.setBizType(agreementBO.getBizType());
        agreementTemplateBO.setCategory(agreementBO.getCategory());
        agreementTemplateBO.setCoverAreaType(agreementBO.getCoverAreaType());
        agreementTemplateBO.setJfCompanyBO(agreementBO.getJfCompanyBO());
        if (agreementBO.getBizType().equals(YzContractTypeDict.RENT)) {
            agreementTemplateBO.setRentPeriod(agreementBO.getRentPeriod());
        }

        agreementTemplateBO.setEffectiveAt(agreementBO.getEffectiveAt());
        agreementTemplateBO.setSignAt(agreementBO.getSignAt());
        agreementTemplateBO.setExpireAt(agreementBO.getExpireAt());
        agreementTemplateBO.setExpireAt(agreementBO.getExpireAt());
        agreementTemplateBO.setIsCenterPay(agreementBO.getIsCenterPay());
        agreementTemplateBO.setProjectTypes(agreementBO.getProjectTypes());
        agreementTemplateBO.setDepartments(agreementBO.getDepartments());
        agreementTemplateBO.setCanRelateDetail(agreementBO.getCanRelateDetail());

        if (agreementBO.getCoverAreaType().equals(AgreementCoverAreaTypeDict.APPOINT)) {
            List<AgreementTemplateCoverAreaBO> templateCoverAreas = new ArrayList<>();
            List<AgreementCoverAreaBO> coverAreas = agreementBO.getCoverAreas();
            for (AgreementCoverAreaBO coverArea : coverAreas) {
                AgreementTemplateCoverAreaBO tem = new AgreementTemplateCoverAreaBO();
                BeanUtils.copyProperties(coverArea, tem);
                tem.setId(null);
                templateCoverAreas.add(tem);
            }
            agreementTemplateBO.setCoverAreas(templateCoverAreas);
        }
    }
}
