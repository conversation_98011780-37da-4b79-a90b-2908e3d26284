package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import cn.hutool.core.lang.Assert;
import io.terminus.gaia.app.b2b.contract.dict.AskPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AskSupplierPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * 供应商确认询价功能实现
 *
 * <AUTHOR>
 * @time 2025/7/9 09:42
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class SupplierConfirmAskPriceFuncImpl implements SupplierConfirmAskPriceFunc {

    @Override
    public BooleanResult execute(AskSupplierPriceBO req) {
        log.info("供应商确认询价，入参:{}", req);

        // 1. 参数校验
        validate(req);

        AskSupplierPriceBO askSupplierPriceBO = new AskSupplierPriceBO();
        askSupplierPriceBO.setId(req.getId());
        askSupplierPriceBO.setStatus(AskSupplierPriceStatusDict.DONE);
        DS.update(askSupplierPriceBO);

        // 判断是否全部完成
        judgeAllConfirm(req);

        return BooleanResult.TRUE;
    }

    private void judgeAllConfirm(AskSupplierPriceBO req) {
        AskSupplierPriceBO supplierPriceBO = DS.findById(AskSupplierPriceBO.class, req.getId());
        AskPriceBO askPriceBO = supplierPriceBO.getAskPriceBO();

        List<AskSupplierPriceBO> all = DS.findAll(AskSupplierPriceBO.class, "*", "askPriceBO=?", askPriceBO.getId());
        boolean allDone = all.stream().anyMatch(it -> Objects.equals(it.getStatus(), AskSupplierPriceStatusDict.DONE));
        if (!allDone) {
            return;
        }

        // 全都报价完成，更新为已确认
        AskPriceBO update = new AskPriceBO();
        update.setId(askPriceBO.getId());
        update.setStatus(AskPriceStatusDict.PRICE_WAITING_CONFIRM);
        DS.update(update);
    }

    private void validate(AskSupplierPriceBO req) {
        Assert.notNull(req.getId(), ExceptionUtil.create("询价单id不能为空"));
    }

}
