package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceImportTO;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceLineImportTO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导入询价清单实现
 *
 * <AUTHOR>
 * @time 2025/7/9 17:00
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class ImportAskSupplierPriceFuncImpl implements ImportAskSupplierPriceFunc {

    @Override
    @DSTransaction
    public BooleanResult execute(AskSupplierPriceImportTO importTO) {
        log.info("开始导入询价清单，参数: {}", JSON.toJSONString(importTO));
        
        validate(importTO);
        
        // 查询供应商报价单信息
        AskSupplierPriceBO supplierPriceBO = DS.findById(AskSupplierPriceBO.class, importTO.getAskSupplierPriceId());
        Assert.notNull(supplierPriceBO, ExceptionUtil.create("供应商报价单不存在"));
        
        // 下载并解析Excel文件
        List<AskSupplierPriceLineImportTO> importData = downloadAndParseExcel(importTO.getFileUrl());
        
        // 校验导入数据
        validateImportData(importData);
        
        // 查询现有的供应商报价单行数据
        List<AskSupplierPriceLineBO> existingLines = DS.findAll(AskSupplierPriceLineBO.class, 
                "*, askPriceLineBO.*", 
                "askSupplierPriceBO=?", supplierPriceBO.getId());
        
        // 更新含税单价
        updateTaxInclusiveUnitPrice(importData, existingLines);
        
        log.info("询价清单导入完成，供应商报价单ID: {}", importTO.getAskSupplierPriceId());
        return BooleanResult.TRUE;
    }

    private void validate(AskSupplierPriceImportTO importTO) {
        Assert.notNull(importTO, ExceptionUtil.create("导入参数不能为空"));
        Assert.notNull(importTO.getAskSupplierPriceId(), ExceptionUtil.create("供应商报价单ID不能为空"));
        Assert.notBlank(importTO.getFileUrl(), ExceptionUtil.create("文件URL不能为空"));
    }

    /**
     * 下载并解析Excel文件
     */
    private List<AskSupplierPriceLineImportTO> downloadAndParseExcel(String fileUrl) {
        try {
            log.info("开始下载文件: {}", fileUrl);


            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            HttpUtil.download(fileUrl,outputStream,true);
            URL url = new URL(downloadUrl);
            InputStream inputStream = url.openStream();
            
            // 解析Excel文件
            List<AskSupplierPriceLineImportTO> importData = EasyExcel.read(inputStream)
                    .head(AskSupplierPriceLineImportTO.class)
                    .sheet()
                    .doReadSync();
            
            log.info("解析Excel文件完成，共{}条数据", importData.size());
            return importData;
            
        } catch (IOException e) {
            log.error("下载或解析Excel文件失败: {}", fileUrl, e);
            throw new BusinessException("下载或解析Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 校验导入数据
     */
    private void validateImportData(List<AskSupplierPriceLineImportTO> importData) {
        if (CollectionUtils.isEmpty(importData)) {
            throw new BusinessException("导入文件为空，请检查文件内容");
        }
        
        List<String> errors = new ArrayList<>();
        
        for (int i = 0; i < importData.size(); i++) {
            AskSupplierPriceLineImportTO item = importData.get(i);
            int rowNum = i + 2; // Excel行号从2开始（第1行是表头）
            
            // 校验含税单价必填
            if (item.getTaxInclusiveUnitPrice() == null) {
                errors.add(String.format("第%d行：含税单价不能为空", rowNum));
                continue;
            }
            
            // 校验含税单价大于等于0
            if (item.getTaxInclusiveUnitPrice().compareTo(BigDecimal.ZERO) < 0) {
                errors.add(String.format("第%d行：含税单价必须大于等于0", rowNum));
            }
            
            // 保留两位小数
            item.setTaxInclusiveUnitPrice(item.getTaxInclusiveUnitPrice().setScale(2, RoundingMode.HALF_UP));
        }
        
        if (!errors.isEmpty()) {
            throw new BusinessException("数据校验失败：\n" + String.join("\n", errors));
        }
    }

    /**
     * 更新含税单价
     */
    private void updateTaxInclusiveUnitPrice(List<AskSupplierPriceLineImportTO> importData, 
                                           List<AskSupplierPriceLineBO> existingLines) {
        
        // 创建映射关系：物料名称+规格+品牌 -> 现有数据行
        Map<String, AskSupplierPriceLineBO> existingLineMap = new HashMap<>();
        for (AskSupplierPriceLineBO line : existingLines) {
            String key = buildLineKey(line);
            if (StrUtil.isNotBlank(key)) {
                existingLineMap.put(key, line);
            }
        }
        
        List<String> updateErrors = new ArrayList<>();
        int updatedCount = 0;
        
        for (int i = 0; i < importData.size(); i++) {
            AskSupplierPriceLineImportTO importItem = importData.get(i);
            int rowNum = i + 2;
            
            String key = buildImportKey(importItem);
            if (StrUtil.isBlank(key)) {
                updateErrors.add(String.format("第%d行：物料名称、规格或品牌信息不完整，无法匹配", rowNum));
                continue;
            }
            
            AskSupplierPriceLineBO existingLine = existingLineMap.get(key);
            if (existingLine == null) {
                updateErrors.add(String.format("第%d行：未找到匹配的询价单行数据", rowNum));
                continue;
            }
            
            // 更新含税单价
            AskSupplierPriceLineBO updateLine = new AskSupplierPriceLineBO();
            updateLine.setId(existingLine.getId());
            updateLine.setPurTaxPrice(importItem.getTaxInclusiveUnitPrice());
            
            DS.update(updateLine);
            updatedCount++;
            
            log.debug("更新询价单行[{}]含税单价为: {}", existingLine.getId(), importItem.getTaxInclusiveUnitPrice());
        }
        
        if (!updateErrors.isEmpty()) {
            log.warn("部分数据更新失败：\n{}", String.join("\n", updateErrors));
            throw new BusinessException("部分数据更新失败：\n" + String.join("\n", updateErrors));
        }
        
        log.info("成功更新{}条询价单行数据", updatedCount);
    }

    /**
     * 构建现有数据行的键
     */
    private String buildLineKey(AskSupplierPriceLineBO line) {
        if (line.getAskPriceLineBO() == null) {
            return null;
        }
        
        String materialName = line.getAskPriceLineBO().getMaterialName();
        String specification = line.getAskPriceLineBO().getThingSizeDesc();
        String brandName = "";
        
        if (line.getRequestPurchaseLineBO() != null && 
            line.getRequestPurchaseLineBO().getBrandBO() != null) {
            brandName = line.getRequestPurchaseLineBO().getBrandBO().getBrandName();
        }
        
        if (StrUtil.isBlank(materialName) || StrUtil.isBlank(specification)) {
            return null;
        }
        
        return materialName + "|" + specification + "|" + StrUtil.nullToEmpty(brandName);
    }

    /**
     * 构建导入数据的键
     */
    private String buildImportKey(AskSupplierPriceLineImportTO importItem) {
        String materialName = importItem.getMaterialName();
        String specification = importItem.getSpecification();
        String brandName = StrUtil.nullToEmpty(importItem.getBrandName());
        
        if (StrUtil.isBlank(materialName) || StrUtil.isBlank(specification)) {
            return null;
        }
        
        return materialName + "|" + specification + "|" + brandName;
    }
}
