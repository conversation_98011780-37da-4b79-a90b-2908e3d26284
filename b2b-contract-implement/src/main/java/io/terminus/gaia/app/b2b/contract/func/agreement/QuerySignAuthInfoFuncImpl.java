package io.terminus.gaia.app.b2b.contract.func.agreement;

import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.gaia.app.b2b.contract.dict.AccreditTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementTypeDict;
import io.terminus.gaia.app.b2b.contract.func.agreement.read.QueryAgreementInfoFunc;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementContactBO;
import io.terminus.gaia.app.b2b.contract.model.SignatureTaskBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementSigntureTaskTO;
import io.terminus.gaia.app.b2b.trade.dict.BusinessTypeDict;

import io.terminus.gaia.app.b2b.trade.model.fulfillment.DeliveryOrderV2BO;
import io.terminus.gaia.common.utils.signature.FadadaSignatureUtil;
import io.terminus.gaia.md.dict.signature.SignatureAuthTypeDict;
import io.terminus.gaia.md.model.signature.SignatureAuthInfoBO;
import io.terminus.gaia.organization.func.employee.read.PageAllEmployeeFunc;
import io.terminus.gaia.organization.model.EmployeeBO;
import io.terminus.gaia.organization.model.query.QEmployeeBO;
import io.terminus.gaia.organization.setting.SunacSetting;
import io.terminus.gaia.trade.setting.TradeGlobalSetting;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.querymodel.type.support.QLong;
import io.terminus.trantorframework.querymodel.type.support.QLongId;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 获取用户信息
 * <AUTHOR>
 * @date 2023/11/24
 * @description
 */
@RequiredArgsConstructor
@FunctionImpl
public class QuerySignAuthInfoFuncImpl implements QuerySignAuthInfoFunc {
    private final TradeGlobalSetting globalSetting;
    private final FadadaSignatureUtil signatureUtil;
    //查询沉溺于的25天
    private QueryAgreementInfoFunc queryAgreementInfoFunc;

    private CheckSignAuthFunc checkSignAuthFunc;

    private final SunacSetting sunacSetting;

    private PageAllEmployeeFunc pageAllEmployeeFunc;

    @Override
    public AgreementSigntureTaskTO execute(AgreementSigntureTaskTO agreementSigntureTaskTO) {

        /*checkSignAuthFunc.execute();*/

        AgreementSigntureTaskTO result = new AgreementSigntureTaskTO();
       // result.setAuthInfos(new ArrayList<>());

        SignatureTaskBO signatureTaskBO = agreementSigntureTaskTO.getSignatureTaskBO();
        //获取协议id
        long orderId = signatureTaskBO.getDeliveryOrderId();
        io.terminus.gaia.app.b2b.contract.model.query.QAgreementBO qAgreementBO = new io.terminus.gaia.app.b2b.contract.model.query.QAgreementBO();
        qAgreementBO.setId(new QLongId(orderId));
        //查询协议
        AgreementBO agreementBO = queryAgreementInfoFunc.execute(qAgreementBO);
                //queryAgreementInfoFunc.execute();
                //DS.findOne(AgreementBO.class, "*", "id=?", orderId);
        //判定是自选
        if(AgreementTypeDict.SELF.equals(agreementBO.getType())) {
            //返回
            if(CollectionUtils.isEmpty(agreementBO.getContacts())){
                throw new BusinessException("请配置乙方签署人员");
            }

            //--------------------------------处理乙方信息start-------------------------------------
            Map<String,List<AgreementContactBO>> map =  agreementBO.getContacts().stream().filter(e->StringUtils.isNotBlank(e.getAccreditType()))
                    .collect(Collectors.groupingBy(
                            AgreementContactBO -> AgreementContactBO.getAccreditType(),
                            Collectors.toList()));

            //乙方签字人
            List<SignatureAuthInfoBO> authInfosSign = buildPartyBSignatureAuthInfoBOList(map.get(AccreditTypeDict.SIGN));
            result.setAuthInfosPartyB(authInfosSign);
            //乙方签章人
            List<SignatureAuthInfoBO> authInfosSignature = buildPartyBSignatureAuthInfoBOList(map.get(AccreditTypeDict.SIGNATURE));
            result.setAuthInfosPartyBSignature(authInfosSignature);
            //--------------------------------处理乙方信息end-------------------------------------

            //--------------------------------处理甲方信息start-------------------------------------
            QEmployeeBO qEmployeeBO = new QEmployeeBO();
            qEmployeeBO.setOrgId(new QLong(sunacSetting.getSupplyChainOrgId()));
            Paging<EmployeeBO> employeeBOList = pageAllEmployeeFunc.execute(qEmployeeBO);
            //甲方签字人
            List<SignatureAuthInfoBO> authInfosPartyA = buildAuthInfoBOListByEmployeeBOList(employeeBOList);
            result.setAuthInfosPartyA(authInfosPartyA);
            //甲方签章人
            List<SignatureAuthInfoBO> authInfosPartyASignature = buildAuthInfoBOListByEmployeeBOList(employeeBOList);
            result.setAuthInfosPartyASignature(authInfosPartyASignature);
            //--------------------------------处理甲方信息end------------------------------------
        }

        return result;
    }

    private List<SignatureAuthInfoBO> buildAuthInfoBOListByEmployeeBOList(Paging<EmployeeBO> employeeBOList) {
        List<SignatureAuthInfoBO> result = new ArrayList<>();

        /*for (:) {

        }*/

        return result;
    }

    private List<SignatureAuthInfoBO> buildPartyBSignatureAuthInfoBOList(List<AgreementContactBO> agreementContactBOList) {
        //返回
        if(CollectionUtils.isEmpty(agreementContactBOList)){
            throw new BusinessException("请配置乙方签署人员");
        }
        List<SignatureAuthInfoBO> result = new ArrayList<>();

        for (AgreementContactBO agreementContactBO:agreementContactBOList) {
            SignatureAuthInfoBO signatureAuthInfoBO =  buildSignatureAuthInfoBO(agreementContactBO);
            if(Objects.nonNull(signatureAuthInfoBO)){
                result.add(signatureAuthInfoBO);
            }
        }

        return result;
    }

    private SignatureAuthInfoBO buildSignatureAuthInfoBO(AgreementContactBO agreementContactBO) {
        if(Objects.isNull(agreementContactBO)){
            return null;
        }
        SignatureAuthInfoBO signatureAuthInfoBOResult = new SignatureAuthInfoBO();
        signatureAuthInfoBOResult.setName(agreementContactBO.getPerson());
        signatureAuthInfoBOResult.setTel(agreementContactBO.getPhone());
        return signatureAuthInfoBOResult;
    }



    /*
    @Override
    public SignatureTaskBO execute(SignatureTaskBO signatureTaskBO) {
        SignatureTaskBO returnTask = new SignatureTaskBO();
        returnTask.setAuthInfos(new ArrayList<>());
        //根据订单类型获取签署参与方
        long orderId = signatureTaskBO.getDeliveryOrderId();
        String select = "*" + "," + DeliveryOrderV2BO.tradeContract_field + ".*";
        DeliveryOrderV2BO deliveryOrder = DS.findOne(DeliveryOrderV2BO.class, select, "id=?", orderId);
        if(Objects.isNull(deliveryOrder)) {
            throw new BusinessException("查询签署参与方失败");
        }
        if(BusinessTypeDict.SELL.equals(deliveryOrder.getBusinessType())) {
            Query query = TSQL.selectFrom(SignatureAuthInfoBO.class)
                    .where(TSQL.field(SignatureAuthInfoBO.authType_field).eq(SignatureAuthTypeDict.CORP))
                    .and(TSQL.field(SignatureAuthInfoBO.openId_field).eq(signatureUtil.getOpenCorpId()));
            SignatureAuthInfoBO sysAuthInfo = DS.findOne(query);
            returnTask.getAuthInfos().add(sysAuthInfo);
        }
        //收货人发货人
        else if(BusinessTypeDict.PURCHASE.equals(deliveryOrder.getBusinessType())) {
            Query query = TSQL.selectFrom(SignatureAuthInfoBO.class)
                    .where(TSQL.field(SignatureAuthInfoBO.authType_field).eq(SignatureAuthTypeDict.PERSONAL))
                    .and(TSQL.field(SignatureAuthInfoBO.clientId_field).eq(globalSetting.getSenderPhone()));
            //应用自身，及发货公司
            returnTask.getAuthInfos().add(DS.findOne(query));
        }
        return returnTask;
        // todo 其他地方根据订单编号取对应的签署参与方

//        return returnTask;
    }*/
}
