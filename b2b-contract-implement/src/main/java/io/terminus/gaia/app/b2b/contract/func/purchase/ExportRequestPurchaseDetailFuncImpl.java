package io.terminus.gaia.app.b2b.contract.func.purchase;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import io.terminus.gaia.app.b2b.contract.func.purchase.ExportRequestPurchaseDetailFunc;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseDetailExportTO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.taurus.common.exception.BizException;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantor.module.base.model.result.StringResult;
import io.terminus.trantorframework.sdk.sql.DS;
import io.terminus.trantorframework.sdk.upload.OSSClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 导出需求明细实现
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class ExportRequestPurchaseDetailFuncImpl implements ExportRequestPurchaseDetailFunc {

    private final OSSClient ossClient;

    @Override
    public StringResult execute(RequestPurchaseBO requestPurchaseBO) {
        log.info("开始导出需求明细，需求单ID: {}", requestPurchaseBO.getId());

        Long requestPurchaseId = requestPurchaseBO.getId();
        if (requestPurchaseId == null) {
            throw new BizException("需求单ID不能为空");
        }

        // 查询需求单信息，包含中标供应商和中标品牌
        RequestPurchaseBO requestPurchase = DS.findById(RequestPurchaseBO.class, requestPurchaseId, 
                "*,winSupplierBO.*,winBrandBO.*");

        if (requestPurchase == null) {
            throw new BizException("未找到对应的需求单");
        }

        List<RequestPurchaseLineBO> requestPurchaseLines = new ArrayList<>();

        // 判断是否已设置中标供应商
        if (requestPurchase.getWinSupplierBO() == null || requestPurchase.getWinBrandBO() == null) {
            log.info("需求单ID: {} 未设置中标供应商或中标品牌，导出空明细", requestPurchaseId);
            // 创建空的明细列表，后续会导出空文件
        } else {
            log.info("需求单ID: {}, 中标供应商ID: {}, 中标品牌ID: {}", 
                    requestPurchaseId, 
                    requestPurchase.getWinSupplierBO().getId(), 
                    requestPurchase.getWinBrandBO().getId());

            // 根据需求单ID和中标品牌ID查询对应的需求明细
            requestPurchaseLines = DS.findAll(RequestPurchaseLineBO.class, 
                    "*,brandBO.*,unit.*,agreement.*,supplier.*,spu.*,agreementDetailBO.*", 
                    "requestPurchaseBO.id = ? AND brandBO.id = ? ORDER BY sort ASC",
                    requestPurchaseId, requestPurchase.getWinBrandBO().getId());

            log.info("查询到中标品牌的需求明细{}条", requestPurchaseLines.size());
        }

        // 生成Excel文件
        String fileName = StrUtil.format("trantor/attachments/需求明细清单-{}.xlsx", 
                DatePattern.PURE_DATETIME_FORMAT.format(new Date()));

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        try {
            // 转换为导出TO
            List<RequestPurchaseDetailExportTO> exportData = convertToExportTO(requestPurchaseLines);
            
            // 创建Excel文件
            EasyExcel.write(outputStream, RequestPurchaseDetailExportTO.class)
                    .sheet("需求明细")
                    .doWrite(exportData);

            // 上传到OSS
            ByteArrayInputStream inputStream = IoUtil.toStream(outputStream);
            ossClient.upload(fileName, inputStream);

            String url = ossClient.getUrlWithoutSignature(fileName);
            if (StrUtil.startWith(url, "http:")) {
                url = StrUtil.replace(url, "http:", "https:");
            }

            log.info("导出完成，文件地址: {}", url);
            return new StringResult(url);

        } catch (Exception e) {
            log.error("导出需求明细失败", e);
            throw new BizException("导出失败: " + e.getMessage());
        } finally {
            // 关闭输出流
            IoUtil.close(outputStream);
        }
    }

    /**
     * 转换为导出TO
     */
    private List<RequestPurchaseDetailExportTO> convertToExportTO(List<RequestPurchaseLineBO> lineList) {
        List<RequestPurchaseDetailExportTO> exportList = new ArrayList<>();
        
        if (CollectionUtils.isEmpty(lineList)) {
            // 如果明细为空，返回空列表，Excel会是空的
            return exportList;
        }
        
        for (RequestPurchaseLineBO line : lineList) {
            RequestPurchaseDetailExportTO exportTO = new RequestPurchaseDetailExportTO();
            
            // 基础信息
            exportTO.setNeedLineName(line.getNeedLineName());
            exportTO.setThingSizeDesc(line.getThingSizeDesc());
            exportTO.setNeedNum(line.getNeedNum());
            exportTO.setUnitName(line.getUnit() != null ? line.getUnit().getUnitName() : "");
            
            // 协议信息
            if (line.getAgreement() != null) {
                exportTO.setAgreementName(line.getAgreement().getName());
                exportTO.setAgreementCode(line.getAgreement().getCode());
            }
            
            // 供应商信息
            if (line.getSupplier() != null) {
                exportTO.setSupplierName(line.getSupplier().getEntityName());
            }
            
            // 标品信息
            if (line.getSpu() != null) {
                exportTO.setSpuCode(line.getSpu().getSpuCode());
                exportTO.setSpuName(line.getSpu().getName());
            }
            
            // 采购相关价格
            exportTO.setPurRawMaterialContent(line.getPurRawMaterialContent());
            exportTO.setPurCopperBasicPrice(line.getPurCopperBasicPrice());
            exportTO.setPurOtherCosts(line.getPurOtherCosts());
            exportTO.setPurTaxPrice(line.getPurTaxPrice());
            exportTO.setPurCopperPrice(line.getPurCopperPrice());
            exportTO.setPurDiscountFactor(line.getPurDiscountFactor());
            
            // 销售相关价格
            exportTO.setSaleOtherCosts(line.getSaleOtherCosts());
            exportTO.setSaleCopperPrice(line.getSaleCopperPrice());
            exportTO.setSaleDiscountFactor(line.getSaleDiscountFactor());
            exportTO.setSaleTaxPrice(line.getSaleTaxPrice());
            exportTO.setProfitRate(line.getProfitRate());
            
            exportList.add(exportTO);
        }
        
        return exportList;
    }
} 