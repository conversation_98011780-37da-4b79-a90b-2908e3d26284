package io.terminus.gaia.app.b2b.contract.func.agreement;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.fasc.open.api.exception.ApiException;
import com.google.common.base.Throwables;
import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;

import io.terminus.gaia.app.b2b.contract.dict.AgreementBothTypeDict;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementSigntureTaskTO;
import io.terminus.gaia.app.b2b.trade.model.fulfillment.query.QDeliveryOrderV2BO;
import io.terminus.gaia.app.notice.flow.SmsSendFlow;
import io.terminus.gaia.app.notice.tmodel.SmsSenderTO;
import io.terminus.gaia.common.utils.signature.FadadaSignatureUtil;
import io.terminus.gaia.md.dict.signature.SignatureActorFlagEnum;
import io.terminus.gaia.md.dict.signature.SignatureAuthStatusDict;
import io.terminus.gaia.md.dict.signature.SignatureAuthTypeDict;
import io.terminus.gaia.md.model.signature.SignatureAuthInfoBO;
import io.terminus.gaia.trade.setting.TradeGlobalSetting;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/21
 * @description 由于自营收发货有一些特殊设置，因此该逻辑仅用于收发货,其他业务需要检查再另写逻辑
 */
@RequiredArgsConstructor
@FunctionImpl
@Slf4j
public class CheckSignAuthFuncImpl implements CheckSignAuthFunc {
    private final FadadaSignatureUtil signatureUtil;
    private final SmsSendFlow smsSendFlow;
    private final RedisTemplate<String, String> redisTemplate;
    private final String KEY_PREFIX = "trade:fadada:authurl:";

    @Override
    public List<SignatureAuthInfoBO> execute(AgreementSigntureTaskTO agreementSigntureTaskTO) {
        //1.实际的乙方签字人
        List<SignatureAuthInfoBO> result = new ArrayList<>();
        //返回
        if(!CollectionUtils.isEmpty(agreementSigntureTaskTO.getAuthInfosPartyB())){
            List<SignatureAuthInfoBO> authInfosPartyB = checkAuthInfos(agreementSigntureTaskTO.getAuthInfosPartyB(), SignatureActorFlagEnum.AGREEMENT_PARTYB);
            result.addAll(authInfosPartyB);
        }
        //2.实际的乙方签章人
        if(!CollectionUtils.isEmpty(agreementSigntureTaskTO.getAuthInfosPartyBSignature())) {
            List<SignatureAuthInfoBO> authInfosPartyBSignature = checkAuthInfos(agreementSigntureTaskTO.getAuthInfosPartyBSignature(),SignatureActorFlagEnum.AGREEMENT_PARTYB);
            result.addAll(authInfosPartyBSignature);
        }
        //3.实际的甲方签字人
        if(!CollectionUtils.isEmpty(agreementSigntureTaskTO.getAuthInfosPartyA())) {
            List<SignatureAuthInfoBO> authInfosPartyA = checkAuthInfos(agreementSigntureTaskTO.getAuthInfosPartyA(),SignatureActorFlagEnum.AGREEMENT_PARTYA);
            result.addAll(authInfosPartyA);
        }
        //4.实际的甲方签章人
        if(!CollectionUtils.isEmpty(agreementSigntureTaskTO.getAuthInfosPartyASignature())) {
            List<SignatureAuthInfoBO> authInfosPartyASignature = checkAuthInfos(agreementSigntureTaskTO.getAuthInfosPartyASignature(),SignatureActorFlagEnum.AGREEMENT_PARTYA);
            result.addAll(authInfosPartyASignature);
        }
        return result;
    }

    private List<SignatureAuthInfoBO> checkAuthInfos(List<SignatureAuthInfoBO> authInfosPartyB, SignatureActorFlagEnum signatureActorFlagEnum) {
        List<SignatureAuthInfoBO> resultAuthInfoList = new ArrayList<>();

        for (SignatureAuthInfoBO signature:authInfosPartyB) {
            //原始参数
            SignatureAuthInfoBO signatureAuthInfoBO = signature.getSignatureBO();
            //1确认该条消息是否需要发送获取认证权限
            //1.1获取openid
            String openId = buildOpenId(signatureAuthInfoBO);
            //1.2组装基础参数
            SignatureAuthInfoBO authInfo = buildSignatureAuthInfoBO(signatureAuthInfoBO);
            //1.3获取联系方式
            String phone = signatureAuthInfoBO.getTel();
            //1.4查询该公司下的该人是否有权限(查询该条信息是否已经在我方维护的法大大表中)
            SignatureAuthInfoBO queryAuthInfo = querySignatureAuthInfoBO(signatureAuthInfoBO);
            //1.5根据条件生成redis的存储key
            String redisKey = KEY_PREFIX + authInfo.getClientId() + authInfo.getAuthType() + authInfo.getTel();
            try {
                if (queryAuthInfo == null) {
                    //2.未查询到信息，1确认是个人还是企业
                    //2.1个人如果没有openid
                    //2.2还未授权, 获取授权链接并向授权人发送短信
                    queryAuthInfo = initSignatureAuthInfoBO(authInfo,signatureAuthInfoBO,openId,phone,redisKey,signatureActorFlagEnum);
                }else if(SignatureAuthStatusDict.AUTHING.equals(queryAuthInfo.getStatus())) {
                    //授权链接已发送，还在授权中
                    queryAuthInfo = handleSignatureAuthStatusAuthing(authInfo,queryAuthInfo, redisKey,openId,signatureAuthInfoBO,signatureActorFlagEnum);
                }
                if(Objects.nonNull(queryAuthInfo)){
                    queryAuthInfo.setActorId(queryAuthInfo.getActorId());
                    resultAuthInfoList.add(queryAuthInfo);
                }
            } catch (Exception e) {
                log.info("检查认证授权失败:{}", Throwables.getStackTraceAsString(e));
                throw new BusinessException("检查认证授权失败");
            }
        }
        return resultAuthInfoList;
    }

    /**
     * 处理认证中的数据
     * @param authInfo
     * @param queryAuthInfo
     * @param redisKey
     * @throws ApiException
     */
    private SignatureAuthInfoBO handleSignatureAuthStatusAuthing(SignatureAuthInfoBO authInfo,SignatureAuthInfoBO queryAuthInfo,
                                                  String redisKey ,String openId,SignatureAuthInfoBO signatureAuthInfoBO,
                                                                         SignatureActorFlagEnum signatureActorFlagEnum) throws ApiException {

        //授权中的
        //如果是企业，openid 存在，说明该企业已经被法大大管控，
        //需要查询该人的个人信息是否已经被纳入智采管控，如果纳入智才管控查看该条信息是否已经存在openid，
        //如果个人存在openid，直接将该企业的法大大openid信息加入到该条信息的openid中，修改数据
        //如果个人不存在openid,需要从法大大拉取个人的授权情况，将该个人的openid信息存储到个人的授权信息中
        //如果
        SignatureAuthInfoBO fadadaAuthInfo = new SignatureAuthInfoBO();
        Boolean flag = false;
        if (SignatureAuthTypeDict.CORP.equals(authInfo.getAuthType())) {
            if(!StringUtils.isBlank(openId)){
                //说明是企业的openid，发送的个人注册短信
                SignatureAuthInfoBO signatureAuthInfoBOParam = new SignatureAuthInfoBO();
                signatureAuthInfoBOParam.setClientId(signatureAuthInfoBO.getTel());
                signatureAuthInfoBOParam.setAuthType(SignatureAuthTypeDict.PERSONAL);
                signatureAuthInfoBOParam.setTel(signatureAuthInfoBO.getTel());

                SignatureAuthInfoBO signatureAuthInfoBOQuery = querySignatureAuthInfoBO(signatureAuthInfoBOParam);
                if(Objects.isNull(signatureAuthInfoBOQuery)){
                    SignatureAuthInfoBO signatureAuthInfoBOAdd = buildSignatureAuthInfoBO(signatureAuthInfoBO);
                    signatureAuthInfoBOAdd.setAuthType(SignatureAuthTypeDict.PERSONAL);
                    signatureAuthInfoBOAdd.setClientId(authInfo.getTel());
                    signatureAuthInfoBOAdd.setActorId(signatureActorFlagEnum.name());
                    DS.save(signatureAuthInfoBOAdd);
                    String authUrl = signatureUtil.getAuthUrl(signatureAuthInfoBOAdd);
                    sendAuthUrl(authInfo.getTel(), authUrl,signatureAuthInfoBOAdd,true);
                }

                //获取个人认证状态
                fadadaAuthInfo = signatureUtil.getAuthInfo(signatureAuthInfoBOParam);
                flag = true;
            }else{
                //获取企业认证装状态
                fadadaAuthInfo = signatureUtil.getAuthInfo(authInfo);
            }
        }else{
            fadadaAuthInfo = signatureUtil.getAuthInfo(authInfo);
        }
        if (SignatureAuthStatusDict.AUTHED.equals(fadadaAuthInfo.getStatus())) {
            //更新状态和openId
            SignatureAuthInfoBO updateAuth = new SignatureAuthInfoBO();
            updateAuth.setId(queryAuthInfo.getId());
            if(flag){
                //企业下的个人注册使用企业的openid
                updateAuth.setOpenId(openId);
            }else{
                //企业，个人注册走正常赋值
                updateAuth.setOpenId(fadadaAuthInfo.getOpenId());
            }
            updateAuth.setStatus(SignatureAuthStatusDict.AUTHED);
            DS.update(updateAuth);
            if(flag){
                //走的是企业下的个人注册
                SignatureAuthInfoBO signatureAuthInfoBOParam = new SignatureAuthInfoBO();
                signatureAuthInfoBOParam.setClientId(signatureAuthInfoBO.getTel());
                signatureAuthInfoBOParam.setAuthType(SignatureAuthTypeDict.PERSONAL);
                signatureAuthInfoBOParam.setTel(signatureAuthInfoBO.getTel());
                //将法大大的个人纳入管理，
                SignatureAuthInfoBO persionSign = querySignatureAuthInfoBO(signatureAuthInfoBOParam);
                if(Objects.nonNull(persionSign)){
                    SignatureAuthInfoBO updateAuthPersion = new SignatureAuthInfoBO();
                    updateAuthPersion.setId(persionSign.getId());
                    updateAuthPersion.setOpenId(fadadaAuthInfo.getOpenId());
                    updateAuthPersion.setStatus(SignatureAuthStatusDict.AUTHED);
                    DS.update(updateAuthPersion);
                }
            }
            queryAuthInfo.setStatus(SignatureAuthStatusDict.AUTHED);
        } else {
            //授权中链接7天失效
            if(flag){
                //企业下的个人的短信链接是发的个人认证链接
                redisKey = KEY_PREFIX + authInfo.getTel() + SignatureAuthTypeDict.PERSONAL + authInfo.getTel();
            }
            String authUrl = redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isEmpty(authUrl)) {
                //如果redis已经失效，重新获取认证的url
                authUrl = getAuthUrl(authInfo,flag);
                //signatureUtil.getAuthUrl(authInfo);
                //再次发送认证短信
                sendAuthUrl(queryAuthInfo.getTel(), authUrl,authInfo,flag);
                //将认证短信存储到redis
                redisTemplate.opsForValue().set(redisKey, authUrl, 7, TimeUnit.DAYS);
            }
        }
        return authInfo;
    }

    private String getAuthUrl(SignatureAuthInfoBO authInfo,Boolean flag) throws ApiException {
        String authUrl = "";
        if(flag){
            //企业发个人
            SignatureAuthInfoBO signatureAuthInfoBOParam = new SignatureAuthInfoBO();
            signatureAuthInfoBOParam.setClientId(authInfo.getTel());
            signatureAuthInfoBOParam.setAuthType(SignatureAuthTypeDict.PERSONAL);
            authUrl = signatureUtil.getAuthUrl(authInfo);
        }else{
            //未被管控的个人和企业
            authUrl = signatureUtil.getAuthUrl(authInfo);
        }
        return authUrl;
    }

    /**
     *
     * @param authInfo              查询该公司下的该人是否有权限(查询该条信息是否已经在我方维护的法大大表中)
     * @param signatureAuthInfoBO   原始参数
     * @param openId                openId(可能是原有的人的openId，或者是原有公司的openId)
     * @param phone                 原始参数中的手机号码
     * @param redisKey              存储到redis中的key
     * @throws ApiException
     */
    private SignatureAuthInfoBO initSignatureAuthInfoBO(SignatureAuthInfoBO authInfo, SignatureAuthInfoBO signatureAuthInfoBO,
                                         String openId,String phone,String redisKey,
                                         SignatureActorFlagEnum signatureActorFlagEnum) throws ApiException {
        Boolean flag = false;
        authInfo.setStatus(SignatureAuthStatusDict.AUTHING);
        authInfo.setActorId(signatureActorFlagEnum.name());
        String authUrl = "";
        if(!StringUtils.isBlank(openId)){
            //未被管控的企业下的个人
            //企业的openid  clentid唯一，只需要给人法注册信息，
            if (SignatureAuthTypeDict.CORP.equals(authInfo.getAuthType())) {
                //企业发个人
                authInfo.setClientId(signatureAuthInfoBO.getTel());
                authInfo.setAuthType(SignatureAuthTypeDict.PERSONAL);
                //存储个人的信息
                //save添加之前先查询吧
                SignatureAuthInfoBO queryAuthInfoNew = querySignatureAuthInfoBO(authInfo);
                if(Objects.isNull(queryAuthInfoNew)){
                    //如果企业已经被纳入管控，查询个人是否被纳入管控，
                    //如果个人未被纳入管控，那么发送个人签章短信，
                    //如果个人已被纳入管控不生成短信url和新增条目信息
                    authUrl = signatureUtil.getAuthUrl(authInfo);
                    DS.save(authInfo);
                    flag = true;
                }else{
                    //将该个人纳入该公司管理
                    authInfo.setOpenId(openId);
                }
                authInfo.setAuthType(SignatureAuthTypeDict.CORP);
                authInfo.setClientId(signatureAuthInfoBO.getClientId());
            }
        }else{
            //未被管控的个人和企业
            authUrl = signatureUtil.getAuthUrl(authInfo);
        }
        if(StringUtils.isNotBlank(authUrl)){
            //原有openid无，则发请求链接
            sendAuthUrl(phone, authUrl,authInfo,flag);
            //
            DS.save(authInfo);
        }
        if(flag){
            //企业下个人
            redisKey = KEY_PREFIX + authInfo.getTel() + SignatureAuthTypeDict.PERSONAL + authInfo.getTel();
        }
        redisTemplate.opsForValue().set(redisKey, authUrl, 7, TimeUnit.DAYS);
        //此时如果本应用相关信息也未录入，无需授权，直接初始化
        initAuthInfoSelf(phone);
        return authInfo;
    }

    private SignatureAuthInfoBO querySignatureAuthInfoBO(SignatureAuthInfoBO signatureAuthInfoBO) {
        // authType PERSONAL 0      个人
        // clientId 为个人的手机号码   tel 为个人的手机号码    openid为自己注册的法大大的标识
        // authType CORP     1      企业
        // clientId 为企业的的编号     tel 为操作人的手机号码    openid为企业注册的法大大的标识
        Query query = TSQL.selectFrom(SignatureAuthInfoBO.class)
                .where(TSQL.field(SignatureAuthInfoBO.authType_field).eq(signatureAuthInfoBO.getAuthType()))
                .and(TSQL.field(SignatureAuthInfoBO.clientId_field).eq(signatureAuthInfoBO.getClientId()))
                .and(TSQL.field(SignatureAuthInfoBO.tel_field).eq(signatureAuthInfoBO.getTel()));
        //查询该条信息是否已经在我方维护的法大大表中
        SignatureAuthInfoBO queryAuthInfo = DS.findOne(query);
        return queryAuthInfo;
    }

    private SignatureAuthInfoBO buildSignatureAuthInfoBO(SignatureAuthInfoBO signatureAuthInfoBO) {
        SignatureAuthInfoBO result = new SignatureAuthInfoBO();
        result.setAuthType(signatureAuthInfoBO.getAuthType());
        result.setClientId(signatureAuthInfoBO.getClientId());
        result.setName(signatureAuthInfoBO.getName());
        result.setStatus(SignatureAuthStatusDict.NOAUTH);
        result.setTel(signatureAuthInfoBO.getTel());
        result.setCompanyName(signatureAuthInfoBO.getCompanyName());
        return result;
    }

    private String buildOpenId(SignatureAuthInfoBO authInfo) {
        String openId = "";
        Query query = TSQL.selectFrom(SignatureAuthInfoBO.class)
                .where(TSQL.field(SignatureAuthInfoBO.authType_field).eq(authInfo.getAuthType()))
                .and(TSQL.field(SignatureAuthInfoBO.clientId_field).eq(authInfo.getClientId()));
        List<SignatureAuthInfoBO> queryAuthInfoList = DS.findAll(query);
        if(!CollectionUtils.isEmpty(queryAuthInfoList)){
            List<SignatureAuthInfoBO> queryAuthInfos= queryAuthInfoList.stream()
                    .filter(e-> org.apache.commons.lang3.StringUtils.isNotBlank(e.getOpenId()))
                    .collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(queryAuthInfos)){
                openId =  queryAuthInfos.get(0).getOpenId();
            }
        }
        return openId;
    }

    /**
     * 发送授权短信
     * @param phone 手机号码
     * @param authUrl       授权链接
     */
    private void sendAuthUrl(String phone, String authUrl,SignatureAuthInfoBO authInfo,Boolean flag) {
        CompletableFuture.runAsync(() -> {
            log.info("发送电子签章授权短信,联系人={}", phone);
            log.info("发送电子签章授权短信,入参={}", JSON.toJSONString(authInfo));
            SmsSenderTO smsSenderTO = new SmsSenderTO();
            smsSenderTO.setPhones(ListUtil.toList(phone));
            if(flag){
                //未被管控的企业下的个人
                smsSenderTO.setTemplateCode(signatureUtil.getSmsTemplateId());
                smsSenderTO.setTemplateValues(ListUtil.toList(authUrl + " "));
            }else{
                if(SignatureAuthTypeDict.PERSONAL.equals(authInfo.getAuthType())){
                    //个人认证模板
                    smsSenderTO.setTemplateCode(signatureUtil.getSmsTemplateId());
                    /* smsSenderTO.setTemplateValues(ListUtil.toList(authUrl));*/
                    smsSenderTO.setTemplateValues(ListUtil.toList(authUrl + " "));
                }else{
                    //企业认证模板
                    smsSenderTO.setTemplateCode(signatureUtil.getSmsCorpTemplateId());
                    smsSenderTO.setTemplateValues(ListUtil.toList(authInfo.getCompanyName(), authUrl + " "));
                }
            }
//            smsSenderTO.setTemplateValues(ListUtil.toList(authUrl));
            smsSendFlow.execute(smsSenderTO);
            log.info("发送电子签章授权短信,出参={}", JSON.toJSONString(smsSenderTO));
        }).exceptionally(ex -> {
            log.error("发送电子签章授权短信失败，联系人={}，cause={}", phone, Throwables.getStackTraceAsString(ex));
            return null;
        });
    }

    /**
     *  初始化应用自身的认证信息
     * @param phone
     * @return
     * @throws ApiException
     */
    public void initAuthInfoSelf(String phone) throws ApiException {
        Query query = TSQL.selectCount().from(SignatureAuthInfoBO.class)
                .where(TSQL.field(SignatureAuthInfoBO.authType_field).eq(SignatureAuthTypeDict.CORP))
                .and(TSQL.field(SignatureAuthInfoBO.openId_field).eq(signatureUtil.getOpenCorpId()));
        long count = DS.count(query);
        if(count > 0) {
            return;
        }
        SignatureAuthInfoBO authSelf = signatureUtil.getAuthInfoSelf();
        authSelf.setTel(phone);
        DS.save(authSelf);
        /*else if(StringUtils.isEmpty(queryAuthInfo.getTel()) || !phone.equals(queryAuthInfo.getTel())) {
            SignatureAuthInfoBO updateAuth = new SignatureAuthInfoBO();
            queryAuthInfo.setId(queryAuthInfo.getId());
            queryAuthInfo.setTel(phone);
            DS.update(updateAuth);
        }*/
    }
}
