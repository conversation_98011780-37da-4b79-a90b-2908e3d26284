package io.terminus.gaia.app.b2b.contract.func.contract.excel;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableList;
import io.terminus.gaia.app.b2b.contract.func.contract.read.PageContractLineFunc;
import io.terminus.gaia.app.b2b.contract.model.ContractLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QContractLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QYzContractBO;
import io.terminus.gaia.app.b2b.contract.properties.ContractImportAttributeProperties;
import io.terminus.gaia.app.b2b.contract.tmodel.ContractLineImportRequestTO;
import io.terminus.gaia.app.b2b.contract.tmodel.ContractLineImportResponseTO;
import io.terminus.gaia.app.b2b.contract.util.ContractImportHelper;
import io.terminus.gaia.app.b2b.item.model.SkuBOExt;
import io.terminus.gaia.app.b2b.item.model.query.QSkuBOExt;
import io.terminus.gaia.app.b2b.trade.model.contract.B2bContractOrderLineExtBO;
import io.terminus.gaia.common.utils.EnhanceDS;
import io.terminus.gaia.item.dict.item.ItemTypeNewDict;
import io.terminus.gaia.md.dict.AttributeConfirmStageDict;
import io.terminus.trantorframework.Page;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.querymodel.type.support.QLongId;
import io.terminus.trantorframework.querymodel.type.support.QString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class ContractLineImportFuncImpl implements ContractLineImportFunc {

    private final ContractImportAttributeProperties properties;
    private final PageContractLineFunc pageContractLineFunc;

    @Override
    public ContractLineImportResponseTO execute(ContractLineImportRequestTO request) {
        InputStream inputStream;
        try {
            inputStream = new URL(request.getImportFile()).openStream();
        } catch (MalformedURLException e) {
            log.error("合同清单文件解析失败：{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException("下载清单文件失败");
        } catch (IOException e) {
            log.error("合同清单文件流异常：{}", Throwables.getStackTraceAsString(e));
            throw new BusinessException("下载清单文件失败");
        }

        List<String> head = ImmutableList.of("商品名称", "商品编码", "标品编码", "属性", properties.getPrefix(), "含税单价（元）", "税率（%）", "本次下单数量", "开始租期（格式：xxxx/xx/xx）", "结束租期（格式：xxxx/xx/xx）", "签约数量", "已下单/租赁中数量", "单位", "小计", "备注");
        ContractImportHelper helper = new ContractImportHelper(properties, AttributeConfirmStageDict.TRADE, head);

        EasyExcel.read(inputStream, helper.getReadListener()).sheet().doRead();

        List<ContractLineBO> lineList = getContractLineList(request, helper.getColumnDataList("商品编码"));
        Map<String, ContractLineBO> lineMap = CollStreamUtil.toIdentityMap(lineList, line -> line.getSku().getSkuCode());

        List<String> confirmSkuList = new ArrayList<>();
        ContractLineImportResponseTO response = new ContractLineImportResponseTO();

        List<String> leaseTypes = ImmutableList.of(ItemTypeNewDict.EQUIPMENT_LEASE, ItemTypeNewDict.MATERIAL_LEASE);
        helper.forEach((data, attributeData) -> {
            try {
                String skuCode = data.get("商品编码");
                BigDecimal quantity = NumberUtil.toBigDecimal(data.get("本次下单数量"));
                if (quantity.compareTo(BigDecimal.ZERO) < 1) {
                    log.warn("商品编码【{}】对应商品数量小于或等于0", skuCode);
                    return;
                }

                if (!lineMap.containsKey(skuCode)) {
                    log.warn("商品编码【{}】不在协议清单列表中", skuCode);
                    confirmSkuList.add(data.get("商品名称") + "(" + skuCode + ")");
                    return;
                }

                ContractLineBO line = lineMap.get(skuCode);
                SkuBOExt sku = line.getSkuJson();

                B2bContractOrderLineExtBO lineExt = new B2bContractOrderLineExtBO();
                lineExt.setContractLine(line);
                lineExt.setTradeQty(quantity);
                lineExt.setRemark(data.get("备注"));
                lineExt.setSku(sku);

                if (leaseTypes.contains(line.getSku().getItemType())) {
                    lineExt.setLeaseStartTime(parseDate(data.get("开始租期（格式：xxxx/xx/xx）")));
                    lineExt.setLeaseEndTime(parseDate(data.get("结束租期（格式：xxxx/xx/xx）")));
                }

                helper.setAttributeValue(sku.getNewAttributes(), attributeData);

                response.getDataList().add(lineExt);
            } catch (NumberFormatException e) {
                log.error("数量异常: {}", Throwables.getStackTraceAsString(e));
                throw new BusinessException("数量填写异常");
            } catch (DateException e) {
                log.error("租期格式异常：{}", Throwables.getStackTraceAsString(e));
                throw new BusinessException("租期格式异常，请确认租期格式是否为：****年**月**日 或 ****-**-** 或 ****/**/**");
            }
        });

        if (!confirmSkuList.isEmpty()) {
            response.setConfirmMsg(StrUtil.format("商品【{}】不属于当前合同，是否继续导入商品清单？", StrUtil.join("|", confirmSkuList)));
        }
        return response;
    }

    private List<ContractLineBO> getContractLineList(ContractLineImportRequestTO request, List<String> skuCodeList) {
        QContractLineBO qContractLineBO = new QContractLineBO();
        qContractLineBO.getQueryParams().setPage(new Page(1, Integer.MAX_VALUE));
        EnhanceDS.addQueryField(qContractLineBO, "spu.*");

        QYzContractBO qYzContractBO = new QYzContractBO();
        qYzContractBO.setId(new QLongId(request.getContractId()));
        qContractLineBO.setContract(qYzContractBO);

        QSkuBOExt qSkuBOExt = new QSkuBOExt();
        qSkuBOExt.setSkuCode(new QString(skuCodeList));
        qContractLineBO.setSku(qSkuBOExt);

        return pageContractLineFunc.execute(qContractLineBO).getData();
    }

    private Date parseDate(String dateStr) {
        if (StrUtil.isNotBlank(dateStr)) {
            if (StrUtil.contains(dateStr, "年")) {
                return DateUtil.parse(dateStr, DatePattern.CHINESE_DATE_FORMAT);
            } else if (StrUtil.contains(dateStr, "-")) {
                return DateUtil.parseDate(dateStr);
            } else {
                return DateUtil.parse(dateStr, "yyyy/MM/dd");
            }
        }
        return null;
    }
}
