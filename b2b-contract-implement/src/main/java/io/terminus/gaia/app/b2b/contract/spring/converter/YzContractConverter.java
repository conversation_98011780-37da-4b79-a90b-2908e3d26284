package io.terminus.gaia.app.b2b.contract.spring.converter;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.terminus.gaia.app.b2b.contract.dict.YzContractBusinessTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.YzContractStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.YzContractTypeDict;
import io.terminus.gaia.app.b2b.contract.model.*;
import io.terminus.gaia.app.b2b.contract.spring.model.yz.YzContract;
import io.terminus.gaia.app.b2b.contract.spring.model.yz.YzContractDetail;
import io.terminus.gaia.app.b2b.contract.tmodel.YzContactTO;
import io.terminus.gaia.app.b2b.item.model.SkuBOExt;
import io.terminus.gaia.common.constants.GaiaCommonConstants;
import io.terminus.gaia.item.dict.item.ItemTypeNewDict;
import io.terminus.gaia.item.model.item.ItemBO;
import io.terminus.gaia.item.model.price.PriceSchemeBO;
import io.terminus.gaia.item.model.sku.SkuBO;
import io.terminus.gaia.md.dict.MerchantTypeDict;
import io.terminus.gaia.md.dict.SyncStatusDict;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.CompanyBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.model.CompanyRelationWithYzBO;
import io.terminus.gaia.organization.model.EmployeeBO;
import io.terminus.gaia.organization.model.ext.ProjectBO;
import io.terminus.gaia.organization.tmodel.PaymentOverdueRuleTO;
import io.terminus.trantorframework.api.type.Currency;
import io.terminus.trantorframework.json.Json;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class YzContractConverter {

    /**
     * 合同convert
     *
     * @param syncContract
     * @return
     */
    public static YzContractBO convert(YzContract syncContract, YzContractBO existContract) {
        YzContractBO newContractBO = new YzContractBO();
        if (existContract != null) {
            newContractBO.setId(existContract.getId());
        }
        List<String> errors = new ArrayList<>();
        newContractBO.setContractCode(syncContract.getContractNo());
        newContractBO.setContractUUID(syncContract.getContractUUID());
        newContractBO.setExternalCode(String.valueOf(syncContract.getId()));
        newContractBO.setVatRate(syncContract.getVatRate());
        // 背靠背外部合同编码
        if (ObjectUtil.isNotNull(syncContract.getBackContractId())) {
            newContractBO.setBackExternalCode(String.valueOf(syncContract.getBackContractId()));
        }
        newContractBO.setAutoCreate(syncContract.getAutoCreate());
        newContractBO.setContractName(syncContract.getContractName());
        newContractBO.setContractTypeDict(convertType(syncContract.getContractCategory()));
        newContractBO.setExceedProportion(syncContract.getExceedProportion());
        newContractBO.setControlMode(syncContract.getControlMode());
        // 新增应收账期相关字段
        newContractBO.setMainPayableSchemeId(syncContract.getMainPayableSchemeId());
        newContractBO.setPayablePeriod(buildPayableSchemeSnapshot(syncContract));
        // 付款日
        newContractBO.setPaymentDays(syncContract.getPaymentDays());
        if("1".equals(syncContract.getPaymentDays()) && StringUtils.isBlank(syncContract.getPaymentMonthlyDay())) {
            log.error("合同【{}-{}】的付款日次月为空", syncContract.getId(), syncContract.getContractName());
            return null;
        }
        newContractBO.setPaymentMonthlyDay(syncContract.getPaymentMonthlyDay());
        // 每月对账日
        newContractBO.setReconciledDays(syncContract.getReconciledDays());
        if (newContractBO.getContractTypeDict() == null) {
            log.error("合同【{}-{}】的 ContractType为空，移除", syncContract.getId(), syncContract.getContractName());
            return null;
        }
        newContractBO.setContractStatusDict(convertStatus(syncContract.getStatus()));
        if (newContractBO.getContractStatusDict() == null) {
            log.error("合同【{}-{}】的 ContractStatus为空，移除", syncContract.getId(), syncContract.getContractName());
            return null;
        }
        newContractBO.setPerformanceStatus(syncContract.getPerformanceStatus());
        if (newContractBO.getPerformanceStatus() == null) {
            log.error("合同【{}-{}】的 PerformanceStatus为空，移除", syncContract.getId(), syncContract.getContractName());
            return null;
        }
        if (StringUtils.isNotBlank(syncContract.getFrameworkAgreementNo())) {
            // 查询关联协议
            AgreementBO agreementBO = DS.findOne(AgreementBO.class, "*", "code = ?", syncContract.getFrameworkAgreementNo());
            if (agreementBO == null) {
                errors.add("关联协议");
            } else {
                newContractBO.setAgreementBO(agreementBO);
                newContractBO.setRentPeriod(agreementBO.getRentPeriod());
            }
        }

        newContractBO.setBusinessType(convertBusinessType(syncContract.getContractType()));

        // 乙方
        CompanyBO yfCompanyBO = syncContract.getSupplierNo() == null ? null :
                DS.findById(CompanyBO.class, syncContract.getSupplierNo());
        if (yfCompanyBO == null) {
            if (ObjectUtil.isNotNull(syncContract.getSupplierNo())) {
                List<CompanyRelationWithYzBO> companyRelationWithYzBOList = DS.findAll(CompanyRelationWithYzBO.class, "*", "companyId = ?", syncContract.getSupplierNo());
                if (CollectionUtils.isNotEmpty(companyRelationWithYzBOList)) {
                    EntityBO partyB = new EntityBO();
                    partyB.setId(companyRelationWithYzBOList.get(0).getEntityId());
                    newContractBO.setPartyB(partyB);
                }
            }
        } else {
            newContractBO.setPartyB(yfCompanyBO.getEntity());
        }

        if (ObjectUtil.isNull(newContractBO.getPartyB())) {
            errors.add(StrUtil.format("合同乙方【{}-{}】", syncContract.getSupplierNo(), syncContract.getSupplierOrgName()));
        }

        // 甲方
        List<CompanyBO> jfCompanies = new ArrayList<>();
        if (ObjectUtil.isNotNull(syncContract.getAutoCreate()) && syncContract.getAutoCreate() == 1) {
            jfCompanies = syncContract.getPurchaserOrgNo() == null ? new ArrayList<>() :
                    DS.findAll(CompanyBO.class, "*", "merchantType = ? AND orgId = ?", MerchantTypeDict.SUPPLY_CHAIN, syncContract.getPurchaserOrgNo());
        } else {
            jfCompanies = syncContract.getPurchaserOrgNo() == null ? new ArrayList<>() :
                    DS.findAll(CompanyBO.class, "*", "merchantType = ? AND orgId = ?", MerchantTypeDict.PURCHASER, syncContract.getPurchaserOrgNo());
        }
        if (CollectionUtils.isEmpty(jfCompanies)) {
            errors.add(StrUtil.format("合同甲方【{}-{}】", syncContract.getPurchaserOrgNo(), syncContract.getPurchaserOrgName()));
        } else {
            newContractBO.setPartyA(jfCompanies.get(0).getEntity());
        }
        // 发起人
        if (syncContract.getSubmitUserNo() != null && ObjectUtil.notEqual(0L, syncContract.getSubmitUserNo())) {
            EmployeeBO employee = DS.findById(EmployeeBO.class, syncContract.getSubmitUserNo());
            newContractBO.setSubmitBy(Optional.ofNullable(employee).map(EmployeeBO::getUser).orElse(null));
        }
        newContractBO.setSubmitByName(syncContract.getSubmitUserName());

        // 供应商联系人
        YzContactTO supplierContact = new YzContactTO();
        supplierContact.setPerson(syncContract.getSupplierContactPerson());
        supplierContact.setPhone(syncContract.getSupplierContactPhone());
        supplierContact.setMail(syncContract.getSupplierContactMail());
        newContractBO.setPartBContact(supplierContact);

        ProjectBO projectBO = DS.findById(ProjectBO.class, syncContract.getProjectNo());
        if (projectBO == null) {
            log.error("云筑合同拉取，根据【{}】找不到项目", syncContract.getProjectNo());
            errors.add("关联项目");
        } else {
            newContractBO.setProjectStage(projectBO);
        }
        newContractBO.setSignAt(syncContract.getSignDate() == null ? null :
                Date.from(syncContract.getSignDate().atZone(ZoneId.systemDefault()).toInstant()));
        newContractBO.setStartDate(syncContract.getStartDate() == null ? null :
                Date.from(syncContract.getStartDate().atZone(ZoneId.systemDefault()).toInstant()));
        newContractBO.setCompletedDate(syncContract.getCompletedDate() == null ? null :
                Date.from(syncContract.getCompletedDate().atZone(ZoneId.systemDefault()).toInstant()));
        newContractBO.setContractAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getTotalAmountExcludeTax(), BigDecimal.ZERO)));
        newContractBO.setContractTaxAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getTotalAmount(), BigDecimal.ZERO)));
        newContractBO.setTaxAmt(newContractBO.getContractTaxAmt().subtract(newContractBO.getContractAmt()));
        newContractBO.setYzwContractNo(syncContract.getYzwContractNo());
        // 0720版本 新增租赁字段
        try {
            if (ObjectUtil.isNotNull(syncContract.getLeaseStartTime())) {
                newContractBO.setLeaseStartTime(DateUtil.parse(syncContract.getLeaseStartTime(), DatePattern.NORM_DATETIME_FORMAT));
            }
            if (ObjectUtil.isNotNull(syncContract.getLeaseEndTime())) {
                newContractBO.setLeaseEndTime(DateUtil.parse(syncContract.getLeaseEndTime(), DatePattern.NORM_DATETIME_FORMAT));
            }
        } catch (Exception e) {
            e.getStackTrace();
            log.error("合同同步，时间格式转换异常");
        }

        // 只同步一次的字段
        if (existContract == null) {
            if (ObjectUtil.isNotNull(syncContract.getIsReplenish()) && "1".equals(syncContract.getIsReplenish())) {
                // 已下单金额
                newContractBO.setPlacedAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountReconciliationPayAmount(), BigDecimal.ZERO)));
            } else {
                // 已下单金额
                newContractBO.setPlacedAmt(new Currency(BigDecimal.ZERO));
            }
            // 可下单金额
            newContractBO.setPlacableAmt(new Currency(BigDecimal.ZERO));
            // 累计应付
            newContractBO.setNeedPayAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountPayAmount(), BigDecimal.ZERO)));
            newContractBO.setSumPayableAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountPayAmount(), BigDecimal.ZERO)));
            newContractBO.setSumPayableNoTaxAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountPayBHSAmount(), BigDecimal.ZERO)));
            newContractBO.setSumPayableTax(syncContract.getAccountPayTAX() != null ?
                    syncContract.getAccountPayTAX() :
                    newContractBO.getSumPayableAmt().subtract(newContractBO.getSumPayableNoTaxAmt()).getValue());
            // 累计对账
            newContractBO.setSumReconciliationAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountReconciliationPayAmount(), BigDecimal.ZERO)));
            newContractBO.setSumReconciliationNoTaxAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountReconciliationPayBHSAmount(), BigDecimal.ZERO)));
            // 三公司特殊处理
            if (2 == syncContract.getCostManagementSystem()) {
                if (ObjectUtil.isNull(syncContract.getAccountReconciliationPayBHSAmount())
                        && ObjectUtil.isNotNull(syncContract.getAccountReconciliationPayAmount())
                        && ObjectUtil.isNotNull(syncContract.getVatRate())) {
                    newContractBO.setSumReconciliationNoTaxAmt(new Currency(syncContract.getAccountReconciliationPayAmount().divide(syncContract.getVatRate().add(BigDecimal.ONE), 2, BigDecimal.ROUND_HALF_UP)));
                }
            }
            newContractBO.setSumReconciliationTax(syncContract.getAccountReconciliationPayTAX() != null ?
                    syncContract.getAccountReconciliationPayTAX() :
                    newContractBO.getSumReconciliationAmt().subtract(newContractBO.getSumReconciliationNoTaxAmt()).getValue());
            // 累计申请付款（应付）
            newContractBO.setSumRequestAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountPayAmount(), BigDecimal.ZERO)));
            newContractBO.setSumRequestTaxAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountPayBHSAmount(), BigDecimal.ZERO)));
            // 累计已付
            newContractBO.setPaidAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountPaidAmount(), BigDecimal.ZERO)));
            newContractBO.setSumPaidAmt(newContractBO.getPaidAmt());
            // 累计已付进度款
            newContractBO.setSumProgressAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountPaidJDKAmount(), BigDecimal.ZERO)));
            newContractBO.setSumProgressNoTaxAmt(newContractBO.getSumProgressAmt());
            // 累计预付款金额
            newContractBO.setSumPrepayAmt(new Currency(ObjectUtil.defaultIfNull(syncContract.getAccountPaidYFKAmount(), BigDecimal.ZERO)));
            newContractBO.setSumPrepayNoTaxAmt(newContractBO.getSumPrepayAmt());
        }

        newContractBO.setIsSupply(Boolean.TRUE.equals(syncContract.getSupplementaryContract()));
        newContractBO.setIsCenterPay(Boolean.TRUE.equals(syncContract.getCombinePay()));
        newContractBO.setIsMallPay(Boolean.TRUE.equals(syncContract.getMallPay()));
        newContractBO.setSourceSystem(syncContract.getCostManagementSystem());
        newContractBO.setPaymentScheme(buildPaymentSchemeSnapshot(syncContract));
        //添加逾期规则快照
        newContractBO.setPaymentOverdueRule(buildPaymentOverdueRule(syncContract));
        if (!(existContract != null && existContract.getCreateNewContract() != null)) {
            newContractBO.setCreateNewContract(syncContract.getCreateNewContract());
        }
        if (Boolean.TRUE.equals(newContractBO.getIsSupply())) {
            // 关联主合同
            YzContractBO parentContract = DS.findOne(YzContractBO.class, "*", "externalCode = ?", syncContract.getMainContractId());
            newContractBO.setMainContractBO(parentContract);
        }

        if (CollectionUtils.isNotEmpty(syncContract.getDetailList())) {
            List<ContractLineBO> newLines = new ArrayList<>();
            List<String> externalCodes = syncContract.getDetailList().stream().map(e -> e.getId().toString()).distinct().collect(Collectors.toList());
            Map<String, ContractLineBO> existLineMap = existContract == null ? new HashMap<>() :
                    DS.findAll(ContractLineBO.class, "*", "contract = ? AND externalCode IN (?)", existContract.getId(), externalCodes)
                            .stream().collect(Collectors.toMap(
                                    ContractLineBO::getExternalCode,
                                    Function.identity(),
                                    (existing, replacement) -> replacement // 遇到重复键时保留原值
                            ));


            Set<String> skuCodes = syncContract.getDetailList().stream().map(YzContractDetail::getSkuNo).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<String, SkuBOExt> skuMap = CollectionUtils.isEmpty(skuCodes) ? new HashMap<>() :
                    DS.findAll(SkuBOExt.class, "*", "skuCode IN (?)", skuCodes)
                            .stream().collect(Collectors.toMap(
                                    SkuBOExt::getSkuCode,
                                    Function.identity(),
                                    (existing, replacement) -> replacement // 遇到重复键时保留原值
                            ));

            syncContract.getDetailList().forEach(syncLine -> {
                ContractLineBO existLine = existLineMap.get(syncLine.getId().toString());
                ContractLineBO newLine = new ContractLineBO();
                if (existLine != null) {
                    newLine.setId(existLine.getId());
                }
                if (existContract != null) {
                    newLine.setContract(YzContractBO.of(existContract.getId()));
                }
                newLine.setCode(syncLine.getId().toString());
                newLine.setExternalCode(syncLine.getId().toString());
                newLine.setThirdContractIntentionDetailNo(syncLine.getThirdContractIntentionDetailNo());
                newLine.setStatus(convertDetailStatus(syncLine.getDetailStatus()));
                newLine.setIntentionDetailNo(syncLine.getContractIntentionDetailNo());
                //newLine.setIntentionNo(syncContract.getContractIntentionNo());
                newLine.setIntentionNo(syncLine.getContractIntentionNo());
                SkuBOExt skuBO = skuMap.get(syncLine.getSkuNo());
                if (skuBO == null) {
                    log.error("云筑合同同步，根据sku编码【{}】找不到SKU", syncLine.getSkuNo());
                    return;
                }
                newLine.setSku(skuBO);
                newLine.setSpu(skuBO.getSpu());
                newLine.setSkuJson(buildSkuSnapshot(syncLine));
                newLine.setUnitName(syncLine.getUnitName());
                newLine.setMaterialCategoryFullPathName(syncLine.getMaterialFullPathName());
                newLine.setPriceScheme(buildPriceSchemeSnapshot(syncLine));
                // 应收账期方案
                newLine.setPayableSchemeTemplates(buildPayableSchemeTemplateSnapshot(syncLine));
                newLine.setSalePriceCalLineId(syncLine.getSalePriceCalLineId());
                newLine.setCopperContent(syncLine.getCopperContent());
                newLine.setCopperBasicPrice(syncLine.getCopperBasicPrice());
                newLine.setOtherCosts(syncLine.getOtherCosts());
                newLine.setSaleDiscountFactor(syncLine.getSaleDiscountFactor());
                newLine.setYanmiCopperPrice(syncLine.getYanmiCopperPrice());
                newLine.setPriceType(syncLine.getPriceType());
                newLine.setBidBrands(buildBidBrand(syncLine));
//                newLine.setPrcWithTax(syncLine.getAgreementPrice() == null ? null : new Currency(syncLine.getAgreementPrice()));
//                newLine.setPrcWithoutTax(syncLine.getAgreementPriceExcludeTax() == null ? null : new Currency(syncLine.getAgreementPriceExcludeTax()));
                newLine.setPrcWithTax(syncLine.getPurchasePrice() == null ? null : new Currency(syncLine.getPurchasePrice()));
                newLine.setPrcWithoutTax(syncLine.getPurchasePriceExcludeTax() == null ? null : new Currency(syncLine.getPurchasePriceExcludeTax()));
                newLine.setTaxRate(Optional.of(syncLine.getTaxRate()).map(e -> e.multiply(GaiaCommonConstants.HUNDRED)).orElse(null));
                newLine.setSignedQty(syncLine.getPurchaseNum());
                newLine.setDiscountFactor(syncLine.getDiscountFactor());
                if (ObjectUtil.isNotNull(syncLine.getAdjustItem())
                        && syncLine.getAdjustItem() == 1) {
                    newLine.setAdjustItem(true);
                } else {
                    newLine.setAdjustItem(false);
                }
                // 0720版本 新增租赁字段
                if (ItemTypeNewDict.EQUIPMENT_LEASE.equals(skuBO.getItemType())
                        || ItemTypeNewDict.MATERIAL_LEASE.equals(skuBO.getItemType())) {
                    newLine.setLeaseForm(syncLine.getLeaseForm());
                    newLine.setLeaseTime(syncLine.getLeaseTime());
                }


                // 只在首次同步时新增
                if (existLine == null) {
                    newLine.setPlacedQty(BigDecimal.ZERO);
                    newLine.setPlacingQty(BigDecimal.ZERO);
                }
                // 同步合同备注信息
                newLine.setRemark(syncLine.getRemark());
                newLines.add(newLine);
            });
            newContractBO.setLines(newLines);
        }

        if (CollectionUtils.isNotEmpty(errors)) {
            newContractBO.setSyncStatus(SyncStatusDict.FAIL);
            newContractBO.setSyncRemark(String.join("、", errors) + "同步失败");
        } else {
            newContractBO.setSyncStatus(SyncStatusDict.SUCCESS);
            newContractBO.setSyncRemark("");
        }
        // 同步是否直入直出
        newContractBO.setIsStraightInAndOut(syncContract.getIsStraightInAndOut());
        // 同步合同计价模式
        newContractBO.setPriceMode(syncContract.getPriceMode());

        newContractBO.setProcurementType(syncContract.getProcurementType());
        // 是否是补录合同意向
        newContractBO.setIsReplenish(syncContract.getIsReplenish());
        // 是否大宗合同
        newContractBO.setBulkContract(syncContract.getBulkContract());
        newContractBO.setCostManagementSystem(syncContract.getCostManagementSystem());
        newContractBO.setProcessExceedProportion(null != syncContract.getProcessExceedProportion() ? syncContract.getProcessExceedProportion() : BigDecimal.ZERO);
        return newContractBO;
    }


    /**
     * 应收账款方案
     *
     * @param yzContract
     * @return
     */
    private static List<PayableSchemeTemplateBO> buildPayableSchemeSnapshot(YzContract yzContract) {
        if (StringUtils.isBlank(yzContract.getPayablePeriod())) {
            return null;
        }

        try {
            return JSON.parseArray(yzContract.getPayablePeriod(), PayableSchemeTemplateBO.class);
        } catch (Exception ex) {
            log.error("应收账款方案快照反序列化失败, snapshot:{},cause:{}", yzContract.getPaymentType(), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 付款方案
     *
     * @param yzContract
     * @return
     */
    private static PaymentSchemeBO buildPaymentSchemeSnapshot(YzContract yzContract) {
        if (StringUtils.isBlank(yzContract.getPaymentType())) {
            return null;
        }

        try {
            return JSON.parseObject(yzContract.getPaymentType(), PaymentSchemeBO.class);
        } catch (Exception ex) {
            log.error("付款方案快照反序列化失败, snapshot:{},cause:{}", yzContract.getPaymentType(), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 逾期规则
     * @param yzContract
     * @return
     */
    private static PaymentOverdueRuleTO buildPaymentOverdueRule(YzContract yzContract) {
        if (StringUtils.isBlank(yzContract.getPaymentOverdueRule())) {
            return null;
        }

        try {
            return JSON.parseObject(yzContract.getPaymentOverdueRule(), PaymentOverdueRuleTO.class);
        } catch (Exception ex) {
            log.error("逾期规则快照反序列化失败, snapshot:{},cause:{}", yzContract.getPaymentOverdueRule(), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 价格方案
     *
     * @param contractDetail
     * @return
     */
    private static PriceSchemeBO buildPriceSchemeSnapshot(YzContractDetail contractDetail) {
        if (StringUtils.isBlank(contractDetail.getPriceScheme())) {
            return null;
        }

        try {
            return JSON.parseObject(contractDetail.getPriceScheme(), PriceSchemeBO.class);
        } catch (Exception ex) {
            log.error("价格方案快照反序列化失败, snapshot:{},cause:{}", contractDetail.getPriceScheme(), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 应收账期方案快照
     *
     * @param contractDetail
     * @return
     */
    private static List<PayableSchemeTemplateBO> buildPayableSchemeTemplateSnapshot(YzContractDetail contractDetail) {
        if (StringUtils.isBlank(contractDetail.getPayablePeriod())) {
            return null;
        }

        try {
            return JSON.parseArray(contractDetail.getPayablePeriod(), PayableSchemeTemplateBO.class);
        } catch (Exception ex) {
            log.error("应收账期方案快照反序列化失败, snapshot:{},cause:{}", contractDetail.getPriceScheme(), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * sku快照
     *
     * @param yzContractDetail
     * @return
     */
    private static SkuBOExt buildSkuSnapshot(YzContractDetail yzContractDetail) {
        if (StringUtils.isBlank(yzContractDetail.getSkuSnapshot())) {
            return null;
        }
        try {
            SkuBOExt skuSnapshot = JSON.parseObject(yzContractDetail.getSkuSnapshot(), SkuBOExt.class);
            if (skuSnapshot != null) {
                SkuBOExt skuBO = DS.findById(SkuBOExt.class, skuSnapshot.getId(), "*,item.*");
                // 不执行兼容，为空则填空
                String itemTranscript = Optional.ofNullable(skuBO).map(SkuBO::getItem).map(ItemBO::getItemTranscript).orElse(null);
                if (skuSnapshot.getItem() == null) {
                    ItemBO itemBO = new ItemBO();
                    itemBO.setItemTranscript(itemTranscript);
                    skuSnapshot.setItem(itemBO);
                } else {
                    if (skuSnapshot.getItem().getItemTranscript() == null) {
                        skuSnapshot.getItem().setItemTranscript(itemTranscript);
                    }
                }
            }
            return skuSnapshot;
        } catch (Exception ex) {
            log.error("SKU快照反序列化失败, snapshot:{},cause:{}", yzContractDetail.getSkuSnapshot(), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 品牌
     * @param detail
     * @return
     */
    private static List<BrandBO> buildBidBrand(YzContractDetail detail) {
        if (CollectionUtils.isEmpty(detail.getBiddingBrand())) {
            return null;
        }

        try {
            Set<String> brandCodes = detail.getBiddingBrand().stream().map(e -> e.getString("brandCode"))
                    .filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(brandCodes)) {
                List<BrandBO> brands = DS.findAll(BrandBO.class, "id,brandCode,brandName", "brandCode IN (?)", brandCodes);
                return brands;
            }
            return null;
        } catch (Exception ex) {
            log.error("招标品牌反序列化失败, snapshot:{},cause:{}", Json.toJson(detail.getBiddingBrand()), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    private static SkuBOExt buildSimpleSku(SkuBOExt sku) {
        SkuBOExt simpleSku = new SkuBOExt();
        simpleSku.setId(sku.getId());
        simpleSku.setSkuCode(sku.getSkuCode());
        simpleSku.setName(sku.getName());
        simpleSku.setThingSize(sku.getThingSize());
        simpleSku.setNewAttributes(sku.getNewAttributes());

        return simpleSku;
    }

    public static String convertType(Integer code) {
        if (code == null) {
            return null;
        }
        switch (code) {
            case 1:
                return YzContractTypeDict.SUB_SUPPLY;
            case 2:
                return YzContractTypeDict.RENT;
            case 3:
                return YzContractTypeDict.PROFESSION_SUBCONTRACT;
            case 4:
                return YzContractTypeDict.LABOR_SUBCONTRACT;
            case 5:
                return YzContractTypeDict.PROJECT_MATERIAL;
            case 6:
                return YzContractTypeDict.FARMER_BENEFITS;

        }
        return String.valueOf(code);
    }

    public static String convertStatus(Integer code) {
        if (code == null) {
            return null;
        }
        switch (code) {
            case 1:
                return YzContractStatusDict.ACTIVE;
            case -1:
                return YzContractStatusDict.STOP;
        }
        return null;
    }

    public static String convertDetailStatus(Integer code) {
        if (code == null) {
            return null;
        }
        switch (code) {
            case 1:
                return YzContractStatusDict.ACTIVE;
            case -1:
                return YzContractStatusDict.STOP;
        }
        return null;
    }

    /**
     *  //1:施工撮合
     *         //2:施工自营
     *         //3:地产自营
     *         //4:地产撮合
     * @param code
     * @return
     */
    public static String convertBusinessType(Integer code) {
        if (code == null) {
            return null;
        }
        switch (code) {
            case 1:
                return YzContractBusinessTypeDict.BUILD_MATCH_MAKING;
            case 2:
                return YzContractBusinessTypeDict.BUILD_SELF;
            case 3:
                return YzContractBusinessTypeDict.ESTATE_SELF;
            case 4:
                return YzContractBusinessTypeDict.ESTATE_MATCH_MAKING;
        }
        return null;
    }
}
