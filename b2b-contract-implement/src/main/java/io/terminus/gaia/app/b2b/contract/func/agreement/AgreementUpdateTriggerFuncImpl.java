package io.terminus.gaia.app.b2b.contract.func.agreement;

import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.util.AgreementTriggerHelper;
import io.terminus.gaia.app.b2b.item.model.CategoryAgreementConfigBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2024/1/29 15:26
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class AgreementUpdateTriggerFuncImpl implements AgreementUpdateTriggerFunc {
    @Override
    public void execute(AgreementBO before) {
        if (Objects.isNull(before) || Objects.isNull(before.getId())) {
            return;
        }

        AgreementBO agreementBO = DS.findOne(AgreementBO.class, "*,category.*,departments.*,coverAreas.*,paymentSchemes.*",
                "id = ?", before.getId());
        boolean basicInfoSatisfy = AgreementTriggerHelper.validateRequired(agreementBO);

        AgreementBO agreementNew = new AgreementBO();
        agreementNew.setId(agreementBO.getId());
        if (!basicInfoSatisfy) {
            agreementNew.setMaintainCompleted(false);
            DS.update(agreementNew);
        } else {
            // 基础信息&&价格方案配置
            CategoryAgreementConfigBO config = AgreementTriggerHelper.getCategoryAgreementConfig(agreementBO);
            AgreementTriggerHelper.validateConfig(config, agreementNew);
        }
    }
}
