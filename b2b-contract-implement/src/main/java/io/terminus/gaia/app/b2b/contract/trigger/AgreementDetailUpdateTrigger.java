package io.terminus.gaia.app.b2b.contract.trigger;

import io.terminus.gaia.app.b2b.contract.dict.AgreementDetailStatusDict;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.util.AgreementTriggerHelper;
import io.terminus.gaia.app.b2b.item.model.CategoryAgreementConfigBO;
import io.terminus.trantorframework.api.annotation.Trigger;
import io.terminus.trantorframework.sdk.sql.DS;
import io.terminus.trantorframework.sdk.trigger.AddEvent;
import io.terminus.trantorframework.sdk.trigger.AddEventTriggerListener;
import io.terminus.trantorframework.sdk.trigger.UpdateEvent;
import io.terminus.trantorframework.sdk.trigger.UpdateEventTriggerListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023-08-17
 * @descrition
 */
@Slf4j
@RequiredArgsConstructor
@Trigger(modelClass = AgreementDetailBO.class, listenFields = {AgreementDetailBO.priceSchemeBO_field, AgreementDetailBO.statusDict_field
})
public class AgreementDetailUpdateTrigger implements UpdateEventTriggerListener<AgreementDetailBO> {

    @Override
    public void execute(UpdateEvent<AgreementDetailBO> updateEvent) {
        AgreementBO before = updateEvent.getBefore().getAgreementBO();

        if (!updateEvent.getBefore().getStatusDict().equals(AgreementDetailStatusDict.START_USING)) {
            return;
        }

        AgreementBO agreementBO = DS.findOne(AgreementBO.class, "*,category.*,departments.*,coverAreas.*,paymentSchemes.*",
                "id = ?", before.getId());
        boolean basicInfoSatisfy = AgreementTriggerHelper.validateRequired(agreementBO);

        if (!basicInfoSatisfy) {
            // 如果已经是false了，就不重新更新
            if (agreementBO.getMaintainCompleted() != false) {
                agreementBO.setMaintainCompleted(false);
                DS.update(agreementBO);
            }
        } else {
            // 基础信息&&价格方案配置
            CategoryAgreementConfigBO config = AgreementTriggerHelper.getCategoryAgreementConfig(agreementBO);
            AgreementTriggerHelper.validateConfig(config, agreementBO);
        }
    }

}
