package io.terminus.gaia.app.b2b.contract.spring.web;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Splitter;
import io.terminus.gaia.app.b2b.contract.dict.AgreementStatusDict;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.util.AgreementTriggerHelper;
import io.terminus.gaia.app.b2b.item.model.CategoryAgreementConfigBO;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-21
 * @descrition
 */
@Slf4j
@RestController
@RequestMapping("/maintain")
@RequiredArgsConstructor
public class MaintainCompletedApi {


    @GetMapping("/multi")
    public void multi(@RequestParam String codesStr) {


        if (StrUtil.isBlank(codesStr)) {
            return;
        }

        List<String> codes = Splitter.on(",").splitToList(codesStr);

        List<AgreementBO> agreementList = DS.findAll(AgreementBO.class, "*,category.*,departments.*,coverAreas.*,paymentSchemes.*",
                "code in (?) and `status` != ?",
                codes, AgreementStatusDict.DROP);

        for (AgreementBO agreementBO : agreementList) {

            Boolean maintainCompletedBefore = agreementBO.getMaintainCompleted();

            boolean basicInfoSatisfy = AgreementTriggerHelper.validateRequired(agreementBO);

            if (!basicInfoSatisfy) {
                agreementBO.setMaintainCompleted(false);
                DS.update(agreementBO);
            } else {
                // 基础信息&&价格方案配置
                CategoryAgreementConfigBO config = AgreementTriggerHelper.getCategoryAgreementConfig(agreementBO);
                AgreementTriggerHelper.validateConfig(config, agreementBO);
            }

            Boolean maintainCompletedAfter = agreementBO.getMaintainCompleted();

            log.info("agreement [{}] maintainCompleted before is {}, after is {}", agreementBO.getCode(), maintainCompletedBefore, maintainCompletedAfter);

        }

    }
}
