package io.terminus.gaia.app.b2b.contract.spring.validator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import io.terminus.gaia.app.b2b.contract.dict.*;
import io.terminus.gaia.app.b2b.contract.model.*;
import io.terminus.gaia.app.b2b.contract.tmodel.PayableSchemeTO;
import io.terminus.gaia.app.b2b.contract.util.B2bContractUtil;
import io.terminus.gaia.app.b2b.item.model.CategoryAgreementConfigBO;
import io.terminus.gaia.common.utils.ValidationChecker;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AgreementValidator {

    /**
     * 创建校验
     */
    public void checkCreate(AgreementBO agreementBO) {
        checkSave(agreementBO);
    }

    private void checkSave(AgreementBO agreementBO) {
        ValidationChecker.checkNotBlank(agreementBO.getName(), "协议名称不能为空");
        ValidationChecker.checkNonNull(agreementBO.getJfCompanyBO(), "协议甲方不能为空");
        ValidationChecker.checkNotEmpty(agreementBO.getDepartments(), "使用单位不能为空");
//        ValidationChecker.checkNotEmpty(agreementBO.getProjectTypes(), "适用项目类型不能为空");
        ValidationChecker.checkNonNull(agreementBO.getYfCompanyBO(), "协议乙方不能为空");
        ValidationChecker.checkNonNull(agreementBO.getCategory(), "协议分类不能为空");
        ValidationChecker.checkNonNull(agreementBO.getCoverAreaType(), "覆盖范围不能为空");
        ValidationChecker.checkNonNull(agreementBO.getSignAt(), "签约时间不能为空");
        ValidationChecker.checkNonNull(agreementBO.getEffectiveAt(), "生效时间不能为空");
        ValidationChecker.checkNonNull(agreementBO.getExpireAt(), "终止时间不能为空");

        if (StringUtils.isBlank(agreementBO.getType())) {
            // 协议类型
            agreementBO.setType(B2bContractUtil.calcAgreementType(agreementBO));
        }

        ValidationChecker.checkNonNull(agreementBO.getType(), "协议类型不能为空");

        if (AgreementTypeDict.SELF.equals(agreementBO.getType())
                && agreementBO.getProjectTypes().contains(AgreementProjectTypeDict.CONSTRUCTION)
                && Objects.equals(agreementBO.getIsSupply(), false)) {
            ValidationChecker.checkNonNull(agreementBO.getTaxAmt(), "含税金额不能为空");
            ValidationChecker.checkNonNull(agreementBO.getNoTaxAmt(), "不含税金额不能为空");
            ValidationChecker.checkNonNull(agreementBO.getTaxPrc(), "税额不能为空");
            ValidationChecker.checkNonNull(agreementBO.getTaxRateBO(), "税额不能为空");
            ValidationChecker.checkArgument(NumberUtil.equals(agreementBO.getTaxPrc().add(agreementBO.getNoTaxAmt()), agreementBO.getTaxAmt()),
                    "协议金额错误");
        }

        if (AgreementTypeDict.SELF.equals(agreementBO.getType())) {
            ValidationChecker.checkNonNull(agreementBO.getPriceMode(), "计价模式不能为空");
        }

        String effectiveStr = DateUtil.formatDate(agreementBO.getEffectiveAt());
        String expireStr = DateUtil.formatDate(agreementBO.getExpireAt());

        if (expireStr.compareTo(effectiveStr) < 0) {
            throw new BusinessException("终止时间不能小于生效时间");
        }

        ValidationChecker.checkArgument(
                !agreementBO.getJfCompanyBO().getId().equals(agreementBO.getYfCompanyBO().getId()),
                "协议甲乙方不能重复");

        checkCoverArea(agreementBO.getCoverAreaType(), agreementBO.getCoverAreas());
        checkPayScheme(agreementBO.getPaymentSchemes());
        checkContacts(agreementBO.getContacts());
        checkDealers(agreementBO.getDealers());
        if (agreementBO.getType().equals(AgreementTypeDict.SELF)) {
            //todo 自营的定时账期基本设置为必填
            checkRegularReconciliations(agreementBO.getRegularReconciliations());
        }
        String bizType = agreementBO.getBizType();
        String rentPeriod = agreementBO.getRentPeriod();

        if (bizType.equals(YzContractTypeDict.RENT)) {
            if (StrUtil.isBlank(rentPeriod)) {
                throw new BusinessException("业务类型为租赁时，租赁报价方式不能为空");
            }
        }

        if (agreementBO.getType().equals(AgreementTypeDict.VIRTUAL)) {
            List<PayableSchemeTO> payableSchemeList = agreementBO.getPayableSchemeList();
            if (CollUtil.isEmpty(payableSchemeList)) {
                throw new BusinessException("协议类型为虚拟的需要维护应收账款信息");
            }
        }
    }

    private void checkRegularReconciliations(List<AgreementRegularReconciliationBO> regularReconciliations) {
        if (CollUtil.isNotEmpty(regularReconciliations)) {
            regularReconciliations.forEach(agreementRegularReconciliationBO -> {
                ValidationChecker.checkNonNull(agreementRegularReconciliationBO.getDay(), "自营协议定时账期基本设置中每个月定时对账日期（日）");
            });
        }

    }

    /**
     * 覆盖范围校验
     *
     * @param coverAreas
     */
    private void checkCoverArea(String coverAreaType, List<AgreementCoverAreaBO> coverAreas) {
        if (AgreementCoverAreaTypeDict.APPOINT.equals(coverAreaType)) {
            ValidationChecker.checkNotEmpty(coverAreas, "覆盖范围不能为空");
            coverAreas.forEach(coverArea -> {
                ValidationChecker.checkNonNull(coverArea.getRegion(), "大区不能为空");
            });
        }
    }

    /**
     * 付款方案校验
     *
     * @param schemes
     */
    private void checkPayScheme(List<PaymentSchemeBO> schemes) {
        if (CollectionUtils.isEmpty(schemes)) {
            throw new BusinessException("付款方案不能为空！");
        }

        schemes.forEach(scheme -> {
            ValidationChecker.checkNonNull(scheme.getSchemeNodes(), "付款节点不能为空");

//            Set<String> nodeSet = scheme.getSchemeNodes().stream().map(PaymentSchemeNodeTO::getNodeDict).collect(Collectors.toSet());
//            ValidationChecker.checkArgument(nodeSet.size() == scheme.getSchemeNodes().size(),
//                    "付款方案的款项类型不能重复");

            scheme.getSchemeNodes().forEach(schemeNode -> {
                ValidationChecker.checkNotBlank(schemeNode.getNodeDict(), "款项类型不能为空");
                ValidationChecker.checkNonNull(schemeNode.getRate(), "付款比例不能为空");
                ValidationChecker.checkNonNull(schemeNode.getTriggerTypeDict(), "触发方式不能为空");
                if (PaymentNodeTriggerTypeDict.AUTO.equals(schemeNode.getTriggerTypeDict())) {
                    ValidationChecker.checkNonNull(schemeNode.getTriggerDay(), "付款节点不能为空");
                }
            });
        });

    }

    /**
     * 联系人校验
     *
     * @param contacts
     */
    private void checkContacts(List<AgreementContactBO> contacts) {
        if (CollectionUtils.isEmpty(contacts)) {
            return;
        }
        contacts.forEach(contact -> {
            ValidationChecker.checkNonNull(contact.getPerson(), "联系人不能为空");
            ValidationChecker.checkNonNull(contact.getPhone(), "联系方式不能为空");
        });
    }

    /**
     * 经销商校验
     *
     * @param dealers
     */
    private void checkDealers(List<AgreementDealerBO> dealers) {
        if (CollectionUtils.isEmpty(dealers)) {
            return;
        }
        dealers.forEach(dealer -> {
            ValidationChecker.checkNonNull(dealer.getDealer(), "经销商不能为空");
            ValidationChecker.checkNonNull(dealer.getRegion(), "经销商服务大区不能为空");
            ValidationChecker.checkNotEmpty(dealer.getDistricts(), "经销商服务省市区不能为空");
        });
    }

    /**
     * 编辑校验
     */
    public void checkEdit(AgreementBO newAgreement, AgreementBO existAgreement) {
        if (!AgreementSourceDict.MANUAL.equals(existAgreement.getSourceDict())) {
            throw new BusinessException("仅创建方式【手动】的协议可以编辑");
        }
        checkSave(newAgreement);
    }

    /**
     * 删除校验
     */
    public void checkDelete(AgreementBO newAgreement, AgreementBO existAgreement) {
        if (!AgreementSourceDict.MANUAL.equals(existAgreement.getSourceDict())) {
            throw new BusinessException("仅创建方式【手动】的协议可以删除");
        }
    }

    /**
     * 启用校验
     */
    public void checkEnable(AgreementBO newAgreement, AgreementBO existAgreement) {
        List<String> expects = ListUtil.toList(AgreementStatusDict.DISABLED,
                AgreementStatusDict.DRAFT, AgreementStatusDict.ENABLED_AUDIT);
        if (!expects.contains(existAgreement.getStatus())) {
            throw new BusinessException("仅【草稿/停用】状态的协议可以启用");
        }
        boolean isRequired = validateRequired(existAgreement);
        if (!isRequired) {
            throw new BusinessException("仅维护完整的协议可以启用");
        }

        Long categoryId = existAgreement.getCategory().getId();

        String idPath = existAgreement.getCategory().getIdPath();

        List<CategoryAgreementConfigBO> configs = DS.findAll(CategoryAgreementConfigBO.class,
                "*", "categoryBO = ? order by updatedAt desc ", categoryId);

        if (CollUtil.isEmpty(configs)) {
            Stack<Long> stack = getIdPath(idPath, categoryId);
            while (!stack.isEmpty()) {
                Long parentId = stack.pop();
                List<CategoryAgreementConfigBO> parentConfigs = DS.findAll(CategoryAgreementConfigBO.class,
                        "*", "categoryBO = ? order by updatedAt desc ", parentId);
                if (CollUtil.isNotEmpty(parentConfigs)) {
                    Integer minRequiredQty = parentConfigs.get(0).getMinRequiredQty();
                    validateDetailEnableNum(existAgreement, minRequiredQty);
                }
            }
        } else {
            Integer minRequiredQty = configs.get(0).getMinRequiredQty();
            validateDetailEnableNum(existAgreement, minRequiredQty);
        }

    }

    private void validateDetailEnableNum(AgreementBO existAgreement, Integer minRequiredQty) {
        AgreementBO mainAgreement;
        if (Objects.nonNull(existAgreement.getMainAgreement())) {
            mainAgreement = DS.findById(AgreementBO.class, existAgreement.getMainAgreement().getId());
        } else {
            mainAgreement = existAgreement;
        }
        if (minRequiredQty != null) {
            List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class, "id", "spuBO is not null and statusDict != ? and agreementBO = ?",
                    AgreementDetailStatusDict.LOSE_EFFICACY, mainAgreement.getId());
            if (details.size() < minRequiredQty) {
                throw new BusinessException("协议清单中未失效的清单行数量未达到协议关联分类配置的最低要求数量");
            }
        }
    }

    private boolean validateRequired(AgreementBO agreement) {

        boolean a = StrUtil.isNotBlank(agreement.getName());

        boolean b = agreement.getJfCompanyBO() != null;

        boolean c = agreement.getYfCompanyBO() != null;

        boolean d = CollUtil.isNotEmpty(agreement.getDepartments());

        boolean e = agreement.getCategory() != null;


        boolean f = agreement.getBizType() != null;

        boolean g = false;
        if (agreement.getCoverAreaType() != null) {
            if (agreement.getCoverAreaType().equals(AgreementCoverAreaTypeDict.ALL)) {
                g = true;
            } else {
                g = CollUtil.isNotEmpty(agreement.getCoverAreas());
            }
        }

        boolean h = CollUtil.isNotEmpty(agreement.getPaymentSchemes());

        boolean i = agreement.getEffectiveAt() != null;

        boolean j = agreement.getExpireAt() != null;

        if (a && b && c && d && e && f && g && h && i && j) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 禁用校验
     */
    public void checkDisable(AgreementBO newAgreement, AgreementBO existAgreement) {
        List<String> expects = ListUtil.toList(AgreementStatusDict.ENABLED, AgreementStatusDict.DISABLED_AUDIT);
        if (!expects.contains(existAgreement.getStatus())) {
            throw new BusinessException("仅【启用】状态的协议可以停用");
        }
    }


    /**
     * 新建补充协议校验
     */
    public void checkSupplyCreate(AgreementBO supplyAgreement, AgreementBO mainAgreement) {
        checkSave(supplyAgreement);
    }

    /**
     * 新建分子公司协议校验
     */
    public void checkSubCompanyCreate(AgreementBO subCompanyAgreement, AgreementBO relateAgreement) {
        checkSave(subCompanyAgreement);
    }

    /**
     * 维护协议信息
     *
     * @param newAgreement
     * @param existAgreement
     */
    public void checkEditExtendInfo(AgreementBO newAgreement, AgreementBO existAgreement) {
        if (AgreementSourceDict.MANUAL.equals(existAgreement.getSourceDict())) {
            throw new BusinessException("仅创建方式=同步协议可以维护协议信息");
        }
        checkSave(newAgreement);
    }

    /**
     * 批量维护协议信息
     *
     * @param request
     */
    public void checkBatchEditExtendInfo(AgreementBO request) {
        ValidationChecker.checkNotEmpty(request.getSelectData(), "选中协议不能为空");
        request.getSelectData().forEach(selectData -> {
            ValidationChecker.checkNonNull(selectData.getId(), "选中的协议ID不能为空");
        });
        ValidationChecker.checkNonNull(request.getJfCompanyBO(), "协议甲方不能为空");
        ValidationChecker.checkNotEmpty(request.getDepartments(), "使用单位不能为空");
//        ValidationChecker.checkNotEmpty(request.getProjectTypes(), "适用项目类型不能为空");
        ValidationChecker.checkNonNull(request.getCategory(), "协议分类不能为空");
        ValidationChecker.checkNonNull(request.getSignAt(), "签约时间不能为空");
        ValidationChecker.checkNonNull(request.getEffectiveAt(), "生效时间不能为空");
        ValidationChecker.checkNonNull(request.getExpireAt(), "终止时间不能为空");

        checkCoverArea(request.getCoverAreaType(), request.getCoverAreas());
        checkPayScheme(request.getPaymentSchemes());

        List<Long> selectAgreementIds = request.getSelectData().stream().map(AgreementBO::getId).collect(Collectors.toList());
        Map<Long, AgreementBO> existMap = DS.findByIds(AgreementBO.class, selectAgreementIds)
                .stream().collect(Collectors.toMap(AgreementBO::getId, Function.identity()));

        request.getSelectData().forEach(selectData -> {
            AgreementBO existAgreement = existMap.get(selectData.getId());
            ValidationChecker.checkNonNull(existAgreement, StrUtil.format("协议【{}】不存在", selectData.getId()));
            if (Objects.nonNull(existAgreement.getType()) && existAgreement.getType().equals(AgreementTypeDict.VIRTUAL)) {
                List<PayableSchemeTO> payableSchemeList = request.getPayableSchemeList();
                if (CollUtil.isEmpty(payableSchemeList)) {
                    throw new BusinessException("协议类型为虚拟的需要维护应收账款信息");
                }
            }
            selectData.setExistAgreement(existAgreement);
        });


    }

    /**
     * 调整覆盖范围
     *
     * @param newAgreement
     * @param existAgreement
     */
    public void checkEditCoverArea(AgreementBO newAgreement, AgreementBO existAgreement) {
        List<String> expect = ListUtil.toList(
                AgreementStatusDict.ENABLED,
                AgreementStatusDict.DISABLED);
        if (!expect.contains(existAgreement.getStatus())) {
            throw new BusinessException("仅状态【启用/停用】的协议可以调整适用范围");
        }
        ValidationChecker.checkNonNull(newAgreement.getCoverAreaType(), "协议覆盖范围不能为空");
        checkCoverArea(newAgreement.getCoverAreaType(), newAgreement.getCoverAreas());
    }

    /**
     * 过期校验
     */
    public void checkExpire(AgreementBO newAgreement, AgreementBO existAgreement) {
        Date now = new Date();
        if (AgreementStatusDict.EXPIRED.equals(existAgreement.getStatus())) {
            throw new BusinessException("协议已过期");
        }
        if (now.before(existAgreement.getExpireAt())) {
            throw new BusinessException("协议未到过期时间");
        }
    }

    /**
     * 创建校验
     */
    public void checkTemplateCreateOrUpdate(AgreementTemplateBO agreementTemplateBO) {
        ValidationChecker.checkNotBlank(agreementTemplateBO.getName(), "协议名称不能为空");
        ValidationChecker.checkNonNull(agreementTemplateBO.getJfCompanyBO(), "协议甲方不能为空");
        ValidationChecker.checkNotEmpty(agreementTemplateBO.getDepartments(), "使用单位不能为空");
        //ValidationChecker.checkNotEmpty(agreementTemplateBO.getProjectTypes(), "适用项目类型不能为空");
        ValidationChecker.checkNonNull(agreementTemplateBO.getCategory(), "协议分类不能为空");
//        ValidationChecker.checkNonNull(agreementTemplateBO.getCoverAreaType(), "覆盖范围不能为空");
//        ValidationChecker.checkNonNull(agreementTemplateBO.getSignAt(), "签约时间不能为空");
//        ValidationChecker.checkNonNull(agreementTemplateBO.getEffectiveAt(), "生效时间不能为空");
//        ValidationChecker.checkNonNull(agreementTemplateBO.getExpireAt(), "终止时间不能为空");
        String effectiveStr = DateUtil.formatDate(agreementTemplateBO.getEffectiveAt());
        String expireStr = DateUtil.formatDate(agreementTemplateBO.getExpireAt());
        if (StrUtil.isNotBlank(effectiveStr)
                && StrUtil.isNotBlank(expireStr)
                && expireStr.compareTo(effectiveStr) < 0) {
            throw new BusinessException("终止时间不能小于生效时间");
        }

        //范围校验
        if (Objects.nonNull(agreementTemplateBO.getCoverAreaType())) {
            checkCoverAreaForTemplate(agreementTemplateBO.getCoverAreaType(), agreementTemplateBO.getCoverAreas());

        }

        String bizType = agreementTemplateBO.getBizType();
        String rentPeriod = agreementTemplateBO.getRentPeriod();

        if (bizType.equals(YzContractTypeDict.RENT)) {
            if (StrUtil.isBlank(rentPeriod)) {
                throw new BusinessException("业务类型为租赁时，租赁报价方式不能为空");
            }
        }

        //清单校验
        List<AgreementDetailTemplateBO> details = agreementTemplateBO.getDetails();
        if (CollectionUtils.isNotEmpty(details)){
            ArrayList<Long> spuIds = ListUtil.toList();
            for (AgreementDetailTemplateBO detail : details) {
                ValidationChecker.checkNonNull(detail.getSpuBO(), "标品不能为空");
                if (spuIds.contains(detail.getSpuBO().getId())){
                    throw new BusinessException("多个清单不能绑定相同spu!");
                }
                spuIds.add(detail.getSpuBO().getId());
            }
        }

    }

    /**
     * 覆盖范围校验
     *
     * @param coverAreas
     */
    private void checkCoverAreaForTemplate(String coverAreaType, List<AgreementTemplateCoverAreaBO> coverAreas) {
        if (AgreementCoverAreaTypeDict.APPOINT.equals(coverAreaType)) {
            ValidationChecker.checkNotEmpty(coverAreas, "覆盖范围不能为空");
            coverAreas.forEach(coverArea -> {
                ValidationChecker.checkNonNull(coverArea.getRegion(), "大区不能为空");
            });
        }
    }


    public Stack getIdPath(String idPath, Long id) {

        String[] stringArray = idPath.split("/");

        Stack<Long> stack = new Stack<>();
        for (String str : stringArray) {
            if (str.equals(String.valueOf(id))) {
                continue;
            }
            stack.push(Long.parseLong(str));
        }

        return stack;
    }
}
