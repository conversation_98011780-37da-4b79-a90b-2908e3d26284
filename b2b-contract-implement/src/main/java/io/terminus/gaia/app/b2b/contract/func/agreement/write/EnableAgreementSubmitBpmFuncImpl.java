package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import io.terminus.gaia.app.b2b.contract.dict.AgreementCoverAreaTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementTypeDict;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementBpmBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementCoverAreaBO;
import io.terminus.gaia.app.b2b.contract.spring.validator.AgreementValidator;
import io.terminus.gaia.common.open.bpm.client.BpmClient;
import io.terminus.gaia.common.utils.EnhanceDS;
import io.terminus.gaia.contract.dict.common.ApproveStatusDict;
import io.terminus.gaia.md.dict.SubmitZJBPMCodeDict;
import io.terminus.gaia.md.tmodel.request.BpmRequestTO;
import io.terminus.gaia.md.tmodel.response.BpmResponseTO;
import io.terminus.gaia.organization.context.UserCompanyContext;
import io.terminus.gaia.organization.context.UserInfoContext;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.gaia.settlement.dict.bpm.ZhiYuanInvoiceEnum;
import io.terminus.gaia.settlement.dict.bpm.ZhiYuanPriceCalRuleEnum;
import io.terminus.gaia.settlement.dict.bpm.ZhiYuanYesEnum;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.json.Json;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class EnableAgreementSubmitBpmFuncImpl implements EnableAgreementSubmitBpmFunc {
    private final AgreementValidator agreementValidator;
    private final UserCompanyContext userCompanyContext;
    private final UserInfoContext userInfoContext;

    @Override
    public BooleanResult execute(AgreementBO agreementBO) {
        AgreementBO existAgreement = EnhanceDS.safeFindOne(AgreementBO.class,
                "*,category.*,coverAreas.*,coverAreas.region.districtName,coverAreas.districtStr,paymentSchemes.*,yfCompanyBO.*,jfCompanyBO.*,taxRateBO.*, agreementBpmBO.*",
                "id = ?", agreementBO.getId());
        agreementValidator.checkEnable(agreementBO, existAgreement);
        Map<String, Object> data = new HashMap<>();
        //主要信息表
        Map<String, Object> formmain_4835 = this.buildFormMain4835(existAgreement);
        data.put("formmain_4835", formmain_4835);
        //覆盖范围明细表
        List<Map<String, Object>> formson_5325 = this.buildFormSon5325(existAgreement);
        data.put("formson_5325", formson_5325);//覆盖范围明细表
        //附件明细表
        List formson_4836 = this.buildFormSon4836(existAgreement);
        data.put("formson_4836", formson_4836);//附件明细表

        BpmRequestTO bpmRequestTO = new BpmRequestTO();
        bpmRequestTO.setData(data);
        String templateCode = this.buildTemplateCode(existAgreement);
        bpmRequestTO.setTemplateCode(templateCode);
        //draft: 发送0，待发1
        bpmRequestTO.setDraft("0");
        bpmRequestTO.setLoginName(userInfoContext.getUserInfo().getUser().getUsername());
        if (existAgreement.getAgreementBpmBO() != null && StringUtils.isNotEmpty(existAgreement.getAgreementBpmBO().getBpmSubjectTitle())) {
            bpmRequestTO.setSubject(existAgreement.getAgreementBpmBO().getBpmSubjectTitle());
        } else {
            String subjectTitle = StrUtil.format("商城自营采购合同审批单/ 乙方（{}）", existAgreement.getYfCompanyBO().getEntityName()) +
                    StrUtil.format("/合同编号（{}）", existAgreement.getCode());
            bpmRequestTO.setSubject(subjectTitle);
        }

        //提交审批
        log.info("SubmitEnableAgreementApplyFuncImpl:BpmClient.param:{}", JSON.toJSON(bpmRequestTO));
        BpmResponseTO call = BpmClient.call(bpmRequestTO);
        log.error("bpm返回信息:{}", Json.toJson(call));
        if (!call.getIsSuccess()) {
            throw new BusinessException("发起审批异常:" + call.getMessage());
        }
        String result = call.getAppBussinessData();
        JSONObject jsonObj = JSONUtil.parseObj(result);
        //更新框架协议状态
        if (jsonObj.containsKey("summaryId") && StringUtils.isNotBlank(jsonObj.getStr("summaryId"))) {
            AgreementBO updateAgreementBo = new AgreementBO();
            updateAgreementBo.setId(agreementBO.getId());
            updateAgreementBo.setSummaryId(jsonObj.getStr("summaryId"));
            updateAgreementBo.setSummaryName(userInfoContext.getUserInfo().getUser().getUsername());
            updateAgreementBo.setApproveStatusDict(ApproveStatusDict.APPROVING);
            updateAgreementBo.setStatus(AgreementStatusDict.ENABLED_AUDIT);
            updateAgreementBo.setStatusBefore(existAgreement.getStatus());
            updateAgreementBo.setOperator(userInfoContext.getUserInfo().getEmployee());
            DS.update(updateAgreementBo);
            return BooleanResult.TRUE;
        }
        return BooleanResult.FALSE;
    }

    @NotNull
    private List buildFormSon4836(AgreementBO existAgreement) {
        List formson_4836 = new ArrayList<>();
        if (existAgreement.getAttachment() != null && CollUtil.isNotEmpty(existAgreement.getAttachment().getFiles())) {
            List<Attachment.File> files = existAgreement.getAttachment().getFiles();
            for (int i = 1; i <= files.size(); i++) {
                Map<String, Object> map = Maps.newHashMap();
                map.put("序号1", i);

                String fileValue = "";
                if (files.get(i - 1) != null) {
                    fileValue = fileValue + files.get(i - 1).getName()
                            + "（点链接下载：https:" + files.get(i - 1).getUrl() + "）";
                }
                map.put("附件名称", fileValue);//合同审批相关资料
                formson_4836.add(map);
            }
        }
        return formson_4836;
    }

    @NotNull
    private List<Map<String, Object>> buildFormSon5325(AgreementBO existAgreement) {
        List<Map<String, Object>> formson_5325 = new ArrayList<>();
        if (AgreementCoverAreaTypeDict.APPOINT.equals(existAgreement.getCoverAreaType()) &&
                !CollectionUtils.isEmpty(existAgreement.getCoverAreas())) {
            for (int i = 0; i < existAgreement.getCoverAreas().size(); i++) {
                AgreementCoverAreaBO agreementCoverAreaBO = existAgreement.getCoverAreas().get(i);

                Map<String, Object> map = Maps.newHashMap();
                map.put("序号1", i + 1);
                map.put("大区", agreementCoverAreaBO.getRegion().getDistrictName());
                map.put("省市区", agreementCoverAreaBO.getDistrictStr());
                formson_5325.add(map);
            }
        }
        return formson_5325;
    }

    @NotNull
    private Map<String, Object> buildFormMain4835(AgreementBO existAgreement) {
        AgreementBpmBO agreementBpmBO = existAgreement.getAgreementBpmBO();
        Map<String, Object> formmain_4835 = Maps.newHashMap();
        formmain_4835.put("项目名称", "/");
        formmain_4835.put("公司", "智采公司");
        Optional.ofNullable(userCompanyContext.getCompanyInfo().getOrgId()).ifPresent(orgId -> {
            DepartmentBO departmentBO = DS.findById(DepartmentBO.class, orgId);
            if (departmentBO != null && StringUtils.isNotEmpty(departmentBO.getPath())) {
                formmain_4835.put("经办部门", departmentBO.getPath());
            }
        });
        formmain_4835.put("经办人", userInfoContext.getUserInfo().getUser().getNickname());
        formmain_4835.put("经办人手机号", userInfoContext.getUserInfo().getUser().getMobile());
        formmain_4835.put("合同类型", "采购合同");
        if (StringUtils.isNotEmpty(existAgreement.getName())) {
            formmain_4835.put("合同名称", existAgreement.getName());
        }
        if (existAgreement.getJfCompanyBO() != null) {
            formmain_4835.put("甲方单位", existAgreement.getJfCompanyBO().getEntityName());
        }
        if (existAgreement.getYfCompanyBO() != null) {
            formmain_4835.put("乙方单位", existAgreement.getYfCompanyBO().getEntityName());
        }
        if (existAgreement.getCategory() != null) {
            formmain_4835.put("品类", existAgreement.getCategory().getPath());
        }
        if (StringUtils.isNotEmpty(existAgreement.getCode())) {
            formmain_4835.put("商城合同编号", existAgreement.getCode());
        }
        if (agreementBpmBO != null && StringUtils.isNotEmpty(agreementBpmBO.getInnerContractCode())) {
            formmain_4835.put("内部合同编号", agreementBpmBO.getInnerContractCode());
        }
        if (StringUtils.isNotEmpty(existAgreement.getName())) {
            formmain_4835.put("合同周期", DateUtil.format(existAgreement.getEffectiveAt(), DatePattern.NORM_DATE_PATTERN) +
                    " 至 " + DateUtil.format(existAgreement.getExpireAt(), DatePattern.NORM_DATE_PATTERN));
        }
        if (agreementBpmBO != null && agreementBpmBO.getSupplementAgreement() != null && agreementBpmBO.getSupplementAgreement()) {
            formmain_4835.put("是否补充协议", ZhiYuanYesEnum.YES.getOaId());
            if (StringUtils.isNotEmpty(agreementBpmBO.getMainContractName())) {
                formmain_4835.put("原合同名称", agreementBpmBO.getMainContractName());
            }
            if (StringUtils.isNotEmpty(agreementBpmBO.getMainContractCode())) {
                formmain_4835.put("原合同编号", agreementBpmBO.getMainContractCode());
            }
        } else {
            formmain_4835.put("是否补充协议", ZhiYuanYesEnum.NO.getOaId());
            formmain_4835.put("原合同名称", "无");
            formmain_4835.put("原合同编号", "无");
        }

        if (agreementBpmBO != null){
            ZhiYuanPriceCalRuleEnum zhiYuanPriceCalRuleEnum = ZhiYuanPriceCalRuleEnum.getInstanceByLocalTag(agreementBpmBO.getPriceCalRule());
            if (zhiYuanPriceCalRuleEnum != null) {
                formmain_4835.put("计价规则", zhiYuanPriceCalRuleEnum.getOaId());
            }
            ZhiYuanInvoiceEnum zhiYuanInvoiceEnum = ZhiYuanInvoiceEnum.getInstanceByLocalTag(agreementBpmBO.getInvoiceTypeBpm());
            if (zhiYuanInvoiceEnum != null) {
                formmain_4835.put("票据类型", zhiYuanInvoiceEnum.getOaId());
            }
        }
        formmain_4835.put("签约金额-含税", existAgreement.getTaxAmt());
        formmain_4835.put("签约金额-不含税", existAgreement.getNoTaxAmt());
        if (existAgreement.getTaxRateBO() != null) {
            formmain_4835.put("税率", existAgreement.getTaxRateBO().getTaxRateShow());
        }
        formmain_4835.put("签约税额", existAgreement.getTaxPrc());
        if(agreementBpmBO != null){
            formmain_4835.put("保修比例",  agreementBpmBO.getMaintenanceRate() == null ? "0%" : agreementBpmBO.getMaintenanceRate() + "%");
            formmain_4835.put("履约保证金类型", agreementBpmBO.getPerformanceBondType());
            formmain_4835.put("付款方式", agreementBpmBO.getPayWay());
        }

        if (!CollectionUtils.isEmpty(existAgreement.getPaymentSchemes())) {
            formmain_4835.put("付款方案描述", existAgreement.getPaymentSchemes().get(0).getDescription());
        }
        if (AgreementCoverAreaTypeDict.ALL.equals(existAgreement.getCoverAreaType())) {
            formmain_4835.put("覆盖范围", "全部");
        } else {
            //指定覆盖范围，按列表展示
            formmain_4835.put("覆盖范围", "指定地区");
        }
        return formmain_4835;
    }

    @NotNull
    private String buildTemplateCode(AgreementBO existAgreement) {
        String templateCode = SubmitZJBPMCodeDict.AGREEMENT_CREATE;
        String bpmSuffix = null;
        if (AgreementTypeDict.SELF.equals(existAgreement.getType())) {
            bpmSuffix = "zcgylgs";
        } else {
            //bpmSuffix = queryBpmTemplateCodeFunc.execute(userCompanyContext.getCompanyInfo()).getTemplateCodeSuffix();
            log.error("失败：当前仅支持自营采购协议提交审批到致远OA！{}", existAgreement.getCode());
            throw new BusinessException("失败：仅自营采购协议需提交审批，请联系商城运营人员！");
        }
        if (StringUtils.isBlank(bpmSuffix)) {
            log.error("失败：当前用户的组织审批编码为空！{}", userCompanyContext.getCompanyInfo().getOrgId());
            throw new BusinessException("失败：请联系商城运营人员配置组织审批编码！");
        }
        templateCode = templateCode + "_" + bpmSuffix;
        return templateCode;
    }
}
