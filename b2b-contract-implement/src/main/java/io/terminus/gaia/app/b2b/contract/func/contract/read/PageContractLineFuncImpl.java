package io.terminus.gaia.app.b2b.contract.func.contract.read;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import io.terminus.gaia.app.b2b.contract.dict.YzContractTypeDict;
import io.terminus.gaia.app.b2b.contract.model.ContractLineBO;
import io.terminus.gaia.app.b2b.contract.model.YzContractBO;
import io.terminus.gaia.app.b2b.contract.model.query.QContractLineBO;
import io.terminus.gaia.app.b2b.item.model.SkuBOExt;
import io.terminus.gaia.app.b2b.trade.dict.EnterSceneStatusDict;
import io.terminus.gaia.app.b2b.trade.model.fulfillment.EnterSceneLineRecordBO;
import io.terminus.gaia.md.model.UnitBO;
import io.terminus.gaia.settlement.dict.settlement.LeaseSettleStatusDict;
import io.terminus.gaia.settlement.dict.settlement.LeaseSettlementTypeDict;
import io.terminus.gaia.settlement.model.settlement.LeaseSettlementOrderLineBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import io.terminus.trantorframework.sdk.sql.Sql;
import io.terminus.trantorframework.sql.QueryModelSqlConvertor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/7
 */
@FunctionImpl(name = "PageContractLineFuncImpl")
@RequiredArgsConstructor
@Slf4j
public class PageContractLineFuncImpl implements PageContractLineFunc {

    private final QueryModelSqlConvertor convertor;

    @Override
    public Paging<ContractLineBO> execute(QContractLineBO qContractLineBO) {

        Sql sql = convertor.toSql(qContractLineBO);
        sql.appendWhere("and adjustItem !=1");
        Paging<ContractLineBO> res = DS.paging(ContractLineBO.class, sql.getSelectSql(), sql.getWhereSql(), qContractLineBO.getQueryParams().getPage(), sql.getParams().toArray());

        if (CollUtil.isEmpty(res.getData())) {
            return res;
        }

        //List<ContractLineBO> contractLineBOS = res.getData().stream().filter(it -> ObjectUtil.notEqual(it.getAdjustItem(), true)).collect(Collectors.toList());
        List<ContractLineBO> contractLineBOS = res.getData();
        List<Long> skuIds = contractLineBOS.stream().map(ContractLineBO::getSku).filter(Objects::nonNull).map(SkuBOExt::getId).collect(Collectors.toList());
        List<SkuBOExt> skuBOExts = DS.findAll(SkuBOExt.class, "*,item.*", "id in (?)", skuIds);
        Map<Long, SkuBOExt> skuBOExtMap = skuBOExts.stream().collect(Collectors.toMap(SkuBOExt::getId, Function.identity()));

        // 租赁新增
        Map<Long, List<EnterSceneLineRecordBO>> enterMap = new HashMap<>();
        Map<Long, List<LeaseSettlementOrderLineBO>> leaseSettleMap = new HashMap<>();
        Map<Long, List<LeaseSettlementOrderLineBO>> depletionSettleMap = new HashMap<>();
        YzContractBO yzContractBO = DS.findById(YzContractBO.class, contractLineBOS.get(0).getContract().getId());
        if (YzContractTypeDict.RENT.equals(yzContractBO.getContractTypeDict())) {
            List<Long> contractLineIds = contractLineBOS.stream().map(ContractLineBO::getId).collect(Collectors.toList());
            // 进场数量 = ∑进场单 本次履约数量，取所有状态 ≠ 已取消 的进场单
            List<EnterSceneLineRecordBO> enterSceneLineRecordBOList = DS.findAll(EnterSceneLineRecordBO.class, "*", "contractLine in (?) and sceneOrder.enterSceneStatus != ?", ListUtil.toList(contractLineIds), EnterSceneStatusDict.CANCELED);
            if (CollectionUtil.isNotEmpty(enterSceneLineRecordBOList)) {
                enterMap = enterSceneLineRecordBOList.stream().collect(Collectors.groupingBy(EnterSceneLineRecordBO::getContractLineId));
            }
            // 耗损数量 = ∑结算单 本次耗损数量，取所有 状态 ≠ 已取消 的结算单
            List<LeaseSettlementOrderLineBO> leaseSettlementOrderLineBOList = DS.findAll(LeaseSettlementOrderLineBO.class, "*", "contractLineBO in (?) and leaseSettlementOrderBO.settleStatus != ?", ListUtil.toList(contractLineIds), LeaseSettleStatusDict.CANCEL);
            if (CollectionUtil.isNotEmpty(leaseSettlementOrderLineBOList)) {
                leaseSettleMap = leaseSettlementOrderLineBOList.stream().collect(Collectors.groupingBy(LeaseSettlementOrderLineBO::getContractLineId));
            }
            // 退场数量 = ∑退场结算单 本次履约数量（本次退场数量），取所有 类型 = 退场结算，状态 ≠ 已取消 的结算单
            List<LeaseSettlementOrderLineBO> depletionSettlementOrderLineBOList = DS.findAll(LeaseSettlementOrderLineBO.class, "*", "contractLineBO in (?) and leaseSettlementOrderBO.settleStatus != ? and leaseSettlementOrderBO.settlementType = ?", ListUtil.toList(contractLineIds), LeaseSettleStatusDict.CANCEL, LeaseSettlementTypeDict.EXIT_SETTLE);
            if (CollectionUtil.isNotEmpty(depletionSettlementOrderLineBOList)) {
                depletionSettleMap = depletionSettlementOrderLineBOList.stream().collect(Collectors.groupingBy(LeaseSettlementOrderLineBO::getContractLineId));
            }
        }



        // 查询库存状态
        for (ContractLineBO contractLineBO : contractLineBOS) {
            if (YzContractTypeDict.RENT.equals(yzContractBO.getContractTypeDict())) {
                // 进场数量
                BigDecimal enterQty = BigDecimal.ZERO;
                // 退场数量
                BigDecimal exitQty = BigDecimal.ZERO;
                // 耗损数量
                BigDecimal depletionQty = BigDecimal.ZERO;
                // 在场数量
                BigDecimal presentQty = BigDecimal.ZERO;
                if (CollectionUtil.isNotEmpty(enterMap.get(contractLineBO.getId()))) {
                    for (EnterSceneLineRecordBO lineRecordBO : enterMap.get(contractLineBO.getId())) {
                        enterQty = enterQty.add(lineRecordBO.getRequestQty());
                    }
                }
                if (CollectionUtil.isNotEmpty(depletionSettleMap.get(contractLineBO.getId()))) {
                    for (LeaseSettlementOrderLineBO leaseSettlementOrderLineBO : depletionSettleMap.get(contractLineBO.getId())) {
                        exitQty = exitQty.add(leaseSettlementOrderLineBO.getCurrentPromiseQty());
                    }
                }
                if (CollectionUtil.isNotEmpty(leaseSettleMap.get(contractLineBO.getId()))) {
                    for (LeaseSettlementOrderLineBO leaseSettlementOrderLineBO : leaseSettleMap.get(contractLineBO.getId())) {
                        depletionQty = depletionQty.add(leaseSettlementOrderLineBO.getCurrentDepletionQty());
                    }
                }
                // 在场数量 = 进场数量 - 退场数量 - 耗损数量
                presentQty = enterQty.subtract(exitQty).subtract(depletionQty);
                contractLineBO.setPlacedQty(presentQty);
            }

            SkuBOExt withInventoryStatus = skuBOExtMap.get(contractLineBO.getSku().getId());
            if (withInventoryStatus != null) {
                contractLineBO.setSku(withInventoryStatus);
            }
            handleAttribute(contractLineBO);
        }

        res.setData(contractLineBOS);
        return res;
    }
    
    private void handleAttribute(ContractLineBO contractLineBO) {
        try {
            contractLineBO.getSkuJson().getNewAttributes().stream().forEach(newAttribute -> {
                UnitBO unit = newAttribute.getUnit();
                if (Objects.isNull(unit)) {
                    return;
                }
                List<UnitBO> unitShortCodeList = DS.findAll(UnitBO.class, "unitShortCode", "unitName=?", unit.getUnitName());
                if (CollectionUtils.isNotEmpty(unitShortCodeList)) {
                    unit.set("unitShortCode", unitShortCodeList.get(0).getUnitShortCode());
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
