package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QRequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.model.query.QRequestPurchaseLineBO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.md.model.query.QEntityBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.querymodel.Select;
import io.terminus.trantorframework.querymodel.type.support.QLongId;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * 设置中标供应商实现
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class SetWinSupplierFuncImpl implements SetWinSupplierFunc {

    @Override
    public BooleanResult execute(RequestPurchaseBO requestPurchaseBO) {
        if (Objects.isNull(requestPurchaseBO) || Objects.isNull(requestPurchaseBO.getId())) {
            throw new BusinessException("需求单不能为空");
        }

        final EntityBO supplierBO = requestPurchaseBO.getWinSupplierBO();
        if (Objects.isNull(supplierBO) || Objects.isNull(supplierBO.getId())) {
            throw new BusinessException("供应商不能为空");
        }

        // 查询该需求单下指定供应商的需求明细，获取品牌信息
        QRequestPurchaseLineBO qRequestPurchaseLineBO = new QRequestPurchaseLineBO();
        QRequestPurchaseBO qRequestPurchaseBO = new QRequestPurchaseBO();
        qRequestPurchaseBO.setId(new QLongId(requestPurchaseBO.getId()));
        qRequestPurchaseLineBO.setRequestPurchaseBO(qRequestPurchaseBO);
        
        QEntityBO qEntityBO = new QEntityBO();
        qEntityBO.setId(new QLongId(supplierBO.getId()));
        qRequestPurchaseLineBO.setSupplier(qEntityBO);
        Select select = new Select();
        select.addField("*")
                .addField(RequestPurchaseLineBO.brandBO_field, new Select.Field(BrandBO.id_field));
        qRequestPurchaseLineBO.getQueryParams().setSelect(select);
        
        List<RequestPurchaseLineBO> requestPurchaseLines = DS.findAll(qRequestPurchaseLineBO);
        
        BrandBO winBrandBO = null;
        if (!requestPurchaseLines.isEmpty()) {
            // 获取第一条数据中的品牌
            RequestPurchaseLineBO firstLine = requestPurchaseLines.get(0);
            winBrandBO = firstLine.getBrandBO();
        }
        
        // 更新需求单的中标供应商和中标品牌
        RequestPurchaseBO update = new RequestPurchaseBO();
        update.setId(requestPurchaseBO.getId());
        update.setWinSupplierBO(supplierBO);
        update.setWinBrandBO(winBrandBO);
        
        DS.update(update);
        
        log.info("设置中标供应商成功，需求单ID: {}, 供应商ID: {}, 品牌ID: {}", 
                requestPurchaseBO.getId(), supplierBO.getId(), 
                winBrandBO != null ? winBrandBO.getId() : null);
        
        return BooleanResult.TRUE;
    }
} 