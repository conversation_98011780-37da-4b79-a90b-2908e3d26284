package io.terminus.gaia.app.b2b.contract.func.purchase;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import io.terminus.gaia.app.b2b.contract.func.purchase.ImportRequestPurchaseProfitFunc;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseProfitExportTO;
import io.terminus.gaia.app.b2b.contract.tmodel.RequestPurchaseProfitUploadTO;
import io.terminus.taurus.common.exception.BizException;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 导入需求销售价格实现
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
public class ImportRequestPurchaseProfitFuncImpl implements ImportRequestPurchaseProfitFunc {

    @Override
    public BooleanResult execute(RequestPurchaseProfitUploadTO uploadTO) {
        log.info("开始导入需求销售价格，参数: {}", JSON.toJSONString(uploadTO));

        if (ObjectUtil.isEmpty(uploadTO) || ObjectUtil.isEmpty(uploadTO.getProfitFile())) {
            throw new BizException("参数不能为空");
        }

        if (uploadTO.getRequestPurchaseId() == null) {
            throw new BizException("需求单ID不能为空");
        }

        List<Attachment.File> files = uploadTO.getProfitFile().getFiles();
        if (CollectionUtils.isEmpty(files)) {
            throw new BizException("文件列表为空，请重新上传");
        }

        // 检查文件后缀
        if (!checkExcelFileExtension(files.get(0).getName())) {
            throw new BizException("请检查文件格式或者文件类型，只支持Excel文件上传");
        }

        String filePath = "http:" + files.get(0).getUrl();
        
        try {
            log.info("要请求的地址: {}", filePath);
            URL url = new URL(filePath);
            InputStream inputStream = url.openStream();

            // 读取Excel文件，支持多sheet
            List<RequestPurchaseProfitExportTO> allImportData = new ArrayList<>();
            
            // 获取所有sheet的数据
            EasyExcel.read(inputStream, RequestPurchaseProfitExportTO.class, new ProfitDataListener(allImportData))
                    .doReadAll();

            log.info("导入的数据为: {}", JSON.toJSONString(allImportData));

            if (CollectionUtils.isEmpty(allImportData)) {
                log.error("解析出来的文件是空的");
                throw new BizException("导入文件为空，请检查文件内容");
            }

            // 处理导入数据
            processImportData(uploadTO.getRequestPurchaseId(), allImportData);

            return BooleanResult.TRUE;

        } catch (MalformedURLException e) {
            log.error("文件URL格式错误: {}", filePath, e);
            throw new BizException("文件URL格式错误");
        } catch (IOException e) {
            log.error("读取文件失败", e);
            throw new BizException("读取文件失败: " + e.getMessage());
        }
    }

    /**
     * 处理导入数据
     */
    private void processImportData(Long requestPurchaseId, List<RequestPurchaseProfitExportTO> importData) {
        // 提取所有需要更新的明细ID
        List<Long> lineIds = importData.stream()
                .map(RequestPurchaseProfitExportTO::getLineId)
                .filter(id -> id != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(lineIds)) {
            throw new BizException("导入数据中没有找到有效的明细ID");
        }

        // 查询现有的明细数据
        String whereClause = "id IN (" + lineIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + 
                           ") AND requestPurchaseBO.id = ?";
        List<RequestPurchaseLineBO> existingLines = DS.findAll(RequestPurchaseLineBO.class, "*", whereClause, requestPurchaseId);
        Map<Long, RequestPurchaseLineBO> existingLineMap = existingLines.stream()
                .collect(Collectors.toMap(RequestPurchaseLineBO::getId, line -> line));

        log.info("查询到{}条现有明细数据", existingLines.size());

        // 更新数据
        List<RequestPurchaseLineBO> updateList = new ArrayList<>();
        StringBuilder validationErrors = new StringBuilder();
        
        for (int i = 0; i < importData.size(); i++) {
            RequestPurchaseProfitExportTO importItem = importData.get(i);
            
            // 验证明细ID
            if (importItem.getLineId() == null) {
                validationErrors.append("第").append(i + 1).append("行数据，明细ID不能为空; ");
                continue;
            }

            RequestPurchaseLineBO existingLine = existingLineMap.get(importItem.getLineId());
            if (existingLine == null) {
                log.warn("第{}行数据，明细ID[{}]不存在，跳过", i + 1, importItem.getLineId());
                continue;
            }

            // 验证必填字段
            if (importItem.getSaleOtherCosts() == null) {
                validationErrors.append("第").append(i + 1).append("行数据，销售辅材及其他价格不能为空; ");
            }
            
            if (importItem.getSaleDiscountFactor() == null) {
                validationErrors.append("第").append(i + 1).append("行数据，销售折扣系数不能为空; ");
            }

            // 创建更新对象
            RequestPurchaseLineBO updateLine = new RequestPurchaseLineBO();
            updateLine.setId(importItem.getLineId());
            
            // 更新销售价格相关字段
            updateLine.setSaleOtherCosts(importItem.getSaleOtherCosts());
            updateLine.setSaleDiscountFactor(importItem.getSaleDiscountFactor());

            // 计算销售含税单价和利润率
            calculateSalePrice(updateLine, existingLine, importItem);
            
            updateList.add(updateLine);
        }

        // 如果有验证错误，抛出异常
        if (validationErrors.length() > 0) {
            throw new BizException("数据验证失败: " + validationErrors.toString());
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            log.info("准备更新{}条明细数据", updateList.size());
            DS.update(updateList);
            log.info("更新完成");
        } else {
            log.warn("没有需要更新的数据");
        }
    }

    /**
     * 计算销售含税单价和利润率
     */
    private void calculateSalePrice(RequestPurchaseLineBO updateLine, RequestPurchaseLineBO existingLine, 
                                  RequestPurchaseProfitExportTO importItem) {
        try {
            // 获取必要的计算参数
            BigDecimal purCopperBasicPrice = existingLine.getPurCopperBasicPrice(); // 采购协议铜基价
            BigDecimal purRawMaterialContent = existingLine.getPurRawMaterialContent(); // 标品含铜量
            BigDecimal saleOtherCosts = importItem.getSaleOtherCosts(); // 销售辅材及其他价格
            BigDecimal saleDiscountFactor = importItem.getSaleDiscountFactor(); // 销售折扣系数
            BigDecimal purTaxPrice = existingLine.getPurTaxPrice(); // 采购含税单价

            // 处理空值，设置默认值
            if (purCopperBasicPrice == null) {
                purCopperBasicPrice = BigDecimal.ZERO;
            }
            if (purRawMaterialContent == null) {
                purRawMaterialContent = BigDecimal.ZERO;
            }
            if (saleOtherCosts == null) {
                saleOtherCosts = BigDecimal.ZERO;
            }
            if (saleDiscountFactor == null) {
                saleDiscountFactor = BigDecimal.ONE;
            }
            if (purTaxPrice == null) {
                purTaxPrice = BigDecimal.ZERO;
            }

            // 计算销售含税单价 = （采购协议铜基价*标品含铜量/1000+销售辅材及其他价格）*销售折扣系数
            BigDecimal copperPrice = purCopperBasicPrice.multiply(purRawMaterialContent)
                    .divide(new BigDecimal("1000"), 6, RoundingMode.HALF_UP);
            BigDecimal saleTaxPrice = copperPrice.add(saleOtherCosts).multiply(saleDiscountFactor);
            
            // 设置保留2位小数
            saleTaxPrice = saleTaxPrice.setScale(2, RoundingMode.HALF_UP);
            updateLine.setSaleTaxPrice(saleTaxPrice);

            // 计算销售利润率 = （销售含税单价-采购含税单价）/采购含税单价*100%
            if (purTaxPrice.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal profitRate = saleTaxPrice.subtract(purTaxPrice)
                        .divide(purTaxPrice, 6, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                profitRate = profitRate.setScale(2, RoundingMode.HALF_UP);
                updateLine.setProfitRate(profitRate);
            } else {
                updateLine.setProfitRate(BigDecimal.ZERO);
            }

            log.info("计算销售价格成功，明细ID: {}, 销售含税单价: {}, 销售利润率: {}%", 
                    updateLine.getId(), saleTaxPrice, updateLine.getProfitRate());

        } catch (Exception e) {
            log.error("计算销售价格失败，明细ID: {}", updateLine.getId(), e);
            throw new BizException("计算销售价格失败: " + e.getMessage());
        }
    }

    /**
     * 检查Excel文件扩展名
     */
    private boolean checkExcelFileExtension(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return false;
        }
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return "xlsx".equals(extension) || "xls".equals(extension);
    }

    /**
     * Excel数据监听器
     */
    private static class ProfitDataListener implements com.alibaba.excel.read.listener.ReadListener<RequestPurchaseProfitExportTO> {
        
        private final List<RequestPurchaseProfitExportTO> allData;
        
        public ProfitDataListener(List<RequestPurchaseProfitExportTO> allData) {
            this.allData = allData;
        }

        @Override
        public void invoke(RequestPurchaseProfitExportTO data, com.alibaba.excel.context.AnalysisContext context) {
            allData.add(data);
        }

        @Override
        public void doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext context) {
            // 完成后的处理
        }
    }
} 