package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import cn.hutool.core.lang.Assert;
import io.terminus.gaia.app.b2b.contract.dict.AskPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AskSupplierPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceConfirmTO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2025/7/9 10:59
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class ConfirmAskSupplierPriceFuncImpl implements ConfirmAskSupplierPriceFunc {
    @Override
    public BooleanResult execute(AskSupplierPriceConfirmTO req) {
        validate(req);

        // 更新询价单状态
        AskPriceBO askPriceNew = new AskPriceBO();
        askPriceNew.setId(req.getAskPriceId());
        askPriceNew.setStatus(AskPriceStatusDict.FINISH);
        askPriceNew.setPayableSchemeTemplateBO(req.getPayableSchemeTemplateBO());
        DS.update(askPriceNew);

        List<AskSupplierPriceBO> askSupplierPriceBOS = DS.findAll(AskSupplierPriceBO.class, "*", "askPriceBO=?", req.getAskPriceId());

        // 更新供应商单据状态
        List<AskSupplierPriceBO> askSupplierPriceNewList = new ArrayList<>();
        for (AskSupplierPriceBO askSupplierPriceBO : askSupplierPriceBOS) {
            AskSupplierPriceBO askSupplierPriceNew = new AskSupplierPriceBO();
            askSupplierPriceNew.setId(askSupplierPriceBO.getId());
            askSupplierPriceNew.setStatus(Objects.equals(askSupplierPriceBO.getSupplierEntity().getId(), req.getEntityBO().getId()) ? AskSupplierPriceStatusDict.WIN : AskSupplierPriceStatusDict.LOSE);
            askSupplierPriceNewList.add(askSupplierPriceNew);
        }
        DS.update(askSupplierPriceNewList);

        return BooleanResult.TRUE;
    }

    private void validate(AskSupplierPriceConfirmTO req) {
        Assert.notNull(req.getAskPriceId(), ExceptionUtil.create("询价单id不能为空"));
        Assert.notNull(req.getPayableSchemeTemplateBO(), ExceptionUtil.create("账期不能为空"));
        Assert.notNull(req.getEntityBO(), ExceptionUtil.create("中标供应商不能为空"));
    }
}
