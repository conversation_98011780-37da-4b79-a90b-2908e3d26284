package io.terminus.gaia.app.b2b.contract.func.askprice.read;

import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskPriceBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 按照询价单id，查 询价单详情
 *
 * @author: huangjunwei
 **/
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class QueryAskPriceDetailFuncImpl implements QueryAskPriceDetailFunc {
    @Override
    public AskPriceBO execute(QAskPriceBO qAskPriceBO) {
        if (qAskPriceBO == null || qAskPriceBO.getId() == null) {
            throw new BusinessException("入参异常：询价单id不能为空！");
        }
        AskPriceBO askPriceBO = DS.findOne(AskPriceBO.class,
                "*, categoryBO.*, projectBO.*, departmentBO.*, askPriceLineBOList.*, askSupplierPriceBOList.*, entityBOList.*, askSupplierPriceBOList.supplierEntity.*",
                "id = ?", qAskPriceBO.getId().getValue());


        return askPriceBO;
    }
}
