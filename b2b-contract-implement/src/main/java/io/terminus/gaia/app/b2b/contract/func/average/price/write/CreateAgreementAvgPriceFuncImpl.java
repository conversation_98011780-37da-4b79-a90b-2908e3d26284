package io.terminus.gaia.app.b2b.contract.func.average.price.write;

import cn.hutool.core.collection.CollUtil;
import io.terminus.gaia.app.b2b.contract.model.AgreementAvgPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementAvgPriceLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementAvgPriceLineDetailTO;
import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.flow.LF;
import io.terminus.trantorframework.sdk.sql.DS;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@FunctionImpl
public class CreateAgreementAvgPriceFuncImpl implements CreateAgreementAvgPriceFunc {

    @Override
    @DSTransaction
    public void execute(AgreementAvgPriceBO agreementAvgPrice) {
        List<AgreementAvgPriceLineBO> allLineList = agreementAvgPrice.getAgreementAvgPriceLineList();
        if (CollUtil.isEmpty(allLineList)) {
            throw new BusinessException("协议清单行不可以为空");
        }

        if (CollUtil.isEmpty(allLineList.get(0).getDetailList())) {
            throw new BusinessException("至少有一个协议清单设置了价格方案");
        }

        Map<Long, List<AgreementAvgPriceLineBO>> lineListMap = allLineList.stream().collect(Collectors.groupingBy(e -> e.getSpu().getId()));
        List<AgreementAvgPriceBO> existList = DS.findAll(AgreementAvgPriceBO.class, "id,spuName", "spu in (?)", lineListMap.keySet());
        if (CollUtil.isNotEmpty(existList)) {
            throw new BusinessException("标品【" + String.join("|", LF.map(existList, AgreementAvgPriceBO::getSpuName)) + "】的平均价已存在");
        }

        Map<Long, SpuBO> spuMap = DS.findByIds(SpuBO.class, lineListMap.keySet()).stream().collect(Collectors.toMap(SpuBO::getId, Function.identity()));

        lineListMap.forEach((spuId, lineList) -> {
            AgreementAvgPriceBO toCreate = new AgreementAvgPriceBO();
            toCreate.setSpu(spuMap.get(spuId));
            toCreate.setSpuName(spuMap.get(spuId).getName());
            toCreate.setSchemeTemplate(agreementAvgPrice.getSchemeTemplate());
            toCreate.setFluctuationType(agreementAvgPrice.getFluctuationType());
            toCreate.setPricePlatform(agreementAvgPrice.getPricePlatform());
            toCreate.setPricingRules(agreementAvgPrice.getPricingRules());
            DS.create(toCreate);

            lineList.forEach(e -> e.setAgreementAvgPrice(toCreate));
            DS.create(lineList);
        });
    }
}
