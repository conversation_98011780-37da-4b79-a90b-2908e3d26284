package io.terminus.gaia.app.b2b.contract.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.terminus.gaia.app.b2b.contract.dict.AgreementCoverAreaTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementDetailStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementTypeDict;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.model.PaymentRelatePriceSchemeBO;
import io.terminus.gaia.app.b2b.item.dict.category.LinkPriceSchemeDict;
import io.terminus.gaia.app.b2b.item.model.CategoryAgreementConfigBO;
import io.terminus.gaia.item.model.price.PriceSchemeBO;
import io.terminus.trantorframework.sdk.sql.DS;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-17
 * @descrition
 */
public class AgreementTriggerHelper {

    public static boolean validateRequired(AgreementBO agreement) {

        boolean a = StrUtil.isNotBlank(agreement.getName());

        boolean b = agreement.getJfCompanyBO() != null;

        boolean c = agreement.getYfCompanyBO() != null;

        boolean d = CollUtil.isNotEmpty(agreement.getDepartments());

        boolean e = agreement.getCategory() != null;


        boolean f = agreement.getBizType() != null;

        boolean g = false;
        if (agreement.getCoverAreaType() != null) {
            if (agreement.getCoverAreaType().equals(AgreementCoverAreaTypeDict.ALL)) {
                g = true;
            } else {
                g =  CollUtil.isNotEmpty(agreement.getCoverAreas());
            }
        }

        boolean h = CollUtil.isNotEmpty(agreement.getPaymentSchemes());

        boolean i = agreement.getEffectiveAt() != null;

        boolean j = agreement.getExpireAt() != null;

        if (a && b && c && d && e && f && g && h && i && j) {
            return true;
        } else {
            return false;
        }

    }


    public static void validateConfig(CategoryAgreementConfigBO config, AgreementBO agreement) {

        String isLinkPriceScheme = Optional.ofNullable(config).map(CategoryAgreementConfigBO::getIsLinkPriceScheme).orElse(LinkPriceSchemeDict.NO);

        AgreementBO agreementNew = new AgreementBO();
        agreementNew.setId(agreement.getId());

        if (!isLinkPriceScheme.equals(LinkPriceSchemeDict.YES)) {
            agreementNew.setMaintainCompleted(true);
            DS.update(agreementNew);
        } else {
            boolean complete = true;
            List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class, "*", "agreementBO = ? and statusDict = ?",
                    agreement.getId(), AgreementDetailStatusDict.START_USING);

            if (Objects.equals(agreement.getType(), AgreementTypeDict.MATCH_MAKING)) {
                List<PaymentRelatePriceSchemeBO> paymentRelatePriceSchemeBOS = DS.findAll(PaymentRelatePriceSchemeBO.class, "*", "agreementBO=?", agreement.getId());
                Map<Long, List<PaymentRelatePriceSchemeBO>> paymentRelatePriceSchemeGroup = paymentRelatePriceSchemeBOS.stream().collect(Collectors.groupingBy(it -> it.getAgreementDetail().getId()));

                complete = details.stream().allMatch(it -> Objects.nonNull(it.getPriceSchemeBO()) || paymentRelatePriceSchemeGroup.containsKey(it.getId()));
            } else {
                complete = details.stream().allMatch(it -> Objects.nonNull(it.getPriceSchemeBO()));
            }

            agreementNew.setMaintainCompleted(complete);
            DS.update(agreementNew);
        }
    }

    public static CategoryAgreementConfigBO getCategoryAgreementConfig(AgreementBO data) {

        if (data.getCategory() == null) {
            return null;
        }

        List<CategoryAgreementConfigBO> all = DS.findAll(CategoryAgreementConfigBO.class,
                "*", "categoryBO = ? ", data.getCategory().getId());
        if (CollUtil.isNotEmpty(all)) {
            return all.get(0);
        }

        String idPath = data.getCategory().getIdPath();
        Stack<Long> stack = getIdPath(idPath, data.getCategory().getId());
        while (!stack.isEmpty()) {
            Long parentId = stack.pop();
            List<CategoryAgreementConfigBO> parentConfigs = DS.findAll(CategoryAgreementConfigBO.class,
                    "*", "categoryBO = ? order by updatedAt desc ", parentId);
            if (CollUtil.isNotEmpty(parentConfigs)) {
                return parentConfigs.get(0);
            }
        }

        return null;
    }


    public static Stack getIdPath(String idPath, Long id) {

        String[] stringArray = idPath.split("/");

        Stack<Long> stack = new Stack<>();
        for (String str : stringArray) {
            if (str.equals(String.valueOf(id))) {
                continue;
            }
            stack.push(Long.parseLong(str));
        }

        return stack;
    }
}
