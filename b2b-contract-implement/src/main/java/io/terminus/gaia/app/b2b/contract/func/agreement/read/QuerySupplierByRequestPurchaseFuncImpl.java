package io.terminus.gaia.app.b2b.contract.func.agreement.read;

import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QRequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.model.query.QRequestPurchaseLineBO;
import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.md.model.UnitBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.querymodel.Select;
import io.terminus.trantorframework.querymodel.type.support.QLongId;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 根据需求单查询供应商实现
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class QuerySupplierByRequestPurchaseFuncImpl implements QuerySupplierByRequestPurchaseFunc {

    @Override
    public List<EntityBO> execute(RequestPurchaseBO requestPurchaseBO) {
        if (Objects.isNull(requestPurchaseBO) || Objects.isNull(requestPurchaseBO.getId())) {
            return new ArrayList<>();
        }

        // 查询该需求单下的所有需求明细
        QRequestPurchaseLineBO qRequestPurchaseLineBO = new QRequestPurchaseLineBO();
        QRequestPurchaseBO qRequestPurchaseBO = new QRequestPurchaseBO();
        qRequestPurchaseBO.setId(new QLongId(requestPurchaseBO.getId()));
        qRequestPurchaseLineBO.setRequestPurchaseBO(qRequestPurchaseBO);
        Select select = new Select();
        select.addField("*")
                .addField(RequestPurchaseLineBO.supplier_field, new Select.Field(EntityBO.id_field))
                .addField(RequestPurchaseLineBO.supplier_field, new Select.Field(EntityBO.entityCode_field))
                .addField(RequestPurchaseLineBO.supplier_field, new Select.Field(EntityBO.entityName_field));
        qRequestPurchaseLineBO.getQueryParams().setSelect(select);
        
        List<RequestPurchaseLineBO> requestPurchaseLines = DS.findAll(qRequestPurchaseLineBO);
        
        // 提取所有供应商，去重
        return requestPurchaseLines.stream()
                .map(RequestPurchaseLineBO::getSupplier)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        EntityBO::getId,
                        supplier -> supplier,
                        (existing, replacement) -> existing
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
    }
} 