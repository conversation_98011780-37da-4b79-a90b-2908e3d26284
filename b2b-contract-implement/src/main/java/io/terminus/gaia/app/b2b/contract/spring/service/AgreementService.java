package io.terminus.gaia.app.b2b.contract.spring.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Throwables;
import io.terminus.gaia.app.b2b.contract.dict.*;
import io.terminus.gaia.app.b2b.contract.model.*;
import io.terminus.gaia.app.b2b.contract.spring.converter.X5AgreementConverter;
import io.terminus.gaia.app.b2b.contract.spring.converter.YzwAgreementConverter;
import io.terminus.gaia.app.b2b.contract.spring.model.x5.X5Contract;
import io.terminus.gaia.app.b2b.contract.spring.model.yzw.YzwContract;
import io.terminus.gaia.app.b2b.contract.spring.repo.AgreementRepo;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementDetailSupplyInfoTO;
import io.terminus.gaia.common.utils.EnhanceDS;
import io.terminus.gaia.contract.dict.common.ApproveStatusDict;
import io.terminus.gaia.md.dict.SyncStatusDict;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.json.Json;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AgreementService {
    private final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(10);

    private final AgreementRepo agreementRepo;
    private final YzwAgreementConverter yzwAgreementConverter;
    private final X5AgreementConverter x5AgreementConverter;
    private final AgreementDetailService agreementDetailService;
    private final AgreementSendNoticeService agreementSendNoticeService;

    /**
     * 新建
     *
     * @param agreementBO
     * @return
     */
    public AgreementBO createAgreement(AgreementBO agreementBO) {
        agreementRepo.createAgreement(agreementBO);
        return agreementBO;
    }

    /**
     * 补充协议创建更新住协议金额(opt=1新增，opt=2编辑，opt=3删除)
     *
     * @param mainAgreementBO
     * @param agreementBO
     * @param opt
     * @param oldAgreementBO
     * @return
     */
    public AgreementBO updateAgreementAmt(AgreementBO mainAgreementBO, AgreementBO agreementBO, int opt, AgreementBO oldAgreementBO) {
        // 新增补充协议
        if (opt == 1) {
            if (Objects.nonNull(agreementBO.getTaxAmt())){
                // 主协议累计补充协议金额
                if (Objects.nonNull(mainAgreementBO.getSupplyTaxAmt())) {
                    mainAgreementBO.setSupplyTaxAmt(agreementBO.getTaxAmt().add(mainAgreementBO.getSupplyTaxAmt()));
                    mainAgreementBO.setSupplyNoTaxAmt(agreementBO.getNoTaxAmt().add(mainAgreementBO.getSupplyNoTaxAmt()));
                    mainAgreementBO.setSupplyTaxPrc(agreementBO.getTaxPrc().add(mainAgreementBO.getSupplyTaxPrc()));
                } else {
                    mainAgreementBO.setSupplyTaxAmt(agreementBO.getTaxAmt());
                    mainAgreementBO.setSupplyNoTaxAmt(agreementBO.getNoTaxAmt());
                    mainAgreementBO.setSupplyTaxPrc(agreementBO.getTaxPrc());
                }
                // 主协议协议金额
                if (Objects.nonNull(mainAgreementBO.getTaxAmt())) {
                    mainAgreementBO.setTaxAmt(mainAgreementBO.getTaxAmt().add(agreementBO.getTaxAmt()));
                    mainAgreementBO.setNoTaxAmt(agreementBO.getNoTaxAmt().add(mainAgreementBO.getNoTaxAmt()));
                    mainAgreementBO.setTaxPrc(agreementBO.getTaxPrc().add(mainAgreementBO.getTaxPrc()));
                } else {
                    mainAgreementBO.setTaxAmt(agreementBO.getTaxAmt());
                    mainAgreementBO.setNoTaxAmt(agreementBO.getNoTaxAmt());
                    mainAgreementBO.setTaxPrc(agreementBO.getTaxPrc());
                }
            }
        } else if(opt == 2 && Objects.nonNull(oldAgreementBO)) {
            // 编辑补充协议
            if (Objects.nonNull(agreementBO.getTaxAmt())){
                // 补充协议金额
                if (Objects.nonNull(mainAgreementBO.getSupplyTaxAmt())) {
                    mainAgreementBO.setSupplyTaxAmt(mainAgreementBO.getSupplyTaxAmt().subtract(Objects.nonNull(oldAgreementBO.getTaxAmt()) ? oldAgreementBO.getTaxAmt(): BigDecimal.ZERO).add(agreementBO.getTaxAmt()));
                    mainAgreementBO.setSupplyNoTaxAmt(mainAgreementBO.getSupplyNoTaxAmt().subtract(Objects.nonNull(oldAgreementBO.getNoTaxAmt()) ? oldAgreementBO.getNoTaxAmt() : BigDecimal.ZERO).add(agreementBO.getNoTaxAmt()));
                    mainAgreementBO.setSupplyTaxPrc(mainAgreementBO.getSupplyTaxPrc().subtract(Objects.nonNull(oldAgreementBO.getTaxPrc()) ? oldAgreementBO.getTaxPrc() : BigDecimal.ZERO).add(agreementBO.getTaxPrc()));
                } else {
                    mainAgreementBO.setSupplyTaxAmt(agreementBO.getTaxAmt());
                    mainAgreementBO.setSupplyNoTaxAmt(agreementBO.getNoTaxAmt());
                    mainAgreementBO.setSupplyTaxPrc(agreementBO.getTaxPrc());
                }
                // 主协议协议金额
                if (Objects.nonNull(mainAgreementBO.getTaxAmt())) {
                    mainAgreementBO.setTaxAmt(mainAgreementBO.getTaxAmt().subtract(Objects.nonNull(oldAgreementBO.getTaxAmt()) ? oldAgreementBO.getTaxAmt(): BigDecimal.ZERO).add(agreementBO.getTaxAmt()));
                    mainAgreementBO.setNoTaxAmt(mainAgreementBO.getNoTaxAmt().subtract(Objects.nonNull(oldAgreementBO.getNoTaxAmt()) ? oldAgreementBO.getNoTaxAmt() : BigDecimal.ZERO).add(agreementBO.getNoTaxAmt()));
                    mainAgreementBO.setTaxPrc(mainAgreementBO.getTaxPrc().subtract(Objects.nonNull(oldAgreementBO.getTaxPrc()) ? oldAgreementBO.getTaxPrc() : BigDecimal.ZERO).add(agreementBO.getTaxPrc()));
                } else {
                    mainAgreementBO.setTaxAmt(agreementBO.getTaxAmt());
                    mainAgreementBO.setNoTaxAmt(agreementBO.getNoTaxAmt());
                    mainAgreementBO.setTaxPrc(agreementBO.getTaxPrc());
                }
            }
        } else if(opt == 3) {
            // 删除补充协议
            if (Objects.nonNull(agreementBO.getTaxAmt())){
                // 补充协议金额
                if (Objects.nonNull(mainAgreementBO.getSupplyTaxAmt())) {
                    mainAgreementBO.setSupplyTaxAmt(mainAgreementBO.getSupplyTaxAmt().subtract(agreementBO.getTaxAmt()));
                    mainAgreementBO.setSupplyNoTaxAmt(mainAgreementBO.getSupplyNoTaxAmt().subtract(agreementBO.getNoTaxAmt()));
                    mainAgreementBO.setSupplyTaxPrc(mainAgreementBO.getSupplyTaxPrc().subtract(agreementBO.getTaxPrc()));
                }
                // 主协议协议金额
                if (Objects.nonNull(mainAgreementBO.getTaxAmt())) {
                    mainAgreementBO.setTaxAmt(mainAgreementBO.getTaxAmt().subtract(agreementBO.getTaxAmt()));
                    mainAgreementBO.setNoTaxAmt(mainAgreementBO.getNoTaxAmt().subtract(agreementBO.getNoTaxAmt()));
                    mainAgreementBO.setTaxPrc(mainAgreementBO.getTaxPrc().subtract(agreementBO.getTaxPrc()));
                }
            }
        }
        DS.update(mainAgreementBO);
        return agreementBO;
    }

    /**
     * 更新协议
     *
     * @param newAgreement
     * @param checkDetail 校验清单信息
     * @return
     */
    public boolean editAgreement(AgreementBO newAgreement, AgreementBO existAgreement, boolean checkDetail) {
        List<Runnable> runnables = new ArrayList<>();
        if (newAgreement.getContacts() != null) {
            runnables.add(() -> {
                List<AgreementContactBO> existContacts = DS.findAll(AgreementContactBO.class, "id", "agreementBO = ?", newAgreement.getId());
                List<AgreementContactBO> newContacts = newAgreement.getContacts();
                Set<Long> newContactIds = newContacts.stream().filter(newContact -> newContact.getId() != null)
                        .map(AgreementContactBO::getId).collect(Collectors.toSet());

                List<AgreementContactBO> deleteContacts = existContacts.stream()
                        .filter(exist -> !newContactIds.contains(exist.getId())).collect(Collectors.toList());
                newAgreement.setDeleteContacts(deleteContacts);
            });
        }
        if (newAgreement.getCoverAreas() != null) {
            runnables.add(() -> {
                List<AgreementCoverAreaBO> existAreas = DS.findAll(AgreementCoverAreaBO.class, "id", "agreementBO = ?", newAgreement.getId());
                List<AgreementCoverAreaBO> newAreas = newAgreement.getCoverAreas();
                Set<Long> newAreaIds = newAreas.stream().filter(newArea -> newArea.getId() != null)
                        .map(AgreementCoverAreaBO::getId).collect(Collectors.toSet());

                List<AgreementCoverAreaBO> deleteAreas = existAreas.stream()
                        .filter(exist -> !newAreaIds.contains(exist.getId())).collect(Collectors.toList());
                newAgreement.setDeleteCoverAreas(deleteAreas);

                if (newAgreement.getCoverAreaLines() != null) {
                    List<AgreementCoverAreaLineBO> existLines = DS.findAll(AgreementCoverAreaLineBO.class, "id", "agreementBO = ?", newAgreement.getId());
                    List<AgreementCoverAreaLineBO> newLines = newAgreement.getCoverAreaLines();
                    Set<Long> newLineIds = newLines.stream().filter(newLine -> newLine.getId() != null)
                            .map(AgreementCoverAreaLineBO::getId).collect(Collectors.toSet());

                    List<AgreementCoverAreaLineBO> deleteLines = existLines.stream()
                            .filter(exist -> !newLineIds.contains(exist.getId())).collect(Collectors.toList());
                    newAgreement.setDeleteCoverAreaLines(deleteLines);
                }
            });
        }
        if (newAgreement.getDealers() != null) {
            runnables.add(() -> {
                List<AgreementDealerBO> existDealers = DS.findAll(AgreementDealerBO.class, "id", "agreementBO = ?", newAgreement.getId());
                List<AgreementDealerBO> newDealers = newAgreement.getDealers();
                Set<Long> newDearIds = newDealers.stream().filter(newArea -> newArea.getId() != null)
                        .map(AgreementDealerBO::getId).collect(Collectors.toSet());

                List<AgreementDealerBO> deleteDealers = existDealers.stream()
                        .filter(exist -> !newDearIds.contains(exist.getId())).collect(Collectors.toList());
                newAgreement.setDeleteDealers(deleteDealers);

                if (newAgreement.getDealerLines() != null) {
                    List<AgreementDealerLineBO> existLines = DS.findAll(AgreementDealerLineBO.class, "id", "agreementBO = ?", newAgreement.getId());
                    List<AgreementDealerLineBO> newLines = newAgreement.getDealerLines();
                    Set<Long> newLineIds = newLines.stream().filter(newLine -> newLine.getId() != null)
                            .map(AgreementDealerLineBO::getId).collect(Collectors.toSet());

                    List<AgreementDealerLineBO> deleteLines = existLines.stream()
                            .filter(exist -> !newLineIds.contains(exist.getId())).collect(Collectors.toList());
                    newAgreement.setDeleteDealerLines(deleteLines);
                }
            });
        }
        if (newAgreement.getPaymentSchemes() != null) {
            runnables.add(() -> {
                List<PaymentSchemeBO> existSchemes = DS.findAll(PaymentSchemeBO.class, "id", "agreementBO = ?", newAgreement.getId());
                List<PaymentSchemeBO> newSchemes = newAgreement.getPaymentSchemes();
                Set<Long> newSchemeIds = newSchemes.stream().filter(newScheme -> newScheme.getId() != null)
                        .map(PaymentSchemeBO::getId).collect(Collectors.toSet());

                List<PaymentSchemeBO> deleteAreas = existSchemes.stream()
                        .filter(exist -> !newSchemeIds.contains(exist.getId())).collect(Collectors.toList());
                newAgreement.setDeletePaymentSchemes(deleteAreas);
            });
        }

        if (newAgreement.getUsingDepartments() != null) {
            runnables.add(() -> {
                List<AgreementUsingDepartmentBO> usingDepartments = DS.findAll(AgreementUsingDepartmentBO.class, "id", "agreementBO = ?", newAgreement.getId());
                List<AgreementUsingDepartmentBO> newUsingDepartments = newAgreement.getUsingDepartments();
                Set<Long> newUsingDepartmentIds = newUsingDepartments.stream().filter(newUsingDepartment -> newUsingDepartment.getId() != null)
                        .map(AgreementUsingDepartmentBO::getId).collect(Collectors.toSet());

                List<AgreementUsingDepartmentBO> deleteDepartments = usingDepartments.stream()
                        .filter(exist -> !newUsingDepartmentIds.contains(exist.getId())).collect(Collectors.toList());
                newAgreement.setDeleteUsingDepartments(deleteDepartments);
            });
        }

        // 定时账期设置
        if (newAgreement.getRegularReconciliations() != null) {
            runnables.add(() -> {
                List<AgreementRegularReconciliationBO> usingRegulars = DS.findAll(AgreementRegularReconciliationBO.class, "id", "agreementBO = ?", newAgreement.getId());
                List<AgreementRegularReconciliationBO> newAgreementRegularReconciliations = newAgreement.getRegularReconciliations();
                Set<Long> newRegularReconciliationIds = newAgreementRegularReconciliations.stream().filter(newRegular -> newRegular.getId() != null)
                        .map(AgreementRegularReconciliationBO::getId).collect(Collectors.toSet());

                List<AgreementRegularReconciliationBO> deleteRegulars = usingRegulars.stream()
                        .filter(exist -> !newRegularReconciliationIds.contains(exist.getId())).collect(Collectors.toList());
                newAgreement.setDeleteRegularReconciliations(deleteRegulars);
            });

        }

        if (checkDetail) {
            runnables.add(() -> {
                // 待更新的明细
                Map<Long, AgreementDetailBO> updateDetailMap = new HashMap<>();
                // DB清单
                List<AgreementDetailBO> existDetails = DS.findAll(AgreementDetailBO.class, "*", "agreementBO = ?", existAgreement.getId());
                // 乙方变更，修改清单供应商
                EntityBO newYf = newAgreement.getYfCompanyBO();
                EntityBO existYf = existAgreement.getYfCompanyBO();
                if (newYf != null && (existYf == null || !newYf.getId().equals(existYf.getId()))) {
                    log.info("协议乙方变更，更新关联清单供应商，【{}-{}】 -> 【{}-{}】",
                            existYf == null ? null : existYf.getId(), existYf == null ? null : existYf.getEntityName(), newYf.getId(), newYf.getEntityName());
                    existDetails.stream().filter(detail -> detail.getSaleEntityBO() == null ||
                                    !detail.getSaleEntityBO().getId().equals(newYf.getId()))
                            .forEach(existDetail -> {
                                updateDetailMap.computeIfAbsent(existDetail.getId(), (id) -> {
                                    AgreementDetailBO newDetail = new AgreementDetailBO();
                                    newDetail.setId(id);
                                    newDetail.setSaleEntityBO(newYf);
                                    AgreementDetailSupplyInfoTO supplyInfo = existDetail.getSupplyInfo();
                                    if (supplyInfo != null) {
                                        supplyInfo.setSaleEntityBO(newYf);
                                        newDetail.setSupplyInfo(supplyInfo);
                                    }
                                    return newDetail;
                                });
                            });
                }
                newAgreement.setDetails(ListUtil.toList(updateDetailMap.values()));
            });
        }
        // 并发执行，后续调整为自定义线程池 todo
        runnables.parallelStream().forEach(Runnable::run);

        // 置空，防止覆盖
        newAgreement.setJcBidInfo(null);

        agreementRepo.updateAgreementAndRel(newAgreement);

        // 过期时间不为空
        boolean doDelay = newAgreement.getExpireAt() != null &&
                newAgreement.getExpireAt().after(new Date()) &&
                AgreementStatusDict.EXPIRED.equals(existAgreement.getStatus());
        if (doDelay) {
            delayAgreement(newAgreement, existAgreement);
        }

        return true;
    }

    /**
     * 订正同步的协议数据
     *
     * @param newAgreement
     */
    @Async
    public void reviseAbnormalAgreement(AgreementBO newAgreement, AgreementBO existAgreement) {
        // 处理同步异常的协议数据
        if (!AgreementStatusDict.ABNORMAL.equals(existAgreement.getStatus())) {
            return;
        }

        AgreementBO toUpdate = AgreementBO.of(newAgreement.getId());
        toUpdate.setStatus(AgreementStatusDict.ENABLED);
        toUpdate.setSyncStatus(SyncStatusDict.SUCCESS);
        toUpdate.setSyncRemark("");

        log.info("协议【{}】异常数据修正:{}", existAgreement.getName(), Json.toJson(toUpdate));
        agreementRepo.updateAgreement(toUpdate);

        // 清单数据
        reviseAbnormalAgreementDetail(newAgreement);
    }

    /**
     * 删除协议
     *
     * @param newAgreement
     * @return
     */
    public boolean deleteAgreement(AgreementBO newAgreement, AgreementBO existAgreement) {
        AgreementBO toDelete = AgreementBO.of(newAgreement.getId());

        // 清单
        List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class,
                "id", "agreementBO = ?", newAgreement.getId());
        toDelete.setDetails(details);
        // 覆盖范围
        List<AgreementCoverAreaBO> areas = DS.findAll(AgreementCoverAreaBO.class,
                "id", "agreementBO = ?", newAgreement.getId());
        toDelete.setDeleteCoverAreas(areas);
        // 付款方案
        List<PaymentSchemeBO> schemes = DS.findAll(PaymentSchemeBO.class,
                "id", "agreementBO = ?", newAgreement.getId());
        toDelete.setDeletePaymentSchemes(schemes);
        // 联系人
        List<AgreementContactBO> contacts = DS.findAll(AgreementContactBO.class,
                "id", "agreementBO = ?", newAgreement.getId());
        toDelete.setDeleteContacts(contacts);
        // 覆盖范围行
        List<AgreementCoverAreaLineBO> coverLines = DS.findAll(AgreementCoverAreaLineBO.class,
                "id", "agreementBO = ?", newAgreement.getId());
        toDelete.setDeleteCoverAreaLines(coverLines);
        // 使用单位
        List<AgreementUsingDepartmentBO> usingDepartments = DS.findAll(AgreementUsingDepartmentBO.class,
                "id", "agreementBO = ?", newAgreement.getId());
        toDelete.setDeleteUsingDepartments(usingDepartments);
        // 经销商
        List<AgreementDealerBO> dealers = DS.findAll(AgreementDealerBO.class,
                "id", "agreementBO = ?", newAgreement.getId());
        toDelete.setDeleteDealers(dealers);
        // 经销商明细
        List<AgreementDealerLineBO> dealerLines = DS.findAll(AgreementDealerLineBO.class,
                "id", "agreementBO = ?", newAgreement.getId());
        toDelete.setDeleteDealerLines(dealerLines);

        boolean result = agreementRepo.deleteAgreementAndRel(toDelete);
        agreementDetailService.deleteAgreementDetail(details);

        return result;
    }

    /**
     * 启用协议
     * @param newAgreement
     * @return
     */
    public boolean enableAgreement(AgreementBO newAgreement, AgreementBO existAgreement) {
        AgreementBO toUpdate = AgreementBO.of(newAgreement.getId());
        toUpdate.setStatus(AgreementStatusDict.ENABLED);
        toUpdate.setApproveStatusDict(ApproveStatusDict.APPROVED);
        agreementRepo.updateAgreementAndRel(toUpdate);

        // 清单禁用
        enableAgreementDetail(newAgreement);

        return true;
    }

    /**
     * 禁用协议
     * @param newAgreement
     * @return
     */
    public boolean disableAgreement(AgreementBO newAgreement, AgreementBO existAgreement) {
        AgreementBO toUpdate = AgreementBO.of(newAgreement.getId());
        toUpdate.setStatus(AgreementStatusDict.DISABLED);
        toUpdate.setApproveStatusDict(ApproveStatusDict.APPROVED);
        agreementRepo.updateAgreementAndRel(toUpdate);

        // 清单禁用
        disableAgreementDetail(newAgreement);

        return true;
    }


    /**
     * 协议过期
     *
     * @param agreementBO
     * @return
     */
    public boolean expireAgreement(AgreementBO agreementBO) {
        AgreementBO toUpdate = AgreementBO.of(agreementBO.getId());
        toUpdate.setStatus(AgreementStatusDict.EXPIRED);

        log.info("协议过期:{}", Json.toJson(toUpdate));
        agreementRepo.updateAgreementAndRel(toUpdate);

        // 清单失效
        expireAgreementDetail(agreementBO);
        agreementSendNoticeService.sendStationLetter(agreementBO);

        return true;
    }

    /**
     * 协议延期
     *
     * @param agreementBO
     * @return
     */
    public boolean delayAgreement(AgreementBO agreementBO, AgreementBO existAgreement) {
        AgreementBO toUpdate = AgreementBO.of(agreementBO.getId());
        toUpdate.setStatus(AgreementStatusDict.DISABLED);

        log.info("协议【{}】延期:{}", existAgreement.getName(), Json.toJson(toUpdate));
        agreementRepo.updateAgreementAndRel(toUpdate);

        // 清单延期
        delayAgreementDetail(agreementBO);

        return true;
    }

    /**
     * 作废协议
     *
     * @param agreementBO
     * @return
     */
    public boolean dropAgreement(AgreementBO agreementBO) {
        AgreementBO toUpdate = AgreementBO.of(agreementBO.getId());
        toUpdate.setStatus(AgreementStatusDict.DROP);

        log.info("集采协议【{}】同步，自动作废:{}", agreementBO.getName(), Json.toJson(toUpdate));
        agreementRepo.updateAgreementAndRel(toUpdate);

        // 清单失效
        dropAgreementDetail(agreementBO);

        return true;
    }


    /**
     * 启用清单
     *
     * @param agreementBO
     */
    private void enableAgreementDetail(AgreementBO agreementBO) {
        List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class,
                "*", "agreementBO = ? AND statusDict = ?",
                agreementBO.getId(), AgreementDetailStatusDict.STOP_USING);
        agreementDetailService.enableAgreementDetail(details);
    }


    /**
     * 禁用清单
     *
     * @param agreementBO
     */
    private void disableAgreementDetail(AgreementBO agreementBO) {
        List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class,
                "*", "agreementBO = ? AND statusDict = ?",
                agreementBO.getId(), AgreementDetailStatusDict.START_USING);
        agreementDetailService.disableAgreementDetail(details);
    }

    /**
     * 协议延期->更新清单
     *
     * @param agreementBO
     */
    private void delayAgreementDetail(AgreementBO agreementBO) {
        List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class,
                "*", "agreementBO = ? AND statusDict = ? AND loseReason = ?",
                agreementBO.getId(), AgreementDetailStatusDict.LOSE_EFFICACY, AgreementDetailLoseReasonDict.AGREEMENT_EXPIRE);
        details.forEach(detail -> {
            detail.setLoseReason(AgreementDetailLoseReasonDict.NULL);
        });
        agreementDetailService.disableAgreementDetail(details);
    }

    /**
     * 协议过期->更新清单
     *
     * @param agreementBO
     */
    private void expireAgreementDetail(AgreementBO agreementBO) {
        List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class,
                "*", "agreementBO = ? AND statusDict IN (?)",
                agreementBO.getId(), ListUtil.toList(AgreementDetailStatusDict.STOP_USING, AgreementDetailStatusDict.START_USING));
        details.forEach(detail -> {
            detail.setLoseReason(AgreementDetailLoseReasonDict.AGREEMENT_EXPIRE);
        });
        agreementDetailService.loseAgreementDetail(details);
    }


    /**
     * 协议作废 -》更新清单
     *
     * @param agreementBO
     */
    private void dropAgreementDetail(AgreementBO agreementBO) {
        List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class,
                "*", "agreementBO = ? AND statusDict IN (?)",
                agreementBO.getId(), ListUtil.toList(AgreementDetailStatusDict.STOP_USING, AgreementDetailStatusDict.START_USING));
        details.forEach(detail -> {
            detail.setLoseReason(AgreementDetailLoseReasonDict.AGREEMENT_DROP);
        });
        agreementDetailService.loseAgreementDetail(details);
    }


    /**
     * 订正同步协议数据 -》 更新清单数据
     *
     * @param newAgreement
     */
    private void reviseAbnormalAgreementDetail(AgreementBO newAgreement) {
        // 所有异常清单
        List<AgreementDetailBO> abnormalDetails = DS.findAll(AgreementDetailBO.class,
                "*", "agreementBO = ? AND statusDict = ? AND loseReason = ?",
                newAgreement.getId(), AgreementDetailStatusDict.LOSE_EFFICACY, AgreementDetailLoseReasonDict.AGREEMENT_SYNC_FAIL);
        if (CollectionUtils.isEmpty(abnormalDetails)) {
            return;
        }
        List<AgreementDetailBO> enableDetails = new ArrayList<>();
        List<AgreementDetailBO> unusualDetails = new ArrayList<>();
        abnormalDetails.forEach(detail -> {
            // 映射成功 && 包含所有必填数据，设为启用
            if (detail.containAllRequired()) {
                detail.setInventoryStatus(InventoryStatusDict.IN_STOCK);
                detail.setStatusDict(AgreementDetailStatusDict.START_USING);
                detail.setLoseReason(AgreementDetailLoseReasonDict.NULL);
                enableDetails.add(detail);
            } else {
                // 没有包含所有必填数据，置为异常
                detail.setInventoryStatus(InventoryStatusDict.OUT_STOCK);
                detail.setStatusDict(AgreementDetailStatusDict.UNUSUAL);
                detail.setLoseReason(AgreementDetailLoseReasonDict.NULL);
                unusualDetails.add(detail);
            }
        });
        log.info("订正异常清单，启用:{}，失效:{}", Json.toJson(enableDetails), Json.toJson(unusualDetails));
        agreementDetailService.enableAgreementDetail(enableDetails);
        agreementDetailService.unusualAgreementDetail(unusualDetails);
    }

    /**
     * 延迟任务
     * TODO 兼容容器异常中断导致任务丢失的情况，容器启动时加载待处理task
     * 暂时无用
     *
     * @param agreement
     */
    public void addDelayTask(AgreementBO agreement) {
        long currentMs = System.currentTimeMillis();
        long delayMs = agreement.getExpireAt().getTime() - currentMs;
        log.info("协议【{}-{}】加入自动过期队列，delayMs={}", agreement.getId(), agreement.getName(), delayMs);
        // 延迟任务
        scheduledExecutorService.schedule(() -> {
            log.info("协议【{}】延迟任务自动过期..", agreement.getId() + agreement.getName());
            expireAgreement(agreement);
        }, delayMs, TimeUnit.MILLISECONDS);
    }

    /**
     * 云筑网协议保存
     *
     * @param yzwContracts
     */
    public void saveYzwAgreement(List<YzwContract> yzwContracts) {
        saveYzwAgreement(yzwContracts, false);
    }

    /**
     * 云筑网协议保存
     *
     * @param yzwContracts
     */
    public void saveYzwAgreement(List<YzwContract> yzwContracts, Boolean skipExist) {
        if (CollectionUtils.isEmpty(yzwContracts)) {
            return;
        }
        yzwContracts.forEach(yzwContract -> {
            try {
                AgreementBO saveAgreement = yzwAgreementConverter.convertAgreement(yzwContract, skipExist);
                if (saveAgreement == null) {
                    return;
                }
                log.info("saveYzwAgreement:{}", Json.toJson(yzwContract));
                if (saveAgreement.getId() != null && AgreementStatusDict.DROP.equals(saveAgreement.getStatus())) {
                    log.info("集采协议协议【{}-{}-{}】作废", saveAgreement.getId(), saveAgreement.getExternalCode(), saveAgreement.getName());
                    dropAgreement(saveAgreement);
                } else {
                    agreementRepo.saveSyncAgreement(saveAgreement);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("集采协议【{}-{}】拉取失败, cause:{}", yzwContract.getExternalId(), yzwContract.getContractName(), Throwables.getStackTraceAsString(ex));
                AgreementSyncMessageBO existSyncMessage = EnhanceDS.safeFindOne(AgreementSyncMessageBO.class, "*", "externalId = ? AND source = ?",
                        yzwContract.getExternalId(), ContractSyncSourceDict.JC_SYNC);
                if (existSyncMessage != null) {
                    AgreementSyncMessageBO toUpdate = new AgreementSyncMessageBO();
                    toUpdate.setId(existSyncMessage.getId());
                    toUpdate.setMessage(Json.toJson(yzwContract));
                    toUpdate.setError(StrUtil.subWithLength(Throwables.getStackTraceAsString(ex), 0, 2000));
                    DS.update(toUpdate);
                } else {
                    AgreementSyncMessageBO syncMessageBO = new AgreementSyncMessageBO();
                    syncMessageBO.setExternalId(yzwContract.getExternalId());
                    syncMessageBO.setExternalCode(yzwContract.getContractCode());
                    syncMessageBO.setExternalName(yzwContract.getContractName());
                    syncMessageBO.setSource(ContractSyncSourceDict.JC_SYNC);
                    syncMessageBO.setMessage(Json.toJson(yzwContract));
                    syncMessageBO.setError(StrUtil.subWithLength(Throwables.getStackTraceAsString(ex), 0, 2000));
                    DS.create(syncMessageBO);
                }
            }

        });
    }

    /**
     * X5网协议保存
     *
     * @param x5Contract
     */
    public void saveX5Agreement(X5Contract x5Contract) {
        log.info("saveX5Agreement:{}", Json.toJson(x5Contract));
        try {
            // 模型转化
            AgreementBO agreementBO = x5AgreementConverter.convertAgreement(x5Contract);
            if (agreementBO == null) {
                return;
            }
            agreementRepo.saveSyncAgreement(agreementBO);
        } catch (Exception ex) {
            log.error("x5协议保存失败:{}", Throwables.getStackTraceAsString(ex));
            AgreementSyncMessageBO existSyncMessage = EnhanceDS.safeFindOne(AgreementSyncMessageBO.class, "*", "externalId = ? AND source = ?",
                    x5Contract.getContractCode(), ContractSyncSourceDict.X5_SYNC);
            if (existSyncMessage == null) {
                return;
            }
            AgreementSyncMessageBO toUpdate = new AgreementSyncMessageBO();
            toUpdate.setId(existSyncMessage.getId());
            toUpdate.setError(StrUtil.subSufByLength(Throwables.getStackTraceAsString(ex), 1000));
            DS.update(existSyncMessage);
        }
    }


    public void updateRentInfo(AgreementBO agreementBO) {
        boolean notRent = !agreementBO.getBizType().equals(YzContractTypeDict.RENT);
        boolean nullPeriod =  agreementBO.getRentPeriod() == null;
        if ( notRent || nullPeriod) {
            //AgreementBO update = DS.findById(AgreementBO.class, agreementBO.getId());
            //update.setRentPeriod(null);
            //DS.updateWithNull(update);

            DS.update(AgreementBO.class,"rentPeriod=null","id=?",agreementBO.getId());
        }
    }


    public void batchUpdateRentInfo(AgreementBO updated, List<AgreementBO> agreementList) {
        for (AgreementBO agreementBO : agreementList) {
            boolean notRent = !updated.getBizType().equals(YzContractTypeDict.RENT);
            boolean nullPeriod =  updated.getRentPeriod() == null;
            if ( notRent || nullPeriod) {
                AgreementBO update = DS.findById(AgreementBO.class, agreementBO.getId());
                update.setRentPeriod(null);
                DS.updateWithNull(update);
            }
        }

    }
}
