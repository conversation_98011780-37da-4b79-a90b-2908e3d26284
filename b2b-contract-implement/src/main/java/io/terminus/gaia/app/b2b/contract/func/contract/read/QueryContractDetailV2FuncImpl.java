package io.terminus.gaia.app.b2b.contract.func.contract.read;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.gaia.app.b2b.contract.dict.IncomingMaterialTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.YzContractBusinessTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.YzContractTypeDict;
import io.terminus.gaia.app.b2b.contract.model.ContractLineBO;
import io.terminus.gaia.app.b2b.contract.model.YzContractBO;
import io.terminus.gaia.app.b2b.item.model.SkuBOExt;
import io.terminus.gaia.common.utils.DictUtil;
import io.terminus.gaia.item.model.sku.SkuBO;
import io.terminus.gaia.item.tmodel.CategoryListTO;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.partner.dict.BusinessTypeDict;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/7
 */
@FunctionImpl(name = "QueryContractDetailV2FuncImpl")
@Slf4j
public class QueryContractDetailV2FuncImpl implements QueryContractDetailV2Func {
    @Override
    public YzContractBO execute(YzContractBO contractExtBO) {
        YzContractBO contractBO = null;
        if (contractExtBO.getId() != null) {
            contractBO = DS.findById(YzContractBO.class, contractExtBO.getId(), "*,partyA.*,partyB.*,projectStage.*,category.*");
        } else if (contractExtBO.getContractCode() != null) {
            contractBO = DS.findOne(YzContractBO.class, "*,category.*", "contractCode = ?", contractExtBO.getContractCode());
        } else if (contractExtBO.getExternalCode() != null) {
            contractBO = DS.findOne(YzContractBO.class, "*,category.*", "externalCode = ?", contractExtBO.getExternalCode());
        }

        // 国际化
        if (contractBO != null) {
            contractBO.setCanModifyStraightInAndOut(true);
            // 获取合同类型
            if (StringUtils.isNotBlank(contractBO.getContractTypeDict()) &&  StringUtils.equals(contractBO.getContractTypeDict() , YzContractTypeDict.RENT)){
                // 租赁合同
                contractBO.setIncomingMaterialType(IncomingMaterialTypeDict.LEASE);
            }else{
                // 判断有没有库中是否有值
                if (contractBO.getIsStraightInAndOut() != null){
                    if (contractBO.getIsStraightInAndOut()) {
                        contractBO.setIncomingMaterialType(IncomingMaterialTypeDict.IS_STRAIGHT_IN_AND_OUT_TRUE);
                        //合同选了直入直出，但是合同没有"混凝土"字样 或者品类不在"混凝土"范围内，就判定业务选错类型了，不设默认值，让业务自己选！
                        if (!contractBO.getContractName().contains("混凝土")) {
                            contractBO.setIncomingMaterialType(null);
                        }
//                        if (!contractBO.getContractName().contains("混凝土") ||
//                                contractBO.getCategory() == null ||
//                                StringUtils.isNotEmpty(contractBO.getCategory().getPath()) ||
//                                !contractBO.getCategory().getPath().contains("混凝土")) {
//                            contractBO.setIncomingMaterialType(null);
//                        }
                    } else {
                        contractBO.setIncomingMaterialType(IncomingMaterialTypeDict.IS_STRAIGHT_IN_AND_OUT_FALSE);
                        //合同选了非直入直出，但是合同有"混凝土"字样，就判定业务选错类型了，不设默认值，让业务自己选！
                        if (contractBO.getContractName().contains("混凝土")) {
                            contractBO.setIncomingMaterialType(null);
                        }
                    }

                }
            }
            // 如果当前合同业务类型是地产撮合，将是否可修改直入直出改为，不可修改
            if (YzContractBusinessTypeDict.ESTATE_MATCH_MAKING.equals(contractBO.getBusinessType())) {
                contractBO.setCanModifyStraightInAndOut(false);
            }

            contractBO.setContractTypeDict(DictUtil.getTranslate(contractBO.getContractTypeDict(), YzContractTypeDict.class));
            hasRelevantContractLines(contractBO);
            log.info("getIsStraightInAndOut ->>>>>> {}" , contractBO.getIsStraightInAndOut());
            log.info("getCanModifyStraightInAndOut ----> {}" , contractBO.getCanModifyStraightInAndOut());
            log.info("setIncomingMaterialType ----> {}" , contractBO.getIncomingMaterialType());
        }
        return contractBO;
    }



    private boolean hasRelevantContractLines(YzContractBO yzContractBO) {
        Query query = TSQL.selectFrom(ContractLineBO.class)
                .where(TSQL.field(ContractLineBO.contract_field).eq(yzContractBO.getId()));
        List<ContractLineBO> contractLineBOS = DS.findAll(query);
        if (CollectionUtils.isNotEmpty(contractLineBOS)){
            for (ContractLineBO contractLineBO : contractLineBOS) {
//                SkuBO skuBO = DS.findById(SkuBO.class, contractLineBO.getSku().getId());
//                CategoryBO categoryBO = DS.findById(CategoryBO.class, skuBO.getCategory().getId());
//                if (StringUtils.contains(categoryBO.getIdPath(), "0303") || StringUtils.contains(categoryBO.getIdPath(), "0304")){
//                    return true;
//                }
                BigDecimal totalSignQty = BigDecimal.ZERO;
                BigDecimal totalPlacedQty = BigDecimal.ZERO;
                if (CollectionUtil.isNotEmpty(contractLineBOS)) {
                    for (ContractLineBO lineBO : contractLineBOS) {
                        totalSignQty = totalSignQty.add(lineBO.getSignedQty());
                        totalPlacedQty = totalPlacedQty.add(lineBO.getPlacedQty());
                    }
                }
                yzContractBO.setSignedQty(totalSignQty);
                yzContractBO.setPlacedQty(totalPlacedQty);
            }
        }
        return false;
    }
}
