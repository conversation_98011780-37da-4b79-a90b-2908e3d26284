package io.terminus.gaia.app.b2b.contract.func.price.cal.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.gaia.app.b2b.contract.dict.price.*;
import io.terminus.gaia.app.b2b.contract.func.customermanager.read.QueryCustomerDiscountFactorFunc;
import io.terminus.gaia.app.b2b.contract.model.ContractLineBO;
import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.gaia.app.b2b.contract.model.YzContractBO;
import io.terminus.gaia.app.b2b.contract.model.price.SalePriceCalLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.QueryCustomerDiscountFactorTO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.SelfSpcCalEndpointTO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.SelfSpcCalPointTO;
import io.terminus.gaia.app.b2b.trade.tmodel.BigDecimalResult;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.gaia.contract.utils.JsonUtil;
import io.terminus.gaia.contract.utils.ThreadPoolUtil;
import io.terminus.gaia.item.dict.BulkStatusDict;
import io.terminus.gaia.item.dict.FluctutationType;
import io.terminus.gaia.item.dict.PricingRulesDict;
import io.terminus.gaia.item.func.price.read.QueryBasicPriceFunc;
import io.terminus.gaia.item.model.price.BasicPriceBO;
import io.terminus.gaia.item.model.sku.OneMouthfulLinePriceBO;
import io.terminus.gaia.item.model.sku.SkuBO;
import io.terminus.gaia.item.tmodel.price.BasicPriceQueryTO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.context.UserInfoContext;
import io.terminus.gaia.organization.model.ext.ProjectBO;
import io.terminus.gaia.organization.tmodel.UserInfoTO;
import io.terminus.trantorframework.api.type.Currency;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2024/4/2 17:35
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SelfSpcCalDispatchService {

    private final QueryBasicPriceFunc queryBasicPriceFunc;
    private final QueryCustomerDiscountFactorFunc queryCustomerDiscountFactorFunc;
    private final UserInfoContext userInfoContext;
    private final RedisTemplate<String, String> redisTemplate;

    private final static String REDIS_PREFIX = "SPU-PRICE:";
    private final static int REDIS_TIMEOUT = 30;

    public Map<Long, SelfSpcCalPointTO> calForSku(List<SelfSpcCalEndpointTO> req) {
        if (CollUtil.isEmpty(req)) {
            return new HashMap<>();
        }

        List<CompletableFuture<Map<Long, SelfSpcCalPointTO>>> completableFutures = new ArrayList<>();

        for (SelfSpcCalEndpointTO endpoint : req) {
            CompletableFuture<Map<Long, SelfSpcCalPointTO>> future = CompletableFuture.supplyAsync(() -> {
                log.info("当前处理的数据为: spuId：{}, 整体算价参数:{}", endpoint.getSkuBO().getSpu().getId(), JsonUtil.getJsonExcludeRootFields(endpoint));

                // 是否命中缓存
                Map<Long, SelfSpcCalPointTO> cache = getCache(endpoint);
                if (Objects.nonNull(cache)) {
                    return cache;
                }

                return dispatch(endpoint);
            }, ThreadPoolUtil.calPriceSelfExecutor);

            completableFutures.add(future);
        }

        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
        HashMap<Long, SelfSpcCalPointTO> result = new HashMap<>();
        completableFutures.stream().map(CompletableFuture::join).forEach(result::putAll);
        return result;
    }

    private Map<Long, SelfSpcCalPointTO> dispatch(SelfSpcCalEndpointTO endpoint) {
        // 切换账期算价，结算切换账期，需要拿到账期重新算价
        Map<Long, SelfSpcCalPointTO> changePaymentTemplateResMap = calChangePaymentTemplate(endpoint);
        if (CollUtil.isNotEmpty(changePaymentTemplateResMap)) {
            // 计算折扣系数
            changePaymentTemplateResMap = solveDiscountCoefficient(endpoint, changePaymentTemplateResMap);
            setCache(endpoint, changePaymentTemplateResMap, "redis key：{}, 缓存完成 calChangePaymentTemplate", "切换账期算价，结算切换账期，需要拿到账期重新算价 合同id:{} ,命中数据:{}");
            return changePaymentTemplateResMap;
        }

        // 存在命中的测算,或者有合同了，用于合同下单，账期算价，只需要从账期取价格即可
        log.info("SelfSpcCalDispatchService.calForSku 执行 calHitSalePriceCalLine");
        Map<Long, SelfSpcCalPointTO> hitSalePriceCalLineMap = calHitSalePriceCalLine(endpoint);
        if (CollUtil.isNotEmpty(hitSalePriceCalLineMap)) {
            // 计算折扣系数
            hitSalePriceCalLineMap = solveDiscountCoefficient(endpoint, hitSalePriceCalLineMap);
            setCache(endpoint, hitSalePriceCalLineMap, "redis key：{}, 缓存完成 calHitSalePriceCalLine", "存在命中的测算,或者有合同了，用于合同下单，账期算价，只需要从账期取价格即可 合同id:{} ,命中数据:{}");
            return hitSalePriceCalLineMap;
        }

        // 正常算价
        Map<Long, SelfSpcCalPointTO> res = calNormalPrice(endpoint);
        // 计算优惠系数
        Map<Long, SelfSpcCalPointTO> selfSpcCalPointTOMap = solveOtherDiscountCoefficient(endpoint, res);
        setCache(endpoint, selfSpcCalPointTOMap, "redis key：{}, 缓存完成 solveOtherDiscountCoefficient", "正常算价 合同id:{} ,命中数据:{}");
        return selfSpcCalPointTOMap;
    }

    private Map<Long, SelfSpcCalPointTO> getCache(SelfSpcCalEndpointTO endpoint) {
        if (Objects.equals(endpoint.getUseCache(), false)) {
            return null;
        }

        String redisKey = getRedisKey(endpoint);
        String spuPriceInfo = redisTemplate.opsForValue().get(redisKey);
        log.info("redis Key命中返回 redisKey:{},value={}", redisKey, spuPriceInfo);
        if (StrUtil.isBlank(spuPriceInfo)) {
            return null;
        }

        Map<Long, SelfSpcCalPointTO> hitCacheItem = JSON.parseObject(spuPriceInfo, new TypeReference<Map<Long, SelfSpcCalPointTO>>() {
        });

        return hitCacheItem;
    }

    private void setCache(SelfSpcCalEndpointTO endpoint, Map<Long, SelfSpcCalPointTO> selfSpcCalPointTOMap, String format, String format1) {
        redisTemplate.opsForValue().set(getRedisKey(endpoint), JSON.toJSONString(selfSpcCalPointTOMap), getRandomExpireTime(REDIS_TIMEOUT, 30), TimeUnit.MINUTES);
        log.info(format, getRedisKey(endpoint));
        log.info(format1, endpoint.getSkuBO().getSpu().getId(), JsonUtil.getJsonExcludeRootFields(selfSpcCalPointTOMap));
    }

    private static final Random RANDOM = new Random();

    /**
     * 获取带有随机偏移的缓存过期时间（单位：分钟）
     *
     * @param baseTimeoutMinutes 固定过期时间（分钟）
     * @param randomDeltaMinutes 最大随机偏移量（分钟）
     * @return 实际过期时间（秒）
     */
    public static long getRandomExpireTime(int baseTimeoutMinutes, int randomDeltaMinutes) {
        return baseTimeoutMinutes + RANDOM.nextInt(randomDeltaMinutes + 1);
    }

    private Map<Long, SelfSpcCalPointTO> calNormalPrice(SelfSpcCalEndpointTO endpoint) {
        // 计算一口价
        Map<Long, SelfSpcCalPointTO> oneMousePriceMap = calOneMousePrice(ListUtil.toList(endpoint.getSkuBO()), endpoint.getPayableSchemeTemplateId(), endpoint.getDistrictBO());
        if (CollUtil.isNotEmpty(oneMousePriceMap)) {
            return oneMousePriceMap;
        }

        // 计算无一口价的sku
        Set<Long> unOneMouseSpuIdSet = new HashSet<>();
        unOneMouseSpuIdSet.add(endpoint.getSkuBO().getSpu().getId());
        Map<Long, SelfSpcCalPointTO> calRes = calUnOneMouseSpuIdSet(unOneMouseSpuIdSet, endpoint);

        Map<Long, SelfSpcCalPointTO> res = covertToSkuMap(calRes, endpoint.getSkuBO());
        return res;
    }

    private Map<Long, SelfSpcCalPointTO> solveOtherDiscountCoefficient(SelfSpcCalEndpointTO endpoint, Map<Long, SelfSpcCalPointTO> res) {
        // 云筑调用不计算优惠
        if (Objects.equals(endpoint.getQueryType(), SpcQueryTypeDict.YUN_ZHU)) {
            return res;
        }

        Long categoryId = Opt.ofNullable(endpoint.getSkuBO()).map(SkuBO::getCategory).map(CategoryBO::getId).orElse(null);
        Long companyId = Opt.ofNullable(endpoint.getCurrentUserInfo()).map(UserInfoTO::getEntity).map(EntityBO::getId).orElse(null);
        if (Objects.isNull(categoryId) || Objects.isNull(companyId)) {
            return res;
        }

        QueryCustomerDiscountFactorTO queryCustomerDiscountFactorTO = new QueryCustomerDiscountFactorTO();
        queryCustomerDiscountFactorTO.setProjectId(Opt.ofNullable(endpoint.getProjectBO()).map(ProjectBO::getId).orElse(null));
        queryCustomerDiscountFactorTO.setCategoryId(endpoint.getSkuBO().getCategory().getId());
        queryCustomerDiscountFactorTO.setCompanyId(companyId);
        BigDecimalResult coefficientResult = queryCustomerDiscountFactorFunc.execute(queryCustomerDiscountFactorTO);
        log.info("命中优惠系数,sku={},coefficientResult={},queryCustomerDiscountFactorTO={}", endpoint.getSkuBO().getId(), coefficientResult, JSON.toJSONString(queryCustomerDiscountFactorTO));

        if (Objects.isNull(coefficientResult) || Objects.isNull(coefficientResult.getValue())) {
            return res;
        }


        for (Map.Entry<Long, SelfSpcCalPointTO> entry : res.entrySet()) {
            SelfSpcCalPointTO value = entry.getValue();
            if (Objects.equals(value.getCalPass(), false)) {
                continue;
            }

            BigDecimal coefficient = coefficientResult.getValue().divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP);

            BigDecimal withoutTax = value.getWithoutTaxAmt().multiply(coefficient).setScale(4, RoundingMode.HALF_UP);
            BigDecimal withTax = value.getWithTaxAmt().multiply(coefficient).setScale(4, RoundingMode.HALF_UP);
            value.setWithoutTaxAmt(withoutTax);
            value.setWithTaxAmt(withTax);
        }
        return res;

    }

    /**
     * 处理优惠系数
     *
     * @param endpoint
     * @param selfSpcCalPointTOMap
     * @return
     */
    private Map<Long, SelfSpcCalPointTO> solveDiscountCoefficient(SelfSpcCalEndpointTO endpoint, Map<Long, SelfSpcCalPointTO> selfSpcCalPointTOMap) {
        ContractLineBO contractLineBO = endpoint.getContractLineBO();
        if (Objects.isNull(contractLineBO.getDiscountFactor())) {
            return selfSpcCalPointTOMap;
        }

        for (Map.Entry<Long, SelfSpcCalPointTO> entry : selfSpcCalPointTOMap.entrySet()) {
            SelfSpcCalPointTO value = entry.getValue();
            if (Objects.equals(value.getCalPass(), false)) {
                continue;
            }

            BigDecimal coefficient = contractLineBO.getDiscountFactor().divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP);

            BigDecimal withoutTax = value.getWithoutTaxAmt().multiply(coefficient).setScale(4, RoundingMode.HALF_UP);
            BigDecimal withTax = value.getWithTaxAmt().multiply(coefficient).setScale(4, RoundingMode.HALF_UP);
            value.setWithoutTaxAmt(withoutTax);
            value.setWithTaxAmt(withTax);
        }
        return selfSpcCalPointTOMap;
    }

    private Map<Long, SelfSpcCalPointTO> calChangePaymentTemplate(SelfSpcCalEndpointTO endpoint) {
        if (Objects.isNull(endpoint.getContractBO()) || Objects.isNull(endpoint.getPayableSchemeTemplateId())) {
            log.info("SelfSpcCalDispatchService.calChangePaymentTemplate 参数校验不通过 ");
            return null;
        }

        // 账期和合同上的账期一致,不走切换账期算价
        if (Objects.equals(endpoint.getContractBO().getMainPayableSchemeId(), endpoint.getPayableSchemeTemplateId())) {
            log.info("SelfSpcCalDispatchService.calForSku.calChangePaymentTemplate 账期和合同上的账期一致,不走切换账期算价 , 代码返回");
            return null;
        }

        if (Objects.isNull(endpoint.getContractLineBO())) {
            log.info("SelfSpcCalDispatchService.calForSku.calChangePaymentTemplate 存在合同行不存在{} , 代码返回", endpoint);
            throw new BusinessException("数据异常");
        }


        //命中一口价
        Map<Long, SelfSpcCalPointTO> selfSpcCalPointTOMap = solveChangeOneMousePrice(endpoint);
        if (CollUtil.isNotEmpty(selfSpcCalPointTOMap)) {
            return selfSpcCalPointTOMap;
        }


        // 命中区域价或浮动价
        Map<Long, SelfSpcCalPointTO> basicCalResMap = solveChangeSaleAreaOrBasicPrice(endpoint);
        if (CollUtil.isNotEmpty(basicCalResMap)) {
            return basicCalResMap;
        }

        return Collections.emptyMap();
    }

    /**
     * 命中销售测算行
     *
     * @param endpoint
     * @return
     */
    private Map<Long, SelfSpcCalPointTO> calHitSalePriceCalLine(SelfSpcCalEndpointTO endpoint) {
        if (Objects.isNull(endpoint.getContractBO())) {
            return null;
        }

        if (Objects.isNull(endpoint.getContractLineBO())) {
            log.error("存在合同行不存在,{}", endpoint);
            throw new BusinessException("数据异常");
        }

        //命中一口价
        Map<Long, SelfSpcCalPointTO> selfSpcCalPointTOMap = solveHitOneMousePrice(endpoint);
        if (CollUtil.isNotEmpty(selfSpcCalPointTOMap)) {
            return selfSpcCalPointTOMap;
        }

        if (Objects.isNull(endpoint.getSalePriceCalLineBO())) {
            log.error("存在合同但是测算行不存在,{}", endpoint);
            throw new BusinessException("数据异常");
        }

        // 命中区域价或浮动价
        Map<Long, SelfSpcCalPointTO> basicCalResMap = solveHitSaleAreaOrBasicPrice(endpoint);
        if (CollUtil.isNotEmpty(basicCalResMap)) {
            return basicCalResMap;
        }

        return Collections.emptyMap();
    }

    /**
     * 命中区域价或浮动价
     */
    private Map<Long, SelfSpcCalPointTO> solveHitSaleAreaOrBasicPrice(SelfSpcCalEndpointTO endpoint) {
        SalePriceCalLineBO salePriceCalLineBO = endpoint.getSalePriceCalLineBO();
        if (Objects.isNull(salePriceCalLineBO)) {
            return Collections.emptyMap();
        }

        Map<Long, SalePriceCalLineBO> priceCalLineParams = Collections.singletonMap(endpoint.getSkuBO().getSpu().getId(), salePriceCalLineBO);

        Map<Long, SelfSpcCalPointTO> basicPriceCalWithSpu;
        if (Objects.equals(salePriceCalLineBO.getPriceType(), SpcPriceSchemeTypeDict.BASIC)) {
            basicPriceCalWithSpu = calBasicPrice(endpoint, priceCalLineParams);
        } else if (Objects.equals(salePriceCalLineBO.getPriceType(), SpcPriceSchemeTypeDict.ELECTRIC_WIRE_AND_CABLE)) {
            basicPriceCalWithSpu = calElectricWireAndCablePrice(endpoint, priceCalLineParams);
        } else {
            basicPriceCalWithSpu = calAreaPrice(endpoint, priceCalLineParams);
        }

        Map<Long, SelfSpcCalPointTO> result = covertToSkuMap(basicPriceCalWithSpu, endpoint.getSkuBO());

        return result;
    }


    /**
     * 命中一口价
     */
    private Map<Long, SelfSpcCalPointTO> solveHitOneMousePrice(SelfSpcCalEndpointTO endpoint) {
        // 未命中一口价
        if (!Objects.equals(endpoint.getContractLineBO().getPriceType(), SelfSpcCalTypeDict.ONE_MOUSE)) {
            return Collections.emptyMap();
        }

        SkuBO skuBO = endpoint.getSkuBO();
        YzContractBO contractBO = endpoint.getContractBO();
        ContractLineBO contractLineBO = endpoint.getContractLineBO();

        Optional<PayableSchemeTemplateBO> payableSchemeTemplateOpt = contractLineBO.getPayableSchemeTemplates().stream().filter(it -> Objects.equals(contractBO.getMainPayableSchemeId(), it.getId())).findFirst();
        if (!payableSchemeTemplateOpt.isPresent()) {
            log.error("修改账期算价,未找到账期模板,{}", endpoint);
            throw new BusinessException("数据异常");
        }
        SelfSpcCalPointTO selfSpcCalPoint = getSelfSpcCalPointTO(payableSchemeTemplateOpt.get(), endpoint);

        return Collections.singletonMap(skuBO.getId(), selfSpcCalPoint);
    }

    private Map<Long, SelfSpcCalPointTO> noPayableScheme(SelfSpcCalEndpointTO endpoint) {
        SelfSpcCalPointTO selfSpcCalPoint = new SelfSpcCalPointTO();
        selfSpcCalPoint.setSkuCode(endpoint.getSkuBO().getSkuCode());
        selfSpcCalPoint.setSkuId(endpoint.getSkuBO().getId());
        selfSpcCalPoint.setCalPass(false);
        selfSpcCalPoint.setCalFailReason("无法获取账期");
        selfSpcCalPoint.setFailType(CalFailTypeDict.OTHER);


        return Collections.singletonMap(endpoint.getSkuBO().getId(), selfSpcCalPoint);
    }

    /**
     * 定义缓存key
     *
     * @param endpoint
     * @return
     */
    private String getRedisKey(SelfSpcCalEndpointTO endpoint) {
        List<String> keyParts = new ArrayList<>();
        keyParts.add(REDIS_PREFIX);

        // 使用 Opt.ofNullable 处理所有可能为 null 的元素
        Opt.ofNullable(endpoint.getSkuBO())
                .map(SkuBO::getId)
                .map(Object::toString)
                .ifPresent(keyParts::add);

        Opt.ofNullable(endpoint.getProjectBO())
                .map(ProjectBO::getId)
                .map(Object::toString)
                .ifPresent(keyParts::add);

        Opt.ofNullable(endpoint.getPayableSchemeTemplateId())
                .map(Object::toString)
                .ifPresent(keyParts::add);

        Opt.ofNullable(endpoint.getDistrictBO())
                .map(DistrictBO::getId)
                .map(Object::toString)
                .ifPresent(keyParts::add);

        Opt.ofNullable(endpoint.getBasicPrice())
                .map(Object::toString)
                .ifPresent(keyParts::add);

        Opt.ofNullable(endpoint.getBrandBO())
                .map(BrandBO::getId)
                .map(Object::toString)
                .ifPresent(keyParts::add);

        String redisKey = String.join("-", keyParts);
        log.info("组装rediskey endpoint:{}, rediskey:{}",
                JSON.toJSONString(endpoint),
                redisKey);
        return redisKey;
    }

    /**
     * 修改账期，重新计算浮动价或区域价
     *
     * @param endpoint
     * @return
     */
    private Map<Long, SelfSpcCalPointTO> solveChangeSaleAreaOrBasicPrice(SelfSpcCalEndpointTO endpoint) {
        log.info("SelfSpcCalDispatchService.calForSku.calChangePaymentTemplate.solveChangeSaleAreaOrBasicPrice is running");
        Optional<PayableSchemeTemplateBO> payableSchemeTemplateOpt = endpoint.getContractLineBO()
                .getPayableSchemeTemplates()
                .stream()
                .filter(it -> Objects.equals(endpoint.getPayableSchemeTemplateId(), it.getId()))
                .findFirst();


        PayableSchemeTemplateBO payableSchemeTemplateBO = payableSchemeTemplateOpt.orElseGet(null);

        if (Objects.isNull(payableSchemeTemplateBO) || Objects.isNull(payableSchemeTemplateBO.getSalePriceCalLineId())) {
            return Collections.emptyMap();
        }

        SalePriceCalLineBO salePriceCalLineBO = DS.findOne(SalePriceCalLineBO.class, "*", "id=?", payableSchemeTemplateBO.getSalePriceCalLineId());

        Map<Long, SalePriceCalLineBO> priceCalLineParams = Collections.singletonMap(endpoint.getSkuBO().getSpu().getId(), salePriceCalLineBO);

        Map<Long, SelfSpcCalPointTO> basicPriceCalWithSpu;
        if (Objects.equals(salePriceCalLineBO.getPriceType(), SpcPriceSchemeTypeDict.BASIC)) {
            basicPriceCalWithSpu = calBasicPrice(endpoint, priceCalLineParams);
        } else if (Objects.equals(salePriceCalLineBO.getPriceType(), SpcPriceSchemeTypeDict.ELECTRIC_WIRE_AND_CABLE)) {
            basicPriceCalWithSpu = calElectricWireAndCablePrice(endpoint, priceCalLineParams);
        } else {
            basicPriceCalWithSpu = calAreaPrice(endpoint, priceCalLineParams);
        }

        Map<Long, SelfSpcCalPointTO> result = covertToSkuMap(basicPriceCalWithSpu, endpoint.getSkuBO());
        return result;
    }

    /**
     * 计算大宗商品价格
     * 自营销售定价：(延米铜价+辅材及其他费用)*销售折扣系数+浮动价（*浮动价）；延米铜价=含铜量*铜基价/1000
     * 自营销售价格计算时，销售折扣系数替换采购折扣系数，销售折扣数据取值逻辑：区域、账期、标品、品牌查询唯一的一条标品销售测算明细
     *
     * @param endpoint
     * @param salePriceCalLineMap
     * @return
     */
    private Map<Long, SelfSpcCalPointTO> calElectricWireAndCablePrice(SelfSpcCalEndpointTO endpoint, Map<Long, SalePriceCalLineBO> salePriceCalLineMap) {
        if (salePriceCalLineMap.isEmpty()) {
            return Collections.emptyMap();
        }

        // 没有传基准价，通过日期获取
        BasicPriceBO basicPriceBO = null;
        if (Objects.isNull(endpoint.getBasicPrice()) && Objects.nonNull(endpoint.getDate())) {
            basicPriceBO = queryElectricWireAndCableBasicPrice(endpoint);
            endpoint.setBasicPrice(basicPriceBO.getPriceWithTax().getValue());
        }

        Assert.notNull(endpoint.getBasicPrice(), ExceptionUtil.create("大宗商品算价，铜基价不能为空！"));

        Map<Long, SelfSpcCalPointTO> result = new HashMap<>();

        for (Map.Entry<Long, SalePriceCalLineBO> entry : salePriceCalLineMap.entrySet()) {
            Long spuId = entry.getKey();
            SalePriceCalLineBO salePriceCalLineBO = entry.getValue();

            SelfSpcCalPointTO selfSpcCalPoint = new SelfSpcCalPointTO();
            selfSpcCalPoint.setSkuCode(endpoint.getSkuBO().getSkuCode());
            selfSpcCalPoint.setSpuId(spuId);
            selfSpcCalPoint.setSalePriceCalLineId(salePriceCalLineBO.getId());
            selfSpcCalPoint.setBasicPriceBO(basicPriceBO);

            try {
                // 获取铜基价
                BigDecimal copperBasePrice = endpoint.getBasicPrice();
                if (copperBasePrice == null) {
                    log.error("铜基价为空, spuId: {}", spuId);
                    result.put(spuId, selfSpcCalPoint.setCalFail("铜基价为空", CalFailTypeDict.OTHER));
                    continue;
                }

                // 获取含铜量
                BigDecimal rawMaterialContent = salePriceCalLineBO.getRawMaterialContent();
                if (rawMaterialContent == null) {
                    log.error("含铜量为空, spuId: {}", spuId);
                    result.put(spuId, selfSpcCalPoint.setCalFail("含铜量为空", CalFailTypeDict.OTHER));
                    continue;
                }

                // 获取辅材及其他费用
                BigDecimal salesRelatedCosts = salePriceCalLineBO.getSalesRelatedCosts();
                if (salesRelatedCosts == null) {
                    log.error("辅材及其他费用为空, spuId: {}", spuId);
                    result.put(spuId, selfSpcCalPoint.setCalFail("辅材及其他费用为空", CalFailTypeDict.OTHER));
                    continue;
                }

                // 获取销售折扣系数
                BigDecimal salesDiscFactor = salePriceCalLineBO.getSalesDiscFactor();
                if (salesDiscFactor == null) {
                    log.error("销售折扣系数为空, spuId: {}", spuId);
                    result.put(spuId, selfSpcCalPoint.setCalFail("销售折扣系数为空", CalFailTypeDict.OTHER));
                    continue;
                }

                // 计算延米铜价 = 含铜量*铜基价/1000
                BigDecimal copperPricePerMeter = rawMaterialContent.multiply(copperBasePrice)
                        .divide(new BigDecimal("1000"), 6, RoundingMode.HALF_UP);
                log.info("电线电缆价格计算 - 延米铜价计算: spuId={}, 含铜量={}, 铜基价={}, 延米铜价={} (公式: 含铜量*铜基价/1000 = {}*{}/1000 = {})",
                        spuId, rawMaterialContent, copperBasePrice, copperPricePerMeter,
                        rawMaterialContent, copperBasePrice, copperPricePerMeter);

                // 计算含税价格 = (延米铜价+辅材及其他费用)*销售折扣系数
                BigDecimal withTaxAmt = copperPricePerMeter.add(salesRelatedCosts)
                        .multiply(salesDiscFactor)
                        .setScale(4, RoundingMode.HALF_UP);
                log.info("电线电缆价格计算 - 含税价格计算: spuId={}, 延米铜价={}, 辅材及其他费用={}, 销售折扣系数={}, 含税价格={} (公式: (延米铜价+辅材及其他费用)*销售折扣系数 = ({}+{})*{} = {})",
                        spuId, copperPricePerMeter, salesRelatedCosts, salesDiscFactor, withTaxAmt,
                        copperPricePerMeter, salesRelatedCosts, salesDiscFactor, withTaxAmt);

                // 获取税率
                BigDecimal taxRate = Opt.ofNullable(endpoint.getTaxRate()).orElse(new BigDecimal("13"))
                        .divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

                // 计算不含税价格
                BigDecimal withoutTaxAmt = withTaxAmt.divide(BigDecimal.ONE.add(taxRate), 4, RoundingMode.HALF_UP);

                // 设置计算结果
                selfSpcCalPoint.setCalPass(true);
                selfSpcCalPoint.setWithoutTaxAmt(withoutTaxAmt);
                selfSpcCalPoint.setWithTaxAmt(withTaxAmt);
                selfSpcCalPoint.setPriceType(SelfSpcCalTypeDict.ELECTRIC_WIRE_AND_CABLE);
                selfSpcCalPoint.setRawMaterialContent(rawMaterialContent);
                selfSpcCalPoint.setSalesRelatedCosts(salesRelatedCosts);
                selfSpcCalPoint.setSalesDiscFactor(salesDiscFactor);
                selfSpcCalPoint.setFluctuationType(salePriceCalLineBO.getFluctuationType());

                result.put(spuId, selfSpcCalPoint);

            } catch (Exception e) {
                log.error("计算电线电缆价格异常, spuId: {}, error: {}", spuId, e.getMessage());
                result.put(spuId, selfSpcCalPoint.setCalFail("计算异常: " + e.getMessage(), CalFailTypeDict.OTHER));
            }
        }

        return result;
    }

    /**
     * 查询最近7天的基准价
     */
    private BasicPriceBO queryElectricWireAndCableBasicPrice(SelfSpcCalEndpointTO endpoint) {
        Query query = TSQL.select(TSQL.field("*"))
                .from(BasicPriceBO.class).where(TSQL.field(BasicPriceBO.bulkRawMaterialType_field).isNotNull())
                .and(TSQL.field(BasicPriceBO.pricePubDate_field).ge(DateUtil.beginOfDay(DateUtil.offsetDay(endpoint.getDate(), -7))))
                .and(TSQL.field(BasicPriceBO.pricePubDate_field).le(DateUtil.endOfDay(endpoint.getDate())))
                .and(TSQL.field(BasicPriceBO.bulkStatus_field).eq(BulkStatusDict.ENABLE))
                .orderBy(TSQL.field(BasicPriceBO.pricePubDate_field).desc());

        List<BasicPriceBO> all = DS.findAll(query);
        BasicPriceBO basicPriceBO = CollUtil.getFirst(all);

        return basicPriceBO;
    }

    /**
     * 修改账期重新计算一口价
     *
     * @param endpoint
     * @return
     */
    private Map<Long, SelfSpcCalPointTO> solveChangeOneMousePrice(SelfSpcCalEndpointTO endpoint) {
        // 未命中一口价
        if (!Objects.equals(endpoint.getContractLineBO().getPriceType(), SelfSpcCalTypeDict.ONE_MOUSE)) {
            return Collections.emptyMap();
        }

        SkuBO skuBO = endpoint.getSkuBO();
        ContractLineBO contractLineBO = endpoint.getContractLineBO();

        Optional<PayableSchemeTemplateBO> payableSchemeTemplateOpt = contractLineBO.getPayableSchemeTemplates().stream().filter(it -> Objects.equals(endpoint.getPayableSchemeTemplateId(), it.getId())).findFirst();
        if (!payableSchemeTemplateOpt.isPresent()) {
            log.error("修改账期算价,未找到账期模板,{}", endpoint);
            throw new BusinessException("数据异常");
        }
        SelfSpcCalPointTO selfSpcCalPoint = getSelfSpcCalPointTO(payableSchemeTemplateOpt.get(), endpoint);

        return Collections.singletonMap(skuBO.getId(), selfSpcCalPoint);
    }

    private SelfSpcCalPointTO getSelfSpcCalPointTO(PayableSchemeTemplateBO payableSchemeTemplate, SelfSpcCalEndpointTO endpoint) {
        SkuBO skuBO = endpoint.getSkuBO();
        SelfSpcCalPointTO selfSpcCalPoint = new SelfSpcCalPointTO();
        selfSpcCalPoint.setSkuId(skuBO.getId());
        selfSpcCalPoint.setSkuCode(skuBO.getSkuCode());
        selfSpcCalPoint.setCalPass(true);
        selfSpcCalPoint.setWithTaxAmt(payableSchemeTemplate.getOneMouthPriceWithTax());
        selfSpcCalPoint.setWithoutTaxAmt(payableSchemeTemplate.getOneMouthPriceWithoutTax());
        selfSpcCalPoint.setPriceType(SelfSpcCalTypeDict.ONE_MOUSE);
        return selfSpcCalPoint;
    }

    private Map<Long, SelfSpcCalPointTO> covertToSkuMap(Map<Long, SelfSpcCalPointTO> map, SkuBO skuBO) {
        SelfSpcCalPointTO selfSpcCalPointTO = map.get(skuBO.getSpu().getId());
        selfSpcCalPointTO.setSkuId(skuBO.getId());
        selfSpcCalPointTO.setSkuCode(skuBO.getSkuCode());
        Map<Long, SelfSpcCalPointTO> result = new HashMap<>();
        result.put(skuBO.getId(), selfSpcCalPointTO);
        return result;
    }


    /**
     * 计算非一口价
     *
     * @return
     */
    private Map<Long, SelfSpcCalPointTO> calUnOneMouseSpuIdSet(Set<Long> unOneMouseSpuIdSet, SelfSpcCalEndpointTO req) {
        // 查询销售测算行
        List<SalePriceCalLineBO> salePriceCalLineList;
        if (Objects.nonNull(req.getPayableSchemeTemplateId())) {
            salePriceCalLineList = DS.findAll(SalePriceCalLineBO.class, "*,payableSchemeTemplateBO.*", "spu in (?) and payableSchemeTemplateBO=? and status=?", unOneMouseSpuIdSet, req.getPayableSchemeTemplateId(), SalePriceCalStatusDict.ENABLED);
        } else {
            salePriceCalLineList = DS.findAll(SalePriceCalLineBO.class, "*,payableSchemeTemplateBO.*", "spu in (?) and status=?", unOneMouseSpuIdSet, SalePriceCalStatusDict.ENABLED);
        }
        Map<Long, List<SalePriceCalLineBO>> salLineSpuIdGroupMap = CollStreamUtil.groupBy(salePriceCalLineList, it -> it.getSpu().getId(), Collectors.toList());

        // 浮动价计算
        Map<Long, SalePriceCalLineBO> calBasicSkuMap = new HashMap<>();
        // 区域价计算
        Map<Long, SalePriceCalLineBO> calAreaSkuMap = new HashMap<>();
        // 大宗计算
        Map<Long, SalePriceCalLineBO> calElectricWireAndCableSkuMap = new HashMap<>();
        // 无法匹配测算行的sku
        HashSet<Long> unMatchSpuSet = new HashSet<>();

        // 需要匹配的区域和价格类型分组
        List<Long> districtListForMatchTemp = Arrays.stream(req.getDistrictBO().getIdPath().split("/")).map(Long::parseLong).collect(Collectors.toList());
        List<Long> districtListForMatch = CollUtil.reverse(districtListForMatchTemp);

        // 按照是否匹配区域，和 浮动价固定价分开计算
        for (Long spuId : unOneMouseSpuIdSet) {
            List<SalePriceCalLineBO> priceCalLineBOS = salLineSpuIdGroupMap.get(spuId);
            if (CollUtil.isEmpty(priceCalLineBOS)) {
                unMatchSpuSet.add(spuId);
                break;
            }

            // 匹配地区
            SalePriceCalLineBO salePriceCalLine = matchDistrict(districtListForMatch, priceCalLineBOS, SalePriceCalLineBO::getDistrictBO);
            // 查找匹配的最优惠的价格
            DistrictBO districtBO = Opt.ofNullable(salePriceCalLine).map(SalePriceCalLineBO::getDistrictBO).orElse(null);
            String priceType = Opt.ofNullable(salePriceCalLine).map(SalePriceCalLineBO::getPriceType).orElse(null);
            salePriceCalLine = findMinSalePrice(districtBO, priceCalLineBOS, req, priceType);

            if (Objects.isNull(salePriceCalLine)) {
                unMatchSpuSet.add(spuId);
                break;
            }

            if (Objects.equals(salePriceCalLine.getPriceType(), SpcPriceSchemeTypeDict.BASIC)) {
                calBasicSkuMap.put(spuId, salePriceCalLine);
            } else if (Objects.equals(salePriceCalLine.getPriceType(), SpcPriceSchemeTypeDict.ELECTRIC_WIRE_AND_CABLE)) {
                calElectricWireAndCableSkuMap.put(spuId, salePriceCalLine);
            } else {
                calAreaSkuMap.put(spuId, salePriceCalLine);
            }
        }


        // 计算浮动价
        Map<Long, SelfSpcCalPointTO> basicPriceMap = calBasicPrice(req, calBasicSkuMap);
        // 计算区域价
        Map<Long, SelfSpcCalPointTO> areaPriceMap = calAreaPrice(req, calAreaSkuMap);
        // 计算大宗价
        Map<Long, SelfSpcCalPointTO> electricWireAndCablePriceMap = calElectricWireAndCablePrice(req, calElectricWireAndCableSkuMap);

        // 计算无法匹配的sku
        Map<Long, SelfSpcCalPointTO> unMatchSkuMap = calUnMatchSku(req, unMatchSpuSet);

        Map<Long, SelfSpcCalPointTO> res = new HashMap<>();
        res.putAll(basicPriceMap);
        res.putAll(areaPriceMap);
        res.putAll(electricWireAndCablePriceMap);
        res.putAll(unMatchSkuMap);
        return res;
    }

    /**
     * 计算浮动价
     *
     * @param req
     * @param salePriceCalLineMap
     * @return
     */
    private Map<Long, SelfSpcCalPointTO> calBasicPrice(SelfSpcCalEndpointTO req, Map<Long, SalePriceCalLineBO> salePriceCalLineMap) {
        if (salePriceCalLineMap.isEmpty()) {
            log.info("SelfSpcCalDispatchService.calForSku.calChangePaymentTemplate.solveChangeSaleAreaOrBasicPrice.calBasicPrice 参数为空 执行结束");
            return Collections.emptyMap();
        }

        // 查询基准价
        List<BasicPriceBO> basicPriceList = Collections.emptyList();
        try {
            basicPriceList = queryBasicPrice(req, salePriceCalLineMap);
        } catch (Exception e) {
            log.error("查询基准价异常", e);
        }

        Map<Long, BasicPriceBO> basicPriceMap = basicPriceList.stream().collect(Collectors.toMap(it -> it.getSpu().getId(), Function.identity()));


        HashMap<Long, SelfSpcCalPointTO> result = new HashMap<>();
        for (Map.Entry<Long, SalePriceCalLineBO> entry : salePriceCalLineMap.entrySet()) {
            Long spuId = entry.getKey();
            SalePriceCalLineBO salePriceCalLineBO = entry.getValue();
            BasicPriceBO basicPriceBO = basicPriceMap.get(spuId);

            SelfSpcCalPointTO selfSpcCalPoint = new SelfSpcCalPointTO();
            selfSpcCalPoint.setSpuId(spuId);
            selfSpcCalPoint.setSalePriceCalLineId(salePriceCalLineBO.getId());
            selfSpcCalPoint.setRawMaterialContent(salePriceCalLineBO.getRawMaterialContent());
            selfSpcCalPoint.setSalesRelatedCosts(salePriceCalLineBO.getSalesRelatedCosts());
            selfSpcCalPoint.setSalesDiscFactor(salePriceCalLineBO.getSalesDiscFactor());
            selfSpcCalPoint.setPriceType(salePriceCalLineBO.getPriceType());
            selfSpcCalPoint.setBasicPriceBO(basicPriceBO);
            result.put(spuId, selfSpcCalPoint);

            if (Objects.isNull(basicPriceBO)) {
                log.info("spuId:{},salePriceCalLineBO:{},basicPriceBO:{}", spuId, salePriceCalLineBO, basicPriceBO);
                selfSpcCalPoint.setCalPass(false);
                selfSpcCalPoint.setCalFailReason("未查询到基准价");
                selfSpcCalPoint.setFailType(CalFailTypeDict.BASIC_PRICE_FAIL);
                continue;
            }

            String fluctuationType = salePriceCalLineBO.getFluctuationType();
            if (Objects.isNull(fluctuationType)) {
                log.info("spuId:{},salePriceCalLineBO:{},basicPriceBO:{}", spuId, salePriceCalLineBO, basicPriceBO);
                selfSpcCalPoint.setCalPass(false);
                selfSpcCalPoint.setCalFailReason("数据异常,价格浮动方式为空");
                selfSpcCalPoint.setFailType(CalFailTypeDict.NO_RESULT);
                continue;
            }

            BigDecimal avgN = salePriceCalLineBO.getFinalCoefficientN();
            if (Objects.isNull(avgN)) {
                log.info("spuId:{},salePriceCalLineBO:{},basicPriceBO:{}", spuId, salePriceCalLineBO, basicPriceBO);
                selfSpcCalPoint.setCalPass(false);
                selfSpcCalPoint.setCalFailReason("数据异常,平均N为空");
                selfSpcCalPoint.setFailType(CalFailTypeDict.NO_RESULT);
                continue;
            }


            String pricingRules = salePriceCalLineBO.getPricingRules();
            if (Objects.isNull(pricingRules)) {
                log.info("spuId:{},salePriceCalLineBO:{},basicPriceBO:{}", spuId, salePriceCalLineBO, basicPriceBO);
                selfSpcCalPoint.setCalPass(false);
                selfSpcCalPoint.setCalFailReason("数据异常,取价规则为空");
                selfSpcCalPoint.setFailType(CalFailTypeDict.NO_RESULT);
                continue;
            }

            BigDecimal basicPriceWithTax = Opt.ofNullable(basicPriceBO.getPriceWithTax()).map(Currency::getValue).orElse(null);
            switch (pricingRules) {
                case PricingRulesDict.FIRST_PRICE:
                    BigDecimal firstPrice = Opt.ofNullable(basicPriceBO.getFirstPrice()).map(Currency::getValue).orElse(null);
                    basicPriceWithTax = Objects.nonNull(firstPrice) ? firstPrice : basicPriceWithTax;
                    break;
                case PricingRulesDict.LAST_PRICE:
                    BigDecimal lastPrice = Opt.ofNullable(basicPriceBO.getLastPrice()).map(Currency::getValue).orElse(null);
                    basicPriceWithTax = Objects.nonNull(lastPrice) ? lastPrice : basicPriceWithTax;
                    break;
                case PricingRulesDict.AVERAGE_PRICE:
                    BigDecimal averagePrice = Opt.ofNullable(basicPriceBO.getAveragePrice()).map(Currency::getValue).orElse(null);
                    basicPriceWithTax = Objects.nonNull(averagePrice) ? averagePrice : basicPriceWithTax;
                    break;
                default:
            }

            BigDecimal basicPriceWithoutTax = calWithoutTaxPrice(basicPriceWithTax, req.getTaxRate());
            basicPriceBO.setPriceWithTax(new Currency(basicPriceWithTax));
            basicPriceBO.setPriceWithoutTax(new Currency(basicPriceWithoutTax));
            if (Objects.equals(fluctuationType, FluctutationType.ADDITION)) {
                selfSpcCalPoint.setWithTaxAmt(basicPriceWithTax.add(avgN));
                selfSpcCalPoint.setWithoutTaxAmt(basicPriceWithoutTax.add(avgN));
            } else {
                BigDecimal avgNPercent = avgN.divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP);
                selfSpcCalPoint.setWithTaxAmt(basicPriceWithTax.multiply(avgNPercent).setScale(4, RoundingMode.HALF_UP));
                selfSpcCalPoint.setWithoutTaxAmt(basicPriceWithoutTax.multiply(avgNPercent).setScale(4, RoundingMode.HALF_UP));
            }

            selfSpcCalPoint.setFinalCoefficientN(avgN);
            selfSpcCalPoint.setPriceType(SelfSpcCalTypeDict.BASIC);
            selfSpcCalPoint.setCalPass(true);
            selfSpcCalPoint.setSalePriceCalLineId(salePriceCalLineBO.getId());
            selfSpcCalPoint.setRawMaterialContent(salePriceCalLineBO.getRawMaterialContent());
            selfSpcCalPoint.setSalesRelatedCosts(salePriceCalLineBO.getSalesRelatedCosts());
            selfSpcCalPoint.setSalesDiscFactor(salePriceCalLineBO.getSalesDiscFactor());

        }

        return result;
    }

    /**
     * 查询基准价
     */
    private List<BasicPriceBO> queryBasicPrice(SelfSpcCalEndpointTO req, Map<Long, SalePriceCalLineBO> salePriceCalLineMap) throws Exception {
        // 构造查询网价参数
        ArrayList<BasicPriceQueryTO> basicPriceQueryReq = new ArrayList<>();
        for (Map.Entry<Long, SalePriceCalLineBO> entry : salePriceCalLineMap.entrySet()) {
            Long spuId = entry.getKey();
            SalePriceCalLineBO salePriceCalLineBO = entry.getValue();

            BasicPriceQueryTO basicPriceQueryTO = new BasicPriceQueryTO();
            basicPriceQueryTO.setCalDate(req.getDate());
            basicPriceQueryTO.setPricePlatform(salePriceCalLineBO.getPlatform());
            basicPriceQueryTO.setBrandBO(req.getBrandBO());
            basicPriceQueryTO.setDistrictBO(req.getDistrictBO());
            basicPriceQueryTO.setSpuId(spuId);
            basicPriceQueryTO.setQueryBySpu(true);

            basicPriceQueryReq.add(basicPriceQueryTO);
        }
        List<BasicPriceBO> execute = queryBasicPriceFunc.execute(basicPriceQueryReq);
        return execute;

    }

    /**
     * 计算固定价
     */
    private Map<Long, SelfSpcCalPointTO> calAreaPrice(SelfSpcCalEndpointTO req, Map<Long, SalePriceCalLineBO> salePriceCalLineMap) {
        if (salePriceCalLineMap.isEmpty()) {
            return Collections.emptyMap();
        }


        Map<Long, SelfSpcCalPointTO> result = new HashMap<>();
        for (Map.Entry<Long, SalePriceCalLineBO> entry : salePriceCalLineMap.entrySet()) {
            Long spuId = entry.getKey();
            SalePriceCalLineBO salePriceCalLineBO = entry.getValue();

            SelfSpcCalPointTO selfSpcCalPoint = new SelfSpcCalPointTO();
            selfSpcCalPoint.setSkuId(spuId);
            selfSpcCalPoint.setCalPass(true);
            selfSpcCalPoint.setWithTaxAmt(salePriceCalLineBO.getRealSalePriceWithTax());
            selfSpcCalPoint.setWithoutTaxAmt(salePriceCalLineBO.getRealSalePriceWithoutTax());
            selfSpcCalPoint.setPriceType(SelfSpcCalTypeDict.AREA);
            selfSpcCalPoint.setSalePriceCalLineId(salePriceCalLineBO.getId());
            selfSpcCalPoint.setRawMaterialContent(salePriceCalLineBO.getRawMaterialContent());
            selfSpcCalPoint.setSalesRelatedCosts(salePriceCalLineBO.getSalesRelatedCosts());
            selfSpcCalPoint.setSalesDiscFactor(salePriceCalLineBO.getSalesDiscFactor());
            result.put(spuId, selfSpcCalPoint);
        }

        return result;
    }

    /**
     * 无法匹配测算的sku，查看有没有一口价
     */
    private Map<Long, SelfSpcCalPointTO> calUnMatchSku(SelfSpcCalEndpointTO req, Set<Long> unMatchSpuSet) {
        // 匹配一口价
        Map<Long, SelfSpcCalPointTO> result = unMatchSpuSet.stream().map(it -> {
            SelfSpcCalPointTO selfSpcCalPoint = new SelfSpcCalPointTO();
            selfSpcCalPoint.setCalPass(false);
            selfSpcCalPoint.setSpuId(it);
            selfSpcCalPoint.setCalFailReason("无法匹配一口价，无法匹配销售价格测算");
            selfSpcCalPoint.setFailType(CalFailTypeDict.NO_RESULT);
            return selfSpcCalPoint;
        }).collect(Collectors.toMap(SelfSpcCalPointTO::getSpuId, it -> it));
        return result;
    }


    /**
     * 计算一口价
     */
    private Map<Long, SelfSpcCalPointTO> calOneMousePrice(List<SkuBO> skuBOList, Long payableSchemeTemplateId, DistrictBO districtBO) {
        if (CollUtil.isEmpty(skuBOList)) {
            return Collections.emptyMap();
        }


        // 查询一口价
        Set<Long> skuIdSet = skuBOList.stream().map(SkuBO::getId).collect(Collectors.toSet());
        List<OneMouthfulLinePriceBO> oneMouthfulLinePriceBOList;
        if (Objects.nonNull(payableSchemeTemplateId)) {
            oneMouthfulLinePriceBOList = DS.findAll(OneMouthfulLinePriceBO.class, "*,districtBO.*", "sku in (?) and payableSchemeTemplateId=?", skuIdSet, payableSchemeTemplateId);
        } else {
            oneMouthfulLinePriceBOList = DS.findAll(OneMouthfulLinePriceBO.class, "*,districtBO.*", "sku in (?)", skuIdSet);
        }

        // 无一口价
        if (CollUtil.isEmpty(oneMouthfulLinePriceBOList)) {
            return Collections.emptyMap();
        }

        Map<Long, List<OneMouthfulLinePriceBO>> oneMouseGroupMap = oneMouthfulLinePriceBOList.stream().collect(Collectors.groupingBy(it -> it.getSku().getId()));

        // 需要匹配的区域
        List<Long> districtListForMatchTemp = Arrays.stream(districtBO.getIdPath().split("/")).map(Long::parseLong).collect(Collectors.toList());
        List<Long> districtListForMatch = CollUtil.reverse(districtListForMatchTemp);

        Map<Long, SelfSpcCalPointTO> calResSkuIdMap = new HashMap<>();
        for (SkuBO skuBO : skuBOList) {
            List<OneMouthfulLinePriceBO> priceCalLineBOS = oneMouseGroupMap.get(skuBO.getId());


            if (CollUtil.isEmpty(priceCalLineBOS)) {
                break;
            }

            // 匹配地区
            OneMouthfulLinePriceBO oneMouthfulLine = matchDistrict(districtListForMatch, priceCalLineBOS, OneMouthfulLinePriceBO::getDistrictBO);
            oneMouthfulLine = findMinSalePrice(oneMouthfulLine, priceCalLineBOS);

            if (Objects.isNull(oneMouthfulLine)) {
                break;
            }

            SelfSpcCalPointTO selfSpcCalPoint = new SelfSpcCalPointTO();
            selfSpcCalPoint.setSkuId(skuBO.getId());
            selfSpcCalPoint.setSkuCode(skuBO.getSkuCode());
            selfSpcCalPoint.setCalPass(true);
            selfSpcCalPoint.setWithTaxAmt(oneMouthfulLine.getAclSalePriceWithTax());
            selfSpcCalPoint.setWithoutTaxAmt(oneMouthfulLine.getAclSalePriceWithoutTax());
            selfSpcCalPoint.setPriceType(SelfSpcCalTypeDict.ONE_MOUSE);
            calResSkuIdMap.put(skuBO.getId(), selfSpcCalPoint);
        }

        return calResSkuIdMap;
    }


    private <T> T matchDistrict(List<Long> districtListForMatch, List<T> priceCalLineBOS, Function<T, DistrictBO> districtSupplier) {
        /*
         * 区域匹配，例如：传入南京，测算行为江苏，那么可以匹配，只需查看传入的地区的 本上级是否包含测算行的地区
         */
        T salePriceCalLine = null;
        for (Long districtId : districtListForMatch) {
            Optional<T> salePriceCalLineOpt = priceCalLineBOS.stream().filter(it -> {
                DistrictBO districtForMatch = districtSupplier.apply(it);
                return Objects.equals(districtForMatch.getId(), districtId);
            }).findFirst();

            if (salePriceCalLineOpt.isPresent()) {
                salePriceCalLine = salePriceCalLineOpt.get();
                break;
            }
        }
        return salePriceCalLine;
    }

    /**
     * 查找匹配的最优惠的价格
     */
    public SalePriceCalLineBO findMinSalePrice(DistrictBO districtBO, List<SalePriceCalLineBO> priceCalLineBOS, SelfSpcCalEndpointTO req, String priceType) {
        if (Objects.isNull(districtBO)) {
            return null;
        }

        // 大宗
        if (Objects.equals(priceType, SpcPriceSchemeTypeDict.ELECTRIC_WIRE_AND_CABLE)) {
            // 有品牌和sortIndex的优先匹配
            List<SalePriceCalLineBO> filteredLines = priceCalLineBOS.stream()
                    .filter(it -> Objects.equals(it.getBrandBO().getId(), req.getBrandBO().getId()))
                    .sorted(this::compareElectricWireAndCableLines)
                    .collect(Collectors.toList());

            return CollUtil.isEmpty(filteredLines) ? null : filteredLines.get(0);
        }

        // 区域相同，按照账期sortIndex排序，在优选
        TreeMap<Long, List<SalePriceCalLineBO>> groupBySortIndex = priceCalLineBOS.stream().filter(it -> Objects.equals(it.getDistrictBO().getId(), districtBO.getId()))
                .collect(Collectors.groupingBy(it -> Opt.ofNullable(it.getPayableSchemeTemplateBO()).map(PayableSchemeTemplateBO::getSortIndex).orElse(Long.MAX_VALUE),
                        TreeMap::new,
                        Collectors.toList()));

        // 去除最小的排序
        List<SalePriceCalLineBO> lineBOList = CollUtil.getFirst(groupBySortIndex.values());

        if (Objects.equals(priceType, SpcPriceSchemeTypeDict.BASIC)) {
            Optional<SalePriceCalLineBO> min = lineBOList.stream().min(Comparator.comparing(SalePriceCalLineBO::getFinalCoefficientN));
            return min.get();
        } else {
            Optional<SalePriceCalLineBO> min = lineBOList.stream().min(Comparator.comparing(SalePriceCalLineBO::getRealSalePriceWithTax));
            return min.get();
        }
    }

    public OneMouthfulLinePriceBO findMinSalePrice(OneMouthfulLinePriceBO line, List<OneMouthfulLinePriceBO> priceCalLineBOS) {
        if (Objects.isNull(line)) {
            return null;
        }

        Optional<OneMouthfulLinePriceBO> min = priceCalLineBOS.stream()
                .filter(it -> Objects.equals(it.getDistrictBO().getId(), line.getDistrictBO().getId()))
                .min(Comparator.comparing(OneMouthfulLinePriceBO::getAclSalePriceWithTax));

        return min.get();
    }

    private BigDecimal calWithoutTaxPrice(BigDecimal withTaxAmt, BigDecimal TaxRate) {
        BigDecimal withoutTaxAmt = withTaxAmt.divide(BigDecimal.ONE.add(TaxRate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP)), 4, RoundingMode.HALF_UP);
        return withoutTaxAmt;
    }

    /**
     * 比较电线电缆价格行的优先级
     * 优先级：同时有品牌和sortIndex > 只有品牌 > 只有sortIndex > 无特殊条件
     */
    private int compareElectricWireAndCableLines(SalePriceCalLineBO a, SalePriceCalLineBO b) {
        // 计算a和b的优先级分数（越高优先级越高）
        int aScore = calculatePriorityScore(a);
        int bScore = calculatePriorityScore(b);

        // 优先级分数不同，直接比较分数
        if (aScore != bScore) {
            return bScore - aScore; // 降序排列，高分优先
        }

        // 优先级分数相同，根据不同情况比较
        switch (aScore) {
            case 3: // 同时有品牌和sortIndex
                return a.getPayableSchemeTemplateBO().getSortIndex().compareTo(b.getPayableSchemeTemplateBO().getSortIndex());
            case 2: // 只有品牌
                return a.getSalesDiscFactor().compareTo(b.getSalesDiscFactor());
            case 1: // 只有sortIndex
                return a.getPayableSchemeTemplateBO().getSortIndex().compareTo(b.getPayableSchemeTemplateBO().getSortIndex());
            default: // 无特殊条件
                return a.getRealSalePriceWithTax().compareTo(b.getRealSalePriceWithTax());
        }
    }

    /**
     * 计算价格行的优先级分数
     * 3: 同时有品牌和sortIndex
     * 2: 只有品牌
     * 1: 只有sortIndex
     * 0: 无特殊条件
     */
    private int calculatePriorityScore(SalePriceCalLineBO line) {
        boolean hasBrand = Objects.nonNull(line.getBrandBO());
        boolean hasSort = Objects.nonNull(line.getPayableSchemeTemplateBO()) &&
                Objects.nonNull(line.getPayableSchemeTemplateBO().getSortIndex());

        if (hasBrand && hasSort) return 3;
        if (hasBrand) return 2;
        if (hasSort) return 1;
        return 0;
    }
}
