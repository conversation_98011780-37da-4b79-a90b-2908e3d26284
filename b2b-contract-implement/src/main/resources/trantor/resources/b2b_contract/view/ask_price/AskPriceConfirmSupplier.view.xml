<?xml version="1.0" encoding="UTF-8" ?>
<View title="确认报价" forModel="b2b_contract_AskSupplierPriceConfirmTO" type="Detail" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">

    <Form key="paymentScheme" model="b2b_contract_AskSupplierPriceConfirmTO" dataSource="#{onDataSource}" columnNum="1" >
        <Fields>
            <Field name="askPriceId" label="询价单id" show="#{false}"/>
            <Field name="entityBO" columnSize="middle" label="中标供应商">
                <RenderType>
                    <NonRelatedModelSelect model="md_EntityBO" fuzzySearchable="#{false}"
                                           dataCondition="id in (?)" dataParams="[env.supplierIds]"
                                           modalTitle="供应商"
                                           fieldNames="{value: 'id', label: 'entityName'}"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="供应商不能为空"/>
                </Validations>
            </Field>
            <Field name="payableSchemeTemplateBO" label="账期" >
                <RenderType>
                    <ModelSelect targetView="b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeTemplateSelectList" env="#{{radio: true}}" optionFormat="#{option => option.code}"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="账期不能为空"/>
                </Validations>
            </Field>

        </Fields>
        <Actions>
            <Action layout="Footer" type="Cancel" after="GoBack"/>
            <Action type="Submit" label="提交" layout="Footer" logicFunc="b2b_contract_ConfirmAskSupplierPriceFunc" after="GoBack"/>
        </Actions>
    </Form>


</View>
