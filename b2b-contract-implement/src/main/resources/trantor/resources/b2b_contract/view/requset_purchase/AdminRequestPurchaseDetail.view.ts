import { Controller, Toast, utils, showMessage } from 'nusi-sdk'

const { triggerLogicFlow, triggerLogicFunction } = utils

export default class extends Controller {

    columns = [
        {title: '材料名称', key: 'needLineName'},
        {title: '规格型号', key: 'thingSizeDesc'},
        {title: '数量', key: 'needNum'},
        {title: '计量单位', key: 'unit.unitName'},
        {title: '协议名称', key: 'agreement.name'},
        {title: '协议编号', key: 'agreement.code'},
        {title: '供应商', key: 'supplier.entityName'},
        {title: '关联标品编号', key: 'spu.spuCode'},
        {title: '标品名称', key: 'spu.name'},
        {title: '含铜量', key: 'purRawMaterialContent'},
        {title: '采购协议铜基价', key: 'purCopperBasicPrice'},
        {title: '采购辅材及其他价格', key: 'purOtherCosts'},
        {title: '采购综合含税单价', key: 'purTaxPrice'},
        {title: '采购延米铜价', key: 'purCopperPrice'},
        {title: '采购折扣系数', key: 'purDiscountFactor'},
        {title: '销售辅材及其他价格', key: 'saleOtherCosts'},
        {title: '销售延米铜价', key: 'saleCopperPrice'},
        {title: '销售折扣系数', key: 'saleDiscountFactor'},
        {title: '销售含税单价', key: 'saleTaxPrice'},
        {title: '销售利润率', key: 'profitRate'}
    ]

    // 一键匹配协议
    autoMatchingAgreement = async () => {
        try {
            utils.openGlobalLoading()

            console.log('开始一键匹配协议:', this.pageRecord)
            const result = await this.triggerLogicFunction('b2b_contract_AutoMatchingAgreementFunc', this.pageRecord);
            console.log('一键匹配协议结果:', result)

            // 无论成功失败都要刷新需求明细表格数据
            const container = this.getContainerByKey('RequestPurchaseLine');
            if (container && container.refresh) {
                container.refresh();
            }

            if (result && result.value) {
                const resultData = result.value;
                
                // 显示处理结果统计信息
                console.log(`处理结果: 成功${resultData.successCount}条，失败${resultData.failCount}条，总计${resultData.totalCount}条`);
                
                // 检查是否有销售测算失败的情况
                if (resultData.hasSalePriceFailed && resultData.failedAgreementNames && resultData.failedAgreementNames.length > 0) {
                    // 显示销售测算失败的警告信息
                    showMessage({
                        level: "Strong",
                        message: resultData.message || "部分协议匹配到的清单没有销售测算数据，请补充后重新匹配协议！",
                        type: "Warning"
                    });
                } else {
                    // 显示成功信息
                    showMessage({
                        level: "Weak",
                        message: resultData.message || "一键匹配协议完成",
                        type: "Success"
                    });

                    const RequestPurchaseContainer = this.getContainerByKey('RequestPurchase');
                    RequestPurchaseContainer.refresh();
                }
            } else {
                Toast.error("一键匹配协议失败");
            }
        } catch (e) {
            console.log('一键匹配协议失败:', e)
            Toast.error(e.message || "一键匹配协议失败");
            
            // 即使出现异常也要刷新表格
            const container = this.getContainerByKey('RequestPurchaseLine');
            if (container && container.refresh) {
                container.refresh();
            }
        } finally {
            utils.closeGlobalLoading()
        }
    }

    // 手动匹配协议
    manualMatchingAgreement = async () => {
        try {
            const container = this.getContainerByKey('multiTabs');
            const data = container?.instance?.getData?.()
            console.log('data', data);
            if (data.currentSelected.length === 0) {
                Toast.error("请先选择一条需求明细");
                return;
            }

            const selectedRequestPurchaseLine = data.currentSelected[0];
            console.log('选中的需求明细:', selectedRequestPurchaseLine);

            // 打开协议清单选择弹框
            this.openView('b2b_contract_AgreementDetailBO_SelectAgreementDetailForManualMatch', {
                openViewType: "Dialog",
                payloadCallback: async (payload) => {
                    console.log('手动匹配协议回调:', payload);
                    
                    if (!payload) {
                        Toast.error("未选择协议清单");
                        return;
                    }

                    // const selectedAgreementDetail = payload;
                    
                    try {
                        // 构造调用手动匹配协议的参数
                        const manualMatchParams = {
                            id: selectedRequestPurchaseLine.id,
                            agreementDetailBO: {
                                id: payload.id
                            }
                        };

                        // 调用手动匹配协议接口
                        const result = await this.triggerLogicFunction('b2b_contract_ManualMatchingAgreementFunc', manualMatchParams);
                        console.log('手动匹配协议结果:', result);

                        if (result && result.value) {
                            showMessage({
                                level: "Weak",
                                message: "手动匹配协议成功",
                                type: "Success"
                            });

                            // 刷新需求明细表格
                            // if (container && container.refresh) {
                            //     container.refresh();
                            // }
                            container.instance.refreshCurrPage()
                            const RequestPurchaseContainer = this.getContainerByKey('RequestPurchase');
                            RequestPurchaseContainer.refresh();
                        } else {
                            Toast.error("手动匹配协议失败");
                        }
                    } catch (e) {
                        console.log('手动匹配协议失败:', e);
                        Toast.error(e.message || "手动匹配协议失败");
                    }
                }
            });

        } catch (e) {
            console.log('手动匹配协议失败:', e);
            Toast.error(e.message || "手动匹配协议失败");
        }
    }

    // 手动匹配标品
    manualMatchingSpu = async () => {
        try {
            const container = this.getContainerByKey('multiTabs');
            const data = container?.instance?.getData?.()
            console.log('data', data);
            if (data.currentSelected.length === 0) {
                Toast.error("请先选择一条需求明细");
                return;
            }

            const selectedRequestPurchaseLine = data.currentSelected[0];
            console.log('选中的需求明细:', selectedRequestPurchaseLine);

            // 打开标品选择弹框
            this.openView('item_SpuBO_SelectSpuForManualMatch', {
                openViewType: 'Dialog',
                payloadCallback: async (payload) => {
                    console.log('手动匹配标品回调:', payload);
                    
                    if (!payload) {
                        Toast.error("未选择标品");
                        return;
                    }

                    // const selectedSpu = payload.record;
                    
                    try {
                        // 构造调用手动匹配标品的参数
                        const manualMatchParams = {
                            id: selectedRequestPurchaseLine.id,
                            spu: {
                                id: payload.id
                            }
                        };

                        // 调用手动匹配标品接口
                        const result = await this.triggerLogicFunction('b2b_contract_ManualMatchingSpuFunc', manualMatchParams);
                        console.log('手动匹配标品结果:', result);

                        if (result && result.value) {
                            showMessage({
                                level: "Weak",
                                message: "手动匹配标品成功",
                                type: "Success"
                            });

                            // 刷新需求明细表格
                            // if (container && container.refresh) {
                            //     container.refresh();
                            // }
                            container.instance.refreshCurrPage()
                        } else {
                            Toast.error("手动匹配标品失败");
                        }
                    } catch (e) {
                        console.log('手动匹配标品失败:', e);
                        Toast.error(e.message || "手动匹配标品失败");
                    }
                }
            });

        } catch (e) {
            console.log('手动匹配标品失败:', e);
            Toast.error(e.message || "手动匹配标品失败");
        }
    }

    // 导出销售价格清单
    exportProfit = async () => {
        try {
            utils.openGlobalLoading()

            console.log(this.pageRecord)
            const result = await this.triggerLogicFunction('b2b_contract_ExportRequestPurchaseProfitFunc', this.pageRecord);
            console.log('导出结果:', result)

            if (result && result.value) {
                window.open(result.value, '_blank');
                showMessage({
                    level: "Weak",
                    message: "导出成功",
                    type: "Success"
                });
            } else {
                Toast.error("导出失败，未获取到文件地址");
            }
        } catch (e) {
            console.log('导出失败:', e)
            Toast.error(e.message || "导出失败");
        } finally {
            utils.closeGlobalLoading()
        }
    }

    // 导出需求明细
    exportDetail = async () => {
        try {
            utils.openGlobalLoading()

            console.log('开始导出需求明细:', this.pageRecord)
            const result = await this.triggerLogicFunction('b2b_contract_ExportRequestPurchaseDetailFunc', this.pageRecord);
            console.log('导出明细结果:', result)

            if (result && result.value) {
                window.open(result.value, '_blank');
                showMessage({
                    level: "Weak",
                    message: "导出成功",
                    type: "Success"
                });
            } else {
                Toast.error("导出失败，未获取到文件地址");
            }
        } catch (e) {
            console.log('导出明细失败:', e)
            Toast.error(e.message || "导出明细失败");
        } finally {
            utils.closeGlobalLoading()
        }
    }

    // 获取导入销售价格的回调函数
    getUploadProfitPayloadCallback = (containerKey) => {
        return async (payload) => {
            console.log('导入销售价格回调:', payload)

            if (!payload || !payload.profitFile) {
                Toast.error("请选择要上传的文件");
                return;
            }

            try {
                // 设置需求单ID
                payload.requestPurchaseId = this.pageRecord.id;

                await this.triggerLogicFunction('b2b_contract_ImportRequestPurchaseProfitFunc', payload);

                showMessage({
                    level: "Weak",
                    message: "导入成功",
                    type: "Success"
                });

                // 刷新表格数据
                const container = this.getContainerByKey(containerKey);
                if (container && container.refresh) {
                    container.refresh();
                }

            } catch (e) {
                console.log('导入失败:', e);
                Toast.error(e.message || "导入失败");
            }
        }
    }
}