<?xml version="1.0" encoding="UTF-8"?>
<View forModel="b2b_contract_AskSupplierPriceBO" type="List" menuView="true" title="报价单" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Table model="b2b_contract_AskSupplierPriceBO"
           dataFunction="b2b_contract_PagingAskSupplierPriceFunc"
           key="table"
           fuzzySearchable="#{false}"
           sortableFields="createdAt"
           customDerivedData="false"
    >
        <!-- 搜索条件字段 -->
        <Search>
            <Fields>
                <Field name="askPriceBO.code" label="询价单编码"/>
                <Field name="askPriceBO.name" label="询价单名称"/>
                <Field name="askPriceBO.projectName" label="项目名称"/>
                <Field name="askPriceBO.categoryBO">
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="2" parentField="parentCategory"
                                            columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                            dataSource="md_CategoryBO_CategoryTreeProvider"
                                            model="md_CategoryBO" valueField="id" linkSelectMode="true"
                                            labelField="categoryName" modalWidth="#{800}"
                                            modalTitle="选择分类" searchInputWidth="#{400}"/>
                    </RenderType>
                </Field>
                <Field name="status"/>
            </Fields>
        </Search>

        <!-- 列表字段 -->
        <Fields>
            <Field name="askPriceBO.code" label="询价单编码"/>
            <Field name="askPriceBO.name" label="询价单名称"/>
            <Field name="askPriceBO.categoryName" label="分类"/>
            <Field name="askPriceBO.projectName" label="项目名称"/>
            <Field name="askPriceBO.enterpriseName" label="项目所属公司名称"/>

            <Field name="status" label="状态"/>
            <Field name="createdBy.nickname" label="创建人"/>
            <Field name="createdAt" label="创建时间"/>
        </Fields>

        <!-- 行操作 -->
        <RecordActions>
            <!--详情-->
            <Action label="detail"
                    targetView="b2b_contract_AskSupplierPriceBO_AskSupplierPriceDetail"
            />

            <!-- 去报价 -->
            <Action label="去报价"
                    show="#{['price_doing','price_reject'].includes(this.record.status)}"
                    targetView="b2b_contract_AskSupplierPriceBO_AskSupplierPriceConfirm"
            />
        </RecordActions>

    </Table>
</View>
