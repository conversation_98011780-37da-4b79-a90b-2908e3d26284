import { Controller, Toast, utils, showMessage } from 'nusi-sdk'

const { triggerLogicFlow, triggerLogicFunction } = utils

export default class extends Controller {

    columns = [
        {title: '材料名称', key: 'needLineName'},
        {title: '规格型号', key: 'thingSizeDesc'},
        {title: '数量', key: 'needNum'},
        {title: '计量单位', key: 'unit.unitName'},
        {title: '协议名称', key: 'agreement.name'},
        {title: '协议编号', key: 'agreement.code'},
        {title: '供应商', key: 'supplier.entityName'},
        {title: '关联标品编号', key: 'spu.spuCode'},
        {title: '标品名称', key: 'spu.name'},
        {title: '含铜量', key: 'purRawMaterialContent'},
        {title: '采购协议铜基价', key: 'purCopperBasicPrice'},
        {title: '采购辅材及其他价格', key: 'purOtherCosts'},
        {title: '采购综合含税单价', key: 'purTaxPrice'},
        {title: '采购延米铜价', key: 'purCopperPrice'},
        {title: '采购折扣系数', key: 'purDiscountFactor'},
        {
            title: '销售辅材及其他价格',
            key: 'saleOtherCosts',
            type: 'InputNumber',
            updateFuncKey: 'b2b_contract_UpdateRequestPurchaseLineFunc',
        },
        {title: '销售延米铜价', key: 'saleCopperPrice'},
        {
            title: '销售折扣系数',
            key: 'saleDiscountFactor',
            type: 'InputNumber',
            max: 1,
            min: 0,
            precision: 2,
            step: 0.01,
            updateFuncKey: 'b2b_contract_UpdateRequestPurchaseLineFunc',
        },
        {title: '销售含税单价', key: 'saleTaxPrice'},
        {title: '销售利润率', key: 'profitRate'}
    ]

    // 导出销售价格清单
    exportProfit = async () => {
        try {
            utils.openGlobalLoading()
            
            const result = await this.triggerLogicFunction('b2b_contract_ExportRequestPurchaseProfitFunc', this.pageRecord);
            console.log('导出结果:', result)
            
            if (result && result.value) {
                window.open(result.value, '_blank');
                showMessage({
                    level: "Weak",
                    message: "导出成功",
                    type: "Success"
                });
            } else {
                Toast.error("导出失败，未获取到文件地址");
            }
        } catch (e) {
            console.log('导出失败:', e)
            Toast.error(e.message || "导出失败");
        } finally {
            utils.closeGlobalLoading()
        }
    }

    // 获取导入销售价格的回调函数
    getUploadProfitPayloadCallback = (containerKey) => {
        return async (payload) => {
            console.log('导入销售价格回调:', payload)

            if (!payload || !payload.record || !payload.record.profitFile) {
                Toast.error("请选择要上传的文件");
                return;
            }

            try {
                const uploadTO = {
                    requestPurchaseId: this.pageRecord.id,
                    profitFile: payload.record.profitFile
                }
                console.log('uploadTO', uploadTO)
                
                await this.triggerLogicFunction('b2b_contract_ImportRequestPurchaseProfitFunc', uploadTO);
                
                showMessage({
                    level: "Weak",
                    message: "导入成功",
                    type: "Success"
                });
                
                // 刷新表格数据
                const container = this.getContainerByKey(containerKey);
                // if (container && container.refresh) {
                //     container.refresh();
                // }
                container.instance.refreshCurrPage()
            } catch (e) {
                console.log('导入失败:', e);
                Toast.error(e.message || "导入失败");
            }
        }
    }
}