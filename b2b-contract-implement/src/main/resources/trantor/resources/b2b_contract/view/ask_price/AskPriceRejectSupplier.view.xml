<?xml version="1.0" encoding="UTF-8" ?>
<View title="拒绝报价" forModel="b2b_contract_AskSupplierPriceReplyTO" type="Detail" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">

    <Form key="paymentScheme" model="b2b_contract_AskSupplierPriceReplyTO" dataSource="#{onDataSource}" columnNum="1" >
        <Fields>
            <Field name="askPriceId" label="询价单id" show="#{false}"/>
            <Field name="entityBOList" columnSize="middle">
                <RenderType>
                    <NonRelatedModelSelect model="md_EntityBO" fuzzySearchable="#{false}"
                                           dataCondition="id in (?)" dataParams="[env.supplierIds]"
                                           modalTitle="供应商"  type="ToMany"
                                           fieldNames="{value: 'id', label: 'entityName'}"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="供应商不能为空"/>
                </Validations>
            </Field>
            <Field name="rejectReason" label="拒绝原因">
                <RenderType>
                    <Textarea rows="#{2}" ></Textarea>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="原因不能为空"/>
                </Validations>
            </Field>
        </Fields>
        <Actions>
            <Action layout="Footer" type="Cancel" after="GoBack"/>
            <Action type="Submit" label="提交" layout="Footer" logicFunc="b2b_contract_RejectAskSupplierPriceFunc" after="GoBack"/>
        </Actions>
    </Form>


</View>
