<?xml version="1.0" encoding="UTF-8" ?>
<View title="设置中标供应商" forModel="b2b_contract_RequestPurchaseBO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Form key="SetWinSupplierForm" title="设置中标供应商" model="b2b_contract_RequestPurchaseBO" 
          dataCondition="id = ?" dataParams="[pageContext.record.id]">
        <Fields>
            <Field name="id" show="false"/>
<!--            <Field name="code" label="需求单编号" readonly="true"/>-->
<!--            <Field name="name" label="需求单名称" readonly="true"/>-->
            <Field name="winSupplierBO" label="中标供应商" required="true">
                <RenderType>
                    <ModelSelect
                        dataFunction="b2b_contract_QuerySupplierByRequestPurchaseFunc"
                        dataParams="{id: pageContext.record.id}"
                        valueField="id"
                        labelField="entityName"
                        showSearch="true"
                        placeholder="请选择中标供应商"
                    />
                </RenderType>
            </Field>
        </Fields>
        <Actions>
            <Action label="取消" action="Close" layout="Footer"/>
            <Action label="确定" logicFunc="b2b_contract_SetWinSupplierFunc" after="Close" layout="Footer"/>
        </Actions>
    </Form>
</View> 