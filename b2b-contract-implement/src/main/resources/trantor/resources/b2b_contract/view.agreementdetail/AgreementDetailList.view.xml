<?xml version="1.0" encoding="UTF-8"?>
<View forModel="b2b_contract_AgreementDetailBO" menuView="true" type="List" title="协议清单" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Table key="agreementDetailList" model="b2b_contract_AgreementDetailBO"
           dataFunction="b2b_contract_PagingWorkspaceAgreementDetailFunc"
           dataParams="{fromSite:{value:'admin'},agreementBO:{projectTypes:env.envApplyProjectType ? {value: env.envApplyProjectType} : null, type:env.envApplyType ? {value: env.envApplyType} : null}}"
           fuzzySearchable="#{false}" sortableFields="outerAgreementName,thingMaterial,thingSize" customDerivedData="false"
           showEditableIcon="#{true}" onUpdate = "#{changeSpuBO}">
    <Search>
            <Fields>
                <Field label="框架协议" name="agreementBO">
                    <RenderType>
                        <ModelSelect targetView="b2b_contract_AgreementBO_SelectAgreement"
                                     env="#{{selection:'multi',condition:{projectTypes:env.envApplyProjectType ? {value: env.envApplyProjectType} : null, type:env.envApplyType ? {value: env.envApplyType} : null}}}"/>
                    </RenderType>
                </Field>
                <Field name="saleEntityBO">
                    <RenderType>
                        <ModelSelect targetView="md_EntityBO_SelectEntity"
                                     modalWidth="#{700}" modalTitle="所属供应商" model="md_EntityBO"   env="#{{condition:`merchantType in ('SUPPLIER','SUPPLY_CHAIN') and entityStatus='ENABLED'`}}">
                        </ModelSelect>
                    </RenderType>
                </Field>
                <Field name="outerAgreementName"/>
                <Field label="标品编码" name="spuCode"/>
                <Field label="标品名称" name="spuName"/>
                <Field name="categoryBO" label="分类">
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="2" parentField="parentCategory"
                                            columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                            dataSource="md_CategoryBO_CategoryTreeProvider"
                                            model="md_CategoryBO" valueField="id" linkSelectMode="true"
                                            labelField="categoryName" modalWidth="#{800}"
                                            modalTitle="选择分类" searchInputWidth="#{400}"/>
                    </RenderType>
                </Field>
                <Field name="brandBo"/>
                <Field name="statusDict"/>
<!--                <Field name="inventoryStatus"/>-->
                <Field name="source"/>
                <Field label="创建人" name="createdBy"/>
            </Fields>
        </Search>
        <Fields>
            <Field name="agreementBO.id" show="#{false}"/>
            <Field name="agreementBO.type" show="#{false}"/>
            <Field name="agreementBO.bizType" show="#{false}"/>
            <Field label="框架协议" name="agreementBO.name">
                <RenderType>
                    <Action openViewType="Dialog"
                            env="#{{agreementId:this.record.agreementBO.id}}"
                            targetView="b2b_contract_AgreementBO_agreementInfo" />
                </RenderType>
            </Field>
            <Field label="供应商" name="saleEntityBO.entityName">
            </Field>
            <Field label="关联补充协议" name="supplementAgreementBO.name">
                <RenderType>
                    <Action openViewType="Dialog"
                            env="#{{agreementId:this.record.supplementAgreementBO.id}}"
                            targetView="b2b_contract_AgreementBO_agreementInfo" />
                </RenderType>
            </Field>
            <Field name="outerAgreementName"/>
            <Field name="statusDict"/>
<!--            <Field label="关联标品" name="spuBO" editable="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO}">-->
<!--                <RenderType>-->
<!--                    <ModelSelect targetView="item_SpuBO_SingleSelectAgreementSpu"-->
<!--                                 env="#{{agreementId:this.record.agreementBO.id}}"/>-->
<!--                    <MainField showPanel="#{false}" onlyText="#{true}" editable="#{false}"/>                </RenderType>-->
<!--            </Field>-->
            <Field label="关联标品" name="spuBO" columnSize="large" initValue="#{this.record?.spuBO ?? '-'}" editable="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO}">
                <RenderType>
                    <ModelSelect modalWidth="1000" modalTitle="选择关联标品" model="item_SpuBO" dataFunction="b2b_contract_PagingAgreementSpuFunc"
                                 dataParams="#{{id:{value:this.record.agreementBO.id}}}" selection="radio"
                                 fuzzySearchable="#{false}">
                        <Search>
                            <Fields>
                                <Field name="spuCode" label="标品编码"/>
                                <Field name="name" label="标品名称"/>
                                <Field name="category">
                                    <RenderType>
                                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="4" parentField="parentCategory"
                                                            columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                                            dataSource="md_CategoryBO_CategoryTreeProvider"
                                                            dataParams="#{{categoryStatus:'ENABLED'}}"
                                                            model="md_CategoryBO" valueField="id"
                                                            labelField="categoryName" modalWidth="#{800}"
                                                            modalTitle="选择分类" searchInputWidth="#{400}"/>
                                    </RenderType>
                                </Field>
                                <Field name="thingMaterial"/>
                                <Field name="thingSize"/>
                            </Fields>
                        </Search>
                        <Fields>
                            <Field name="id" show="#{false}"/>
                            <Field label="标品编码" name="spuCode"/>
                            <Field label="标品名称" name="name"/>
                            <Field label="关联物料" name="thing.thingName"/>
                            <Field label="分类" name="category.path"/>
                            <!--            <Field name="thingMaterial"/>-->
                            <!--            <Field name="thingSize"/>-->
                            <Field label="计量单位" name="thing.unit.unitName"/>
                            <Field label="属性" name="attributes" columnSize="large">
                                <RenderType>
                                    <Text format="#{formatAttributes}"/>
                                </RenderType>
                            </Field>
                        </Fields>
                    </ModelSelect>
                    <MainField showPanel="#{false}"  onlyText="#{true}" editable="#{false}"/>
                </RenderType>
            </Field>
            <Field label="分类" name="categoryBO.path">
                <RenderType>
                    <MainField detailAction="false" showMore="false" showEdit="false"/>
                </RenderType>
            </Field>
<!--            <Field name="thingMaterial"/>-->
<!--            <Field name="thingSize"/>-->
            <Field name="spuBO.attributes" label="属性">
                <RenderType>
                    <Text format="#{formatAttributes}"/>
                </RenderType>
            </Field>
            <Field label="税率(%)" name="taxRate.taxRateShow">
                <RenderType>
                    <MainField onlyText="#{true}"/>
                </RenderType>
            </Field>
            <Field name="supplyInfo.price">
                <RenderType>
                    <Number format="0.[0000]"/>
                </RenderType>
            </Field>
            <Field name="supplyInfo.taxPrice">
                <RenderType>
                    <Number format="0.[0000]"/>
                </RenderType>
            </Field>
            <Field label="计量单位" name="unit.unitName"/>
            <Field name="spuBO.rawMaterialContent" label="铜含量（kg/m）" />
            <Field name="copperBasicPrice" label="参考铜基价" />
            <Field name="yanmiCopperPrice" label="延米铜价" />
            <Field name="otherCosts" label="辅材及其他费用" />
            <Field name="purDiscountFactor" label="采购折扣系数" />
            <Field name="brands">
                <RenderType>
                    <MainField onlyText="#{true}"/>
                </RenderType>
            </Field>
            <Field name="priceSchemeBO.name"  label="价格方案"/>
            <Field name="supplyInfo.supplyPeriod" />
            <Field name="source" />
            <Field name="outerCode"/>
            <Field label="创建人" name="createdBy.username"/>
            <Field label="创建时间" name="createdAt"/>
            <Field name="syncInfo" show="#{false}"/>
            <Field name="priceSchemeBO.id"  show="false"/>
        </Fields>
        <RecordActions>
            <!--详情-->
            <Action label="detail" targetView="b2b_contract_AgreementDetailBO_AgreementDetail" />
            <Action label="edit" show="#{this.record.source==='AUTOMATIC' }"
                    targetView="b2b_contract_AgreementDetailBO_AgreementDetailEdit" />
            <Action label="维护清单信息" show="#{this.record.statusDict === 'UNUSUAL' }" openViewType="Dialog" openViewSize="l"
                    targetView="b2b_contract_AgreementDetailBO_AgreementDetailMaintain" />
            <Action label="映射标品" action="#{mapSpu}"
                    show="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO }"
                    openViewType="Dialog" />
            <Action label="设置价格方案" action="#{setPriceScheme}"
                    show="#{(this.record.statusDict === 'START_USING' || this.record.statusDict === 'STOP_USING') }"
                    openViewType="Dialog" />
            <Action label="查看价格方案" action="#{viewPriceScheme}"/>
<!--            <Action label="设置库存状态" openViewType="Dialog" openViewSize="s"-->
<!--                    show="#{this.record.statusDict === 'START_USING'}"-->
<!--                    targetView="b2b_contract_AgreementDetailBO_AgreementInventoryStatusUpdate"/>-->
            <Action label="失效"
                    show="#{(this.record.statusDict ==='START_USING' || this.record.statusDict ==='STOP_USING') }"
                    confirm="确认失效么？" logicFunc="b2b_contract_AgreementDetailLoseFunc" after="Refresh"  />
            <Action label="启用"
                    show="#{this.record.statusDict ==='STOP_USING' }"
                    confirm="确认启用么？" logicFunc="b2b_contract_AgreementDetailEnableFunc" after="Refresh" />
            <Action label="停用"
                    show="#{this.record.statusDict ==='START_USING' }"
                    confirm="确认停用么？" logicFunc="b2b_contract_AgreementDetailDisableFunc" after="Refresh" />
            <Action label="删除" show="#{this.record.source === 'AUTOMATIC' }"
                    confirm="确认删除么？此操作不可逆，请谨慎操作"
                    logicFunc="b2b_contract_AgreementDetailDeleteFunc" after="Refresh"/>
        </RecordActions>
        <Actions>
            <Action label="删除" multi="#{true}" layout="Header" confirm="确认删除么？此操作不可逆，请谨慎操作"
                    logicFunc="b2b_contract_AgreementDetailBatchDeleteFunc" after="Refresh"
                    validator="#{validateBatchDelete}" />
            <!--<Action label="映射标品" action="#{batchMapSpu}" multi="#{true}" layout="Header"
                    validator="#{validateBatchMapSpu}"/>-->
            <Action label="设置价格方案" action="#{batchSetPriceScheme}" multi="#{true}" layout="Header"
                    validator="#{validateBatchSetPrice}" />
<!--            <Action openViewType="Dialog" label="设置库存状态" multi="#{true}" layout="Header" openViewSize="s"-->
<!--                    targetView="b2b_contract_AgreementDetailBO_AgreementInventoryStatusBatchUpdate"-->
<!--                    validator="#{validateBatchSetInventoryStatus}" />-->
            <Action label="新建" type="Submit" targetView="b2b_contract_AgreementDetailBO_AgreementDetailCreate"
                    openViewType="Dialog" />
        </Actions>
    </Table>
</View>
