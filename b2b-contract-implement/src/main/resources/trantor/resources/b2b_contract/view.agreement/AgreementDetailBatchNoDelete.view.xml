<?xml version="1.0" encoding="UTF-8"?>
<View forModel="b2b_contract_AgreementDetailBO" type="Form" title="提醒" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <!--    "#{`账单确认后应${env.typeMsg}账款节点`}"-->
    <Detail model="b2b_contract_AgreementDetailBO">
        <Alert type="warning" message= "#{`选择的清单其中有${env.cloudListLen}项来自云筑/x5同步，不可删除，其余${env.automaticListLen}项允许删除，确认是否删除？`}" closable="false" show="#{env.automaticListLen !== 0}"/>
        <Alert type="warning" message= "#{`选择的清单中${env.cloudListLen}项全部来自云筑/x5同步，不可删除`}" closable="false" show="#{env.automaticListLen === 0}"/>


        <Actions>
            <Action label="取消删除"
                    layout="Footer"
                    action="GoBack"
                    show="#{env.automaticListLen !== 0}"/>
            <Action label="确认删除"
                    layout="Footer"
                    logicFunc="b2b_contract_AgreementDetailBatchDeleteFunc"
                    record="#{env.automaticList}"
                    after="GoBack"
                    show="#{env.automaticListLen !== 0}"/>
        </Actions>
    </Detail>


</View>