<?xml version="1.0" encoding="UTF-8"?>
<View forModel="b2b_contract_AgreementBO" type="List" menuView="true" title="协议列表" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Table model="b2b_contract_AgreementBO" dataFunction="b2b_contract_PagingWorkspaceAgreementFunc"
           dataParams="{fromSite:{value:'admin'}, projectTypes:env.envApplyProjectType ? {value: env.envApplyProjectType} : null, type:env.envApplyType ? {value: env.envApplyType} : null}" key="table"
           fuzzySearchable="#{false}" sortableFields="code,externalNo,name,bidTaskCode,effectiveAt,expireAt,createdAt" customDerivedData="false">
        <Search>
            <Fields>
                <Field name="code"/>
                <Field name="externalNo"/>
                <Field name="name"/>
                <Field name="type" show="#{env.envApplyType == null}"/>
                <Field name="category">
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="2" parentField="parentCategory"
                                            columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                            dataSource="md_CategoryBO_CategoryTreeProvider"
                                            model="md_CategoryBO" valueField="id" linkSelectMode="true"
                                            labelField="categoryName" modalWidth="#{800}"
                                            modalTitle="选择分类" searchInputWidth="#{400}"/>
                    </RenderType>
                </Field>
                <Field name="jfCompanyBO">
                    <RenderType>
                        <ModelSelect model="md_EntityBO" dataCondition="entityStatus='ENABLED' and merchantType in ('PURCHASER','SUPPLY_CHAIN') "/>
                    </RenderType>
                </Field>
                <Field name="departments"/>
                <Field name="yfCompanyBO">
                    <RenderType>
                        <ModelSelect targetView="md_EntityBO_SelectEntity"
                                     modalWidth="#{700}" modalTitle="所属供应商" model="md_EntityBO"   env="#{{condition:`merchantType in ('SUPPLIER','SUPPLY_CHAIN') and entityStatus='ENABLED'`}}">
                        </ModelSelect>
                    </RenderType>
                </Field>
<!--                <Field label="关联标品" name="spuBO.name">-->
<!--                    <RenderType>-->
<!--                        <Action targetView="item_SpuBO_SpuDetail" env="#{{spuId: this.record.spuBO.id}}"/>-->
<!--                    </RenderType>-->
<!--                </Field>-->
                <Field name="dealer"/>
                <Field name="effectiveAt"/>
                <Field name="expireAt"/>
                <Field name="projectTypes" show="#{env.envApplyProjectType == null}"/>
                <Field name="isSubCompany"/>
                <Field name="isSupply"/>
                <Field name="status"/>
                <Field name="approveStatusDict"/>
                <Field name="mainAgreement"/>
                <Field name="bidTaskCode"/>
                <Field name="sourceDict"/>
                <Field name="bizType"/>
                <Field name="maintainCompleted" label="是否维护完整"/>
                <Field name="optEmployeesStr"/>
                <!--<Field name="optDepartments">
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="6" parentField="department"
                                            columnTitles="#{['一级部门', '二级部门', '三级部门', '四级部门','五级部门','六级部门']}"
                                            dataSource="organization_DepartmentBO_DepartmentTreeProvider"
                                            dataParams="#{{departmentStatusDict:'ENABLED'}}"
                                            model="organization_DepartmentBO" valueField="id"
                                            labelField="departmentName" modalTitle="选择部门" modalWidth="1000"
                                            searchInputWidth="600"/>
                    </RenderType>
                </Field>-->
            </Fields>
        </Search>
        <Fields>
            <Field name="code" fixed="left">
                <RenderType>
                    <Action targetView="b2b_contract_AgreementBO_AgreementInfo" />
                </RenderType>
            </Field>
            <Field name="externalNo"/>
            <Field name="name"/>
            <Field name="status"/>
            <Field name="approveStatusDict"/>
            <Field name="maintainCompleted" label="是否维护完整"/>
            <Field name="category.path" label="分类"/>
            <Field name="jfCompanyBO.entityName" label="协议甲方"/>
            <Field name="departmentStr"/>
            <Field name="yfCompanyBO.entityName" label="协议乙方"/>
            <Field name="optDepartments">
                <RenderType>
                    <JsonViewer format="#{(value) => (value || []).map(i => i.departmentName).join(',')}" />
                </RenderType>
            </Field>
            <Field name="optEmployeesStr"/>
            <Field name="effectiveAt"/>
            <Field name="expireAt"/>
            <Field name="bidTaskCode"/>
            <Field name="type" />
            <Field name="bizType" />
            <Field name="projectTypes" />
            <Field name="isSubCompany"/>
            <Field name="isSupply" />
            <Field name="mainAgreement.code" label="关联主协议">
                <RenderType>
                    <Action env="#{{agreementId: this.record.mainAgreement.id}}"
                            targetView="b2b_contract_AgreementBO_AgreementInfo"/>
                </RenderType>
            </Field>
            <Field name="operator.employeeName" label="经办人"/>
            <Field name="sourceDict" />
            <Field name="syncStatus" />
            <Field name="createdAt"/>

            <Field name="contractQuantity"/>
            <Field name="supplementaryAgreedQuantity"/>
            <!--todo 注释展示字段-->
            <Field name="signatureTask.signStatus" label="签署状态"/>

            <Field name="agreementTemplateBO" show="#{false}"/>
            <Field name="useTemplateMaintain" show="#{false}"/>
            <Field name="summaryId" show="#{false}"/>
            <Field name="summaryName" show="#{false}"/>
            <Field name="attachment" show="#{false}"/>
        </Fields>
        <RecordActions>
            <!--详情-->
            <Action label="detail" targetView="b2b_contract_AgreementBO_AgreementInfo"  />

            <!--详情-->
            <!--详情 todo 功能改造 -->
            <Action label="维护协议清单" targetView="b2b_contract_AgreementBO_AgreementInfo" show = "false"/>

            <!--编辑 todo 功能改造 -->
            <!-- show="#{this.record.sourceDict === 'manual' &amp;&amp; !['enabled_audit','disabled_audit'].includes(this.record.status)}" -->
            <Action label="edit" show="false"
                    targetView="b2b_contract_AgreementBO_AgreementEdit"/>

            <!-- 维护协议和编辑 -->
            <Action label="维护协议" show="#{!['enabled_audit','disabled_audit'].includes(this.record.status) }"
                    action="#{extendInfoEditOrEdit}"  />


            <!--提交启用审批 todo 对接OA后，打开-->
            <Action label="提交启用审批单" confirm="是否确认提交启用审批" after="Refresh"
                    show="#{['draft','disabled'].includes(this.record.status) &amp;&amp; this.record.syncStatus !== 'fail' &amp;&amp; this.record.type === 'self' }"
                    logicFunc="b2b_contract_EnableAgreementSubmitBpmFunc"  />
            <!--查看审批 todo 对接OA后，打开-->
            <Action label="查看启用审批单"
                    show="#{(this.record.approveStatusDict != null) &amp;&amp; this.record.summaryId != null }"
                    action="#{jumpToOA}" />
            <!--启用（非自营）-->
            <Action label="enable" confirm="确定启用？" after="Refresh"
                    show="#{['draft','disabled'].includes(this.record.status) &amp;&amp; this.record.syncStatus !== 'fail' &amp;&amp; this.record.type !== 'self' }"
                    logicFunc="b2b_contract_EnableAgreementFunc"  />
            <!--停用（非自营）-->
            <Action label="disable" confirm="确认停用？" after="Refresh"
                    show="#{this.record.status ==='enabled' &amp;&amp; this.record.type !== 'self' }"
                    logicFunc="b2b_contract_DisableAgreementFunc" />
            <!--启用（自营，改用OA审批） todo 黄军威 对接OA后，注释掉-->
<!--            <Action label="enable" confirm="是否申请启用？" after="Refresh"-->
<!--                    show="#{['draft','disabled'].includes(this.record.status) &amp;&amp; this.record.syncStatus !== 'fail' &amp;&amp; this.record.type === 'self'}"-->
<!--                    logicFunc="b2b_contract_EnableAgreementApplyFunc"  />-->
            <!--停用（自营）-->
            <Action label="disable" confirm="是否申请停用？" after="Refresh"
                    show="#{this.record.status ==='enabled' &amp;&amp; this.record.type === 'self' }"
                    logicFunc="b2b_contract_DisableAgreementApplyFunc" />
            <!--审批通过（自营启用改用OA审批；自营停用，还用这个；非自营启用和停用，还用这个）-->
            <Action label="通过" confirm="是否确认审批通过？" after="Refresh"
                    show="#{(this.record.status ==='enabled_audit' &amp;&amp; this.record.type !== 'self' || this.record.status === 'disabled_audit')  }"
                    logicFunc="b2b_contract_AbleApproveAgreementFunc"  />
            <!--审批驳回-->
            <Action label="驳回" confirm="是否确认审批驳回？" after="Refresh"
                    show="#{(this.record.status ==='enabled_audit' &amp;&amp; this.record.type !== 'self' || this.record.status === 'disabled_audit')  }"
                    logicFunc="b2b_contract_AbleRejectAgreementFunc" />

            <!--审批通过（自营启用改用OA审批；自营停用，还用这个；非自营启用和停用，还用这个） todo 对接OA后，关闭 -->
<!--            <Action label="通过" confirm="是否确认审批通过？" after="Refresh"-->
<!--                    show="#{this.record.status ==='enabled_audit' || this.record.status === 'disabled_audit'}"-->
<!--                    logicFunc="b2b_contract_AbleApproveAgreementFunc"  />-->
            <!--审批驳回 todo 对接OA后，关闭 -->
<!--            <Action label="驳回" confirm="是否确认审批驳回？" after="Refresh"-->
<!--                    show="#{this.record.status ==='enabled_audit' || this.record.status === 'disabled_audit'}"-->
<!--                    logicFunc="b2b_contract_AbleRejectAgreementFunc" />-->

            <!--补充协议-->
            <Action label="新建补充协议"
                    show="#{['enabled','expired'].includes(this.record.status) &amp;&amp; !this.record.isSupply &amp;&amp; !this.record.isSubCompany &amp;&amp; this.record.syncStatus !== 'fail'  }"
                    targetView="b2b_contract_AgreementBO_AgreementSupplyCreate"  />
            <Action label="协议签约" action="#{contractJumpout}"/>
            <!--调整适用范围-->
            <Action label="调整适用范围" openViewType="Dialog"
                    targetView="b2b_contract_AgreementBO_AgreementCoverAreaEdit"
                    show="#{['enabled','disabled'].includes(this.record.status) }"  />
            <!-- todo 功能改造 -->
            <!--维护协议信息-->
            <!-- show="#{this.record.sourceDict !== 'manual' &amp;&amp; !['enabled_audit','disabled_audit'].includes(this.record.status)}" -->
            <Action label="维护协议信息"
                    targetView="b2b_contract_AgreementBO_AgreementExtendInfoEdit"
                    show="false"  />
            <!--删除-->
            <Action label="delete" confirm="确认删除？此操作不可逆，请谨慎操作" after="Refresh"
                    show="#{this.record.sourceDict==='manual' &amp;&amp; !['enabled_audit','disabled_audit'].includes(this.record.status)  }"
                    logicFunc="b2b_contract_DeleteAgreementFunc"  />
            <!--同步-->
            <Action label="同步" confirm="确认同步？" after="Refresh"
                    show="#{this.record.sourceDict !== 'manual' &amp;&amp; this.record.syncStatus === 'fail' }"
                    logicFlow="b2b_contract_SinglePullExternalAgreementFlow"  />

            <!-- 盖章 仅限自营的盖章-->
            <!-- todo 方便测试放开  show = "#{this.record.type ==='self' &amp;&amp; this.record.approveStatusDict === 'APPROVED' &amp;&amp; this.record.signatureTask == null}" -->
            <Action label="盖章" openViewType="Dialog"   action="#{checkSignAuthInfo}" show = "#{this.record.type ==='self' &amp;&amp; this.record.approveStatusDict === 'APPROVED' &amp;&amp; this.record.signatureTask == null }"/>
            <!-- 签署情况 -->
            <!-- todo #{[1, 2].includes(this.record.signatureTask.signStatus)} -->
            <Action label="签署情况" openViewType="Dialog" show="#{[1, 2].includes(this.record.signatureTask.signStatus) }" targetView="b2b_contract_SignatureTaskBO_ShowSignatureTask" env="#{{taskId:this.record.signatureTask.id}}"  />
        </RecordActions>
        <Actions>
            <Action label="引用模版维护" targetView="b2b_contract_ChooseMaintainBidTaskTO_ChooseMaintainBidTask"/>
            <!-- <Action label="维护协议信息" openViewType="Dialog" openViewSize="l" multi="#{true}"
                    targetView="b2b_contract_AgreementBO_AgreementExtendInfoBatchEdit"/> -->
            <Action label="批量维护协议信息" multi="#{true}"
                action="#{extendInfoBatchEdit}" />

            <GroupAction label="同步">
                <Action label="批量同步" confirm="确认同步？" logicFlow="b2b_contract_AddPullExternalAgreementFlow"
                    after="Refresh" />
                <Action label="精确同步" after="Refresh" targetView="b2b_contract_AgreementBO_SingleAgreementPull" openViewType="Dialog" />
            </GroupAction>
            <Action type="Create" label="新建"  confirmTitle="提示" confirmModalType="info" confirm="是否已和子公司及供应链商城运营人员确定无线上框架协议？" targetView="b2b_contract_AgreementBO_AgreementCreate" env="#{env}" />
            <Action type="Create" label="批量新建协议价格方案"
                    targetView="b2b_contract_AgreementBO_BatchCreatePriceScheme"
                    env="#{{source: 'OPE'}}" />
<!--            action="#{batchCreatePriceScheme}"-->

            <Action type="Create" multi="#{true}" label="协调人维护" 
                action="#{nextSteps}"/>
<!--                    openViewType="Dialog" openViewSize="s" targetView="b2b_contract_AgreementBO_BatchConfigOperator" />-->



        </Actions>
    </Table>
</View>