<?xml version="1.0" encoding="UTF-8" ?>
<View title="#{`编辑应${env.typeMsg}账款方案`}" forModel="b2b_contract_PayableSchemeTO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">

    <Form key="PayableAmtSchemes" model="b2b_contract_PayableSchemeTO" dataSource="#{() => ({ data: env.scheme })}">
        <Fields>
            <Field name="mark">
                <Validations>
                    <Validation required="#{true}" message="方案描述不能为空"/>
                </Validations>
                <RenderType>
                    <Textarea maxLength="#{1000}"/>
                </RenderType>
            </Field>
        </Fields>
        <Actions>
            <Action label="取消" action="Close" layout="Footer"/>
            <Action label="确认" type="Submit"
                    action="#{goBackWithRecord}" validator="#{validateScheme}" layout="Footer"/>
        </Actions>
    </Form>

    <TableForm title="#{`账单确认后应${env.typeMsg}账款节点`}" model="b2b_contract_PayableSchemeNodeTO"
               lookupFrom="PayableAmtSchemes.schemeNodes"
               minDataCount="#{1}" >
        <Fields>
            <Field name="payableRatio" label="#{`应${env.typeMsg}比例`}" >
                <RenderType>
                    <Number min="0" unit="%"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="#{`应${env.typeMsg}比例不能为空`}"/>
                </Validations>
            </Field>

            <Field name="payableInfo">

            </Field>
            <MultiField label="#{`应${env.typeMsg}账期`}" columnSize="large">
                <Field name="payablePeriod" columnSize="#{90}">
                    <RenderType>
                        <Number min="0" />
                    </RenderType>
                    <Validations>
                        <Validation required="#{true}" message="#{`应${env.typeMsg}账期不能为空`}"/>
                    </Validations>
                </Field>
                <Field name="periodType" initValue="#{data=>data?data:'DAY'}"  columnSize="#{40}" show="#{env.type !='virtual'}"/>
                <Field name="periodType" initValue="#{'MONTH'}" columnSize="#{40}" readonly="true" show="#{env.type =='virtual'}"/>
            </MultiField>

        </Fields>

    </TableForm>
</View>
