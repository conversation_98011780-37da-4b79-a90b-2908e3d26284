<?xml version="1.0" encoding="UTF-8"?>
<View title="批量维护协调人" forModel="b2b_contract_AgreementBO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Form key="agreement" model="b2b_contract_AgreementBO" >

        <Fields>

            <Field name="ids" show="#{false}" initValue="#{env.ids}"/>

            <Field name="optEmployees">
            </Field>

            <Field name="optDepartments">
                <Validations>
                    <Validation required="#{true}" message="使用单位不能为空"/>
                </Validations>
                <RenderType>
                    <CascadeModelSelect isLeafField="isLeaf" depthLimit="6" parentField="department"
                                        columnTitles="#{['一级部门', '二级部门', '三级部门', '四级部门','五级部门','六级部门']}"
                                        dataSource="organization_DepartmentBO_DepartmentTreeProvider"
                                        dataParams="#{{departmentStatusDict:'ENABLED'}}"
                                        model="organization_DepartmentBO" valueField="id"
                                        labelField="departmentName" modalTitle="选择部门" modalWidth="1000"
                                        searchInputWidth="600"/>
                </RenderType>
            </Field>
        </Fields>


        <Actions>
            <Action label="取消" layout="Footer" action="GoBack"/>
            <Action type="Submit" label="确认" confirm="确认提交？" layout="Footer"
                    logicFunc="b2b_contract_BatchConfigOperatorFunc" after="GoBack"/>
        </Actions>
    </Form>
</View>