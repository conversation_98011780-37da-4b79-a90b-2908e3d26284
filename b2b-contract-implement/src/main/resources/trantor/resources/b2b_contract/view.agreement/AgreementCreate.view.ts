import {Controller,state,utils} from 'nusi-sdk'

export default class extends Controller {

    @state
    agType = 'match_making';
    agTx = '收';

    // 协议变更
    agreementChange = async ({value, fieldName, record}) => {
        console.log('===== agreementChange ', value)
        let agreementContainer = this.getContainerByKey('agreement');
        if (fieldName == 'jfCompanyBO' || fieldName == 'yfCompanyBO') {
            // 含税价格
            const jfCompanyBO = this._.get(record, 'jfCompanyBO');
            // 税率
            const yfCompanyBO = this._.get(record, 'yfCompanyBO');
            if (!!jfCompanyBO && !!yfCompanyBO) {
                const result = await this.triggerLogicFunction('b2b_contract_CalcAgreementTypeFunc', [record]);
                agreementContainer.updateData(
                    {
                        type: result.type
                    }
                )

                this.agType = result.type

                let payableScheme = this.getContainerByKey('payableScheme');
                result.type==='virtual'? this.agTx = '收' : this.agTx = '付'

            }
            if (fieldName == 'jfCompanyBO') {
                const dsResult = await this.getDataSource('DataStore', {
                    modelKey: 'md_CompanyBO',
                    fields: ['id', 'orgId'],
                    condition: {
                        expression: 'id = ?',
                        params: [jfCompanyBO.id]
                    }
                });
                agreementContainer.updateData(
                    {
                        departmentId: null,
                        departments: null
                    }
                )
                setTimeout(() => agreementContainer.updateData({departmentId: dsResult.data[0].orgId}), 500)
            }
        } else if (fieldName == 'taxRateBO' || fieldName === 'taxAmt') {
            // 含税价格
            let taxAmt = this._.get(record, 'taxAmt');
            // 税率
            let taxRate = this._.get(record, 'taxRateBO.taxRate');
            if ((!!taxAmt || taxAmt == 0) && !!taxRate) {
                // 不含税价格
                let noTaxAmt = parseFloat((taxAmt / (1 + taxRate / 100)).toFixed(4));
                let taxPrc = parseFloat((taxAmt - noTaxAmt).toFixed(4));
                agreementContainer.updateData(
                    {
                        noTaxAmt: noTaxAmt,
                        taxPrc: taxPrc
                    }
                )
            }
        } else if (fieldName == 'projectTypes') {
            if (agreementContainer.data.type != 'virtual' && agreementContainer.data.projectTypes.includes("construction") && agreementContainer.data.bizType == 'project_material') {
                agreementContainer.updateData({
                    bizType: null
                });
            }
        }
    }

    coverAreaChange = ({fieldName, value, index, record}) => {
        console.log(fieldName, value, index)
        const table = this.getContainerByKey('coverAreas')
        if (fieldName === 'region') {
            table.updateData({...record, region: undefined}, index)
            setTimeout(() => table.updateData({...record, region: value}, index), 500)
        }
    }

    // 经销商变更
    dealerChange = ({fieldName, value, index, record}) => {
        console.log(fieldName, value, index)
        const table = this.getContainerByKey('dealers')
        if (fieldName === 'region') {
            table.updateData({...record, region: undefined}, index)
            setTimeout(() => table.updateData({...record, region: value}, index), 500)
        }
    }

    addPaymentScheme = () => {
        this.openView('b2b_contract_PaymentSchemeBO_PaymentSchemeCreate', {
            openViewType: 'Dialog',
            // openViewSize: "xs",
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')
                const schemes = schemeContainer.data

                console.log("=== addPaymentScheme, payload=", payload)
                let schemeTitle = payload.schemeNodes.map(node => translateNodeDict(node.nodeDict) + node.rate + '%').join(" | ");
                schemeContainer.updateData([...schemes,
                    {
                        name: schemeTitle,
                        title: schemeTitle,
                        description: payload.description,
                        schemeNodes: payload.schemeNodes,
                        apply: false
                    }
                ])

                this.applyFirstPayment()
            }
        })
    }

    // 选择付款方案
    selectPaymentSchemeTemplate = () => {
        const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')

        let paymentList = schemeContainer.data
            .filter(it=>it['paymentSchemeTemplateBO'] != null)
            .map((payment) => payment['paymentSchemeTemplateBO'])

        console.log('paymentList',paymentList)
        
        this.openView('b2b_contract_PaymentSchemeTemplateBO_PaymentSchemeSelectListV2', {
            openViewType: 'Dialog',
            // record:  paymentList,
            payloadCallback: (payload) => {
                console.log("=== addPaymentScheme, payload=", payload)

                //  拿出不存在paymentSchemeTemplateBO的数据
                let historyPaymentList = schemeContainer.data.filter(item => item['paymentSchemeTemplateBO']?.id == null);
                let newPaymentList = schemeContainer.data.filter(item => item['paymentSchemeTemplateBO']?.id != null);

                let newPaymentListMap =  newPaymentList.reduce((map, item) => {
                    let templateId = item['paymentSchemeTemplateBO']['id']
                    map[templateId] = item
                    return map
                },{})

                let res = [...schemeContainer.data]

                for (const it of payload.record) {
                    if (!newPaymentListMap[it.id]){
                        let copy  = {...it};
                        copy['paymentSchemeTemplateBO'] = it
                        copy['id'] = null
                        copy['apply'] = false
                        res.push(copy)
                    }
                }

                schemeContainer.updateData(res)

                this.applyFirstPayment()
            }
        })
    }

    // 删除，新增，选择模版后，设置第一个付款方案为应用
    applyFirstPayment = ()=>{
        const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')

        //  如果只有一个付款方案，自动设置成应用
        if (schemeContainer?.data?.length==1){
            schemeContainer.data[0].apply = true
            schemeContainer.updateData(schemeContainer.data)
        } else if (schemeContainer?.data?.length>1) {
            let res = schemeContainer.data.every(item => !item.apply)
            if (res){
                schemeContainer.data[0].apply = true
                schemeContainer.updateData(schemeContainer.data)
            }
        }
    }


    addPayableAmtScheme = () => {
        const agreementContainer = this.getContainerByKey('agreement')
        let typeMsg =  agreementContainer.data.type ==='virtual'? this.agTx = '收' : this.agTx = '付'
        let type =  agreementContainer.data.type
        this.openView('b2b_contract_PayableSchemeTO_PayableAmtSchemeCreate', {
            openViewType: 'Dialog',
            env: {typeMsg: typeMsg, type:type},
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('payableScheme')
                const data = schemeContainer.data

                console.log("=== addPaymentScheme, payload=", payload)
                let title = payload.schemeNodes.map(item => item.payableRatio + '%').join(" | ");
                let content = payload.schemeNodes.map(item => "对账确认后"+item.payablePeriod + translatePeriodDict(item.periodType)).join(" | ");
                schemeContainer.updateData([...data,
                    {
                        title: title,
                        content: content,
                        mark: payload.mark,
                        schemeNodes: payload.schemeNodes
                    }
                ])
            }
        })
    }

    selectPayableAmtScheme= () => {
        this.openView('b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeTemplateSelectList', {
            openViewType: 'Dialog',
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('payableScheme')
                const data = schemeContainer.data

                let existWithCodeList =  data.filter(it=>!!it['code'])
                let existWithoutCodeList =  data.filter(it=>!it['code'])

                let res = [...data]

                let existWithCodeMap =existWithCodeList.reduce((map, item) => {
                    map[item['code']] = item
                    return map;
                },{})

                for (const it of payload.record) {
                    if (!existWithCodeMap[it.code]){
                        let copy  = {...it};

                        copy['payableSchemeTemplateBO'] = it
                        copy['id'] = null
                        res.push(copy)
                    }
                }

                schemeContainer.updateData(res)
            }
        })
    }


    // 编辑应付账款方案
    editPayableScheme = ({ record }) => {
        const agreementContainer = this.getContainerByKey('agreement')
        let typeMsg =  agreementContainer.data.type ==='virtual'? this.agTx = '收' : this.agTx = '付'

        const rowId = record._TEMPID
        console.log("scheme,,,,,,", record)
        this.openView('b2b_contract_PayableSchemeTO_PayableAmtSchemeEdit', {
            openViewType: 'Dialog',
            env: {
                scheme: record,
                typeMsg: typeMsg,
                type: agreementContainer.data.type
            },
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('payableScheme')
                const data = schemeContainer.data
                console.log("=== editPayableScheme, payload=", payload)
                console.log("=== editPayableScheme, data=", data)
                let title = payload.schemeNodes.map(item => item.payableRatio + '%').join(" | ");
                let content = payload.schemeNodes.map(item => "对账确认后" + item.payablePeriod + translatePeriodDict(item.periodType)).join(" | ");
                data.forEach((scheme) => {
                    if (scheme._TEMPID === rowId) {
                        scheme.title = title
                        scheme.content = content
                        scheme.mark = payload.mark
                        scheme.schemeNodes = payload.schemeNodes
                    }
                })
                schemeContainer.updateData([...data])
            }
        })
    }

    // 编辑付款方案
    editPaymentScheme = ({record}) => {
        const rowId = record._TEMPID
        this.openView('b2b_contract_PaymentSchemeBO_PaymentSchemeEdit', {
            openViewType: 'Dialog',
            // openViewSize: "xs",
            env: {
                scheme: record
            },
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')
                const schemes = schemeContainer.data

                console.log("=== editPaymentScheme, payload=", payload)
                let schemeTitle = payload.schemeNodes.map(node => translateNodeDict(node.nodeDict) + node.rate + '%').join(" | ");
                schemes.forEach((scheme) => {
                    if (scheme._TEMPID === rowId) {
                        scheme.name = schemeTitle
                        scheme.title = schemeTitle
                        scheme.description = payload.description
                        scheme.schemeNodes = payload.schemeNodes
                    }
                })
                schemeContainer.updateData([...schemes])
            }
        })
    }

    // 删除付款方案
    deletePaymentScheme = ({record}) => {
        const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')
        const schemes = schemeContainer.data

        const rowId = record._TEMPID;
        schemeContainer.updateData([...schemes.filter(scheme => rowId != scheme._TEMPID)])

        this.applyFirstPayment()
    }


    // 删除应付账款方案
    deletePayableScheme = ({record}) => {
        const schemeContainer = this.getContainerByKey('payableScheme')
        const data = schemeContainer.data

        const rowId = record._TEMPID;
        schemeContainer.updateData([...data.filter(scheme => rowId != scheme._TEMPID)])
    }

    // 属性值
    showDepartments(value) {
        return value
            .map((department) => {
                return department.departmentName
            }).join('，')
    }

    convertJfCompany() {
        let conditions = ["entityStatus = 'ENABLED'", "merchantType != 'SUPPLIER'"];
        if (!!this.env?.envApplyType) {
            if (this.env?.envApplyType === 'self') {
                conditions.push("merchantType = 'SUPPLY_CHAIN'");
            } else {
                conditions.push("merchantType != 'SUPPLY_CHAIN'");
            }
        }
        return {
            condition: conditions.join(" and "), purchase: false
        }
    }

    convertYfCompany() {
        let conditions = ["entityStatus = 'ENABLED'"];
        let mainData = this.getContainerByKey("agreement").data;
        if (mainData?.projectTypes?.length == 1 && mainData.projectTypes.includes('estate')) {
            conditions.push("company.source = 'AIDE'");
        }
        if (!!this.env?.envApplyType) {
            if (this.env?.envApplyType === 'virtual') {
                conditions.push("merchantType = 'SUPPLY_CHAIN'");
            } else {
                conditions.push("merchantType != 'SUPPLY_CHAIN'");
            }
        }
        return {
            condition: conditions.join(" and ")
        }
    }


    applyPaymentScheme = ({ index, record })=>{
        utils.confirm({
            content: '是否应用该付款方案下对应的协议价格？',
        }).then((result: boolean) => {
            if (result) {
                let agreementPaymentSchemesContainer = this.getContainerByKey('agreementPaymentSchemes')
                let agreementPaymentSchemes = agreementPaymentSchemesContainer.data

                // 先全置为false
                agreementPaymentSchemes.forEach(obj => {
                    obj.apply = false;
                });
                agreementPaymentSchemes[index].apply = true;
                agreementPaymentSchemesContainer.updateData(agreementPaymentSchemes)
            }
        })
    }

    checkBizType = (rule, types, callback, record) => {
        if (this.getContainerByKey('agreement').data.type == 'match_making' && record.projectTypes && record.projectTypes.includes("construction") && types === "project_material") {
            callback("撮合协议的施工项目无工程物资业务");
        }
    }

    filterBizType = (values, record) => {
        if (this.getContainerByKey('agreement').data.type == 'match_making' && record.projectTypes && record.projectTypes.includes("construction")) {
            return values.filter(item => item === 'project_material');
        }
        return [];
    }
}

// 手动翻译，无法获取trantor资源
function translateNodeDict(nodeDict) {
    switch (nodeDict) {
        case 'advance':
            return '预付款'
        case 'progress':
            return '进度款'
        case 'settlement':
            return '结算款'
        case 'complete':
            return '竣工款'
        case 'guarantee':
            return '保修金'
        case 'special':
            return '特殊付款方式'
        default:
            return "";
    }
}

function translatePeriodDict(nodeDict) {
    switch (nodeDict) {
        case 'DAY':
            return '天'
        case 'MONTH':
            return '月'

        default:
            return "";
    }
}
