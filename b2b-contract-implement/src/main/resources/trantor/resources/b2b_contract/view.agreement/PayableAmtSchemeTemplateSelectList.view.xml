<?xml version="1.0" encoding="UTF-8" ?>
<View title="账款方案模版" forModel="b2b_contract_PayableSchemeTemplateBO" type="List" menuView="true"  version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">

    <Table key="PayableAmtSchemes" model="b2b_contract_PayableSchemeTemplateBO" dataCondition="`status` = 'ENABLE'"
           selection="#{env?.radio?'radio':'checkbox'}" presetSelectedData="#{pageRecord}" rowSelectDisabled="#{record=>record.status!=='ENABLE'}" >
        <Search>
            <Fields>
                <Field name="code"/>
                <Field name="mark"/>
                <Field name="status"/>
            </Fields>
        </Search>
        <Fields>
            <Field name="id" show="false"/>
            <Field name="code" label="编码"/>
            <Field name="title" label="应收节点"/>
            <Field name="content" label="应收账期"/>
            <Field name="mark" label="描述"/>
            <Field name="status" label="状态"/>
            <Field name="schemeNodes" show="#{false}"/>
        </Fields>
        <RecordActions>
            <Action label="详情"  targetView="b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeTemplateDetail"  openViewType="Dialog"/>
            <Action label="编辑" show="#{['DISABLE','DRAFT'].includes(this.record.status)}"  env="#{{'operate':'edit'}}" after="Refresh" targetView="b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeTemplateCreateOrEdit"  openViewType="Dialog"/>
            <Action label="复制" after="Refresh" logicFunc="b2b_contract_PayableSchemeTemplateCopyFunc" confirm="确认复制？"/>
            <Action label="启用" show="#{['DISABLE','DRAFT'].includes(this.record.status)}" record="#{formatter.merge({status:'ENABLE'})}" after="Refresh" logicFunc="b2b_contract_PayableSchemeTLChangeStatusFunc" confirm="确认启用？"/>
            <Action label="停用" show="#{['ENABLE'].includes(this.record.status)}" after="Refresh" record="#{formatter.merge({status:'DISABLE'})}" logicFunc="b2b_contract_PayableSchemeTLChangeStatusFunc" confirm="确认停用？"/>
            <Action label="删除" after="Refresh" logicFunc="b2b_contract_PayableSchemeTLDeleteFunc" confirm="确认删除？"/>
        </RecordActions>
        <Actions>
            <Action label="新建" after="Refresh" layout="Header" env="#{{'operate':'create'}}" targetView="b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeTemplateCreateOrEdit"  openViewType="Dialog"/>
            <Action label="取消" action="Close" layout="Footer"/>
            <Action label="确定" type="Submit" multi="#{true}" action="GoBackWithContext" layout="Footer"/>
        </Actions>
    </Table>
</View>
