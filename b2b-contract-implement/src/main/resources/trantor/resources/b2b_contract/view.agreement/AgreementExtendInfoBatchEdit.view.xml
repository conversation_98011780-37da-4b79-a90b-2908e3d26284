<?xml version="1.0" encoding="UTF-8"?>
<View title="批量维护协议信息" forModel="b2b_contract_AgreementBO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Form key="agreement" model="b2b_contract_AgreementBO" onFieldChange="#{agreementChange}">
        <Fields>
            <Field name="jfCompanyBO">
                <RenderType>
                    <ModelSelect targetView="md_EntityBO_SelectEntity"
                                 modalWidth="#{700}"
                                 env="#{{condition:'entityStatus=\'ENABLED\' and merchantType !=\'SUPPLIER\''}}"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="协议甲方不能为空"/>
                </Validations>
            </Field>
            <Field name="projectTypes">
                <Validations>
                    <Validation required="#{true}" message="适用项目类型不能为空"/>
                    <Validation validator="#{(rule, types, callback) => {if(env.envApplyType != 'virtual' &amp;&amp; types.length &gt; 1)callback('适用项目类型只能选一个')}}"/>
                </Validations>
            </Field>
            <Field name="departmentId" show="#{false}" submit="#{false}"/>
            <Field name="departments" readonly="#{!this.data.jfCompanyBO}" tips="请先选择协议甲方">
                <Validations>
                    <Validation required="#{true}" message="使用单位不能为空"/>
                </Validations>
                <RenderType>
                    <CascadeModelSelect isLeafField="isLeaf" depthLimit="6" parentField="department"
                                        columnTitles="#{['一级部门', '二级部门', '三级部门', '四级部门','五级部门','六级部门']}"
                                        dataSource="organization_DepartmentBO_DepartmentTreeProvider"
                                        dataParams="#{{departmentStatusDict:'ENABLED',department:{id:this.data.departmentId},withCurrent:true}}"
                                        model="organization_DepartmentBO" valueField="id" linkSelectMode="#{true}"
                                        labelField="departmentName" modalTitle="选择部门" modalWidth="1000"
                                        searchInputWidth="600"/>
                </RenderType>
            </Field>
            <Field name="bizType">
                <RenderType>
                    <Select disabledValues="#{values => filterBizType(values, this.record)}"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="业务类型不能为空"/>
                    <Validation validator="#{(rule, types, callback) => {checkBizType(rule, types, callback, this.record)}}"/>
                </Validations>
            </Field>
            <Field name="rentPeriod" show="#{this.data.bizType == 'rent'}"/>
            <Field name="coverAreaType">
                <Validations>
                    <Validation required="#{false}" message="覆盖范围不能为空"/>
                </Validations>
                <RenderType>
                    <Radio direction="horizontal"/>
                </RenderType>
            </Field>
            <Field name="category">
                <Validations>
                    <Validation required="#{true}" message="分类不能为空"/>
                </Validations>
                <RenderType>
                    <CascadeModelSelect isLeafField="isLeaf" depthLimit="2" leafOnly="#{true}" parentField="parentCategory"
                                        columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                        dataSource="md_CategoryBO_CategoryTreeProvider"
                                        dataParams="#{{categoryStatus:'ENABLED'}}"
                                        model="md_CategoryBO" valueField="id"
                                        labelField="categoryName" modalWidth="#{800}"
                                        modalTitle="选择分类" searchInputWidth="#{400}"/>
                </RenderType>
            </Field>
            <Field name="signAt">
                <Validations>
                    <Validation required="#{true}" message="签约日期不能为空"/>
                </Validations>
            </Field>
            <Field name="effectiveAt">
                <Validations>
                    <Validation required="#{true}" message="生效日期不能为空"/>
                </Validations>
            </Field>
            <Field name="expireAt">
                <Validations>
                    <Validation required="#{true}" message="终止日期不能为空"/>
                </Validations>
                <RenderType>
                    <DatePicker disabledDate="#{(current) => current.isBefore(new Date(),'day')}"/>
                </RenderType>
            </Field>
            <Field name="selectData" initValue="#{pageContext.record}" show="#{false}"/>
            <GroupField singleColumn="true">
                <Field name="remarks" colSize='all' >
                    <RenderType>
                        <RichText rows="#{12}"></RichText>
                    </RenderType>
                </Field>
            </GroupField>
        </Fields>
        <Actions>
            <Action label="取消" layout="Footer" action="GoBack"/>
            <Action label="确认" type="Submit" layout="Footer"
                    logicFunc="b2b_contract_BatchEditAgreementExtendInfoFunc" after="GoBack"/>
        </Actions>
    </Form>

    <Anchors>
        <Anchor title="付款方案">
            <TableForm title="付款方案" model="b2b_contract_PaymentSchemeBO" key="agreementPaymentSchemes"
                       lookupFrom="agreement.paymentSchemes" showAdd="#{false}" showDelete="#{false}"
                       minDataCount="#{1}">
                <Fields>
                    <Field name="id"  show="#{false}"/>
                    <Field name="code" readonly="#{true}"/>
                    <Field name="title" readonly="#{true}"/>
                    <Field name="description" readonly="#{true}"/>
                    <Field name="schemeNodes" show="#{false}"/>
                    <Field name="paymentSchemeTemplateBO.id" show="#{false}"/>
                </Fields>
                <RecordActions>
                    <Action label="详情"  targetView="b2b_contract_PaymentSchemeBO_PaymentSchemeDetail"  openViewType="Dialog"/>
                    <Action label="编辑" action="#{editPaymentScheme}" show="#{!this.record.paymentSchemeTemplateBO?.id}"/>
                    <Action label="删除" action="#{deletePaymentScheme}"/>
                </RecordActions>
                <Actions>
                    <Action label="新建方案" action="#{addPaymentScheme}"/>
                    <Action label="选择方案模版" action="#{selectPaymentSchemeTemplate}"/>
                </Actions>
            </TableForm>
        </Anchor>

        <Anchor title="#{`应${getContainerByKey('agreement').data.type=='virtual'?'收':'付'}账款方案`}"
                show="#{['virtual','self'].includes(getContainerByKey('agreement').data.type)}">
            <TableForm title="#{`应${getContainerByKey('agreement').data.type=='virtual'?'收':'付'}账款方案`}"
                       model="b2b_contract_PayableSchemeTO" key="payableScheme"
                       lookupFrom="agreement.payableSchemeList" showAdd="#{false}" showDelete="#{false}">
                <Alert message="应收账款方案为管理自营业务中采购商向供应链公司支付货款的账期及金额，所有应收款均在对账单双方确认后开始计算。" show="#{getContainerByKey('agreement').data.type==='virtual'}"/>
                <Alert message="应付账款方案为管理自营业务中供应链公司向供应商支付货款的账期及金额，所有应付款均在对账单双方确认后开始计算。" show="#{getContainerByKey('agreement').data.type==='self'}"/>
                <Fields>
                    <Field name="code" label="编码" readonly="#{true}"/>
                    <Field name="title" label="节点" readonly="#{true}"/>
                    <Field name="content" label="账期" readonly="#{true}"/>
                    <Field name="schemeNodes" show="#{false}"/>
                    <Field name="mark" readonly="true"/>
                    <Field name="payableSchemeTemplateBO" show="#{false}"/>
                </Fields>
                <RecordActions>
                    <Action label="编辑" action="#{editPayableScheme}" show="#{!this.record.payableSchemeTemplateBO?.id}"/>
                    <Action label="详情"  openViewType="Dialog" targetView="b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeDetail" />
                    <Action label="删除" action="#{deletePayableScheme}"/>
                </RecordActions>
                <Actions>
                    <Action label="新建方案" action="#{addPayableAmtScheme}"/>
                    <Action label="选择方案模版" action="#{selectPayableAmtScheme}" show="#{getContainerByKey('agreement').data.type==='virtual'}"/>
                </Actions>
            </TableForm>
        </Anchor>

        <Anchor title="覆盖范围" show="#{getContainerByKey('agreement').data.coverAreaType === 'appoint'}">
            <TableForm model="b2b_contract_AgreementCoverAreaBO" title="覆盖范围"
                       onFieldChange="#{coverAreaChange}" key="coverAreas"
                       lookupFrom="agreement.coverAreas">
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="region" resetFields="districts">
                        <RenderType>
<!--                            <ModelSelect dataCondition="`districtStatus` = 'ENABLED' AND `type` = 'big_area'"/>-->
                            <ModelSelect targetView="md_DistrictBO_SelectDistrict"
                                         modalWidth="#{700}"
                                         env="#{{condition:'districtStatus=\'ENABLED\' and type =\'big_area\''}}"/>
                        </RenderType>
                        <Validations>
                            <Validation required="#{true}" message="大区不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="districts" readonly="#{!this.record.region}" tips="请先选择大区">
                        <RenderType>
                            <CascadeModelSelect isLeafField="isLeaf"
                                                depthLimit="4"
                                                columnTitles="#{['省','市','区']}"
                                                dataSource="md_DistrictBO_DistrictTreeProvider"
                                                dataParams="#{{districtStatus:'ENABLED',parentDistrict:{id:this.record.region.id}}}"
                                                parentField="parentDistrict"
                                                model="md_DistrictBO" valueField="id" labelField="districtName"
                                                linkSelectMode="#{true}" modalTitle="选择地址"/>
                        </RenderType>
                        <Validations>
                            <Validation required="#{true}" message="省市区不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="limitType">
                        <Validations>
                            <Validation required="#{false}" message="框架限制不能为空"/>
                        </Validations>
                    </Field>
                </Fields>
            </TableForm>
        </Anchor>
    </Anchors>
</View>