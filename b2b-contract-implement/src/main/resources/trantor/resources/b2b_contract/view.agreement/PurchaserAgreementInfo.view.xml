<?xml version="1.0" encoding="UTF-8" ?>
<View title="协议详情" forModel="b2b_contract_AgreementBO" type="Detail" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Detail model="b2b_contract_AgreementBO" key="agreement" onDataLoaded="#{onBaseFormLoaded}" show="#{initialFetched}"
            dataFunction="b2b_contract_QueryAgreementInfoFunc" dataParams="{id:env.agreementId ? env.agreementId : pageContext.record.id}">
        <Alert type="error" message="#{getContainerByKey('agreement').data.syncRemark}"
               show="#{getContainerByKey('agreement').data.syncStatus === 'fail'}"/>
        <Fields>
            <GroupField title="基础信息">
                <Field name="id" show="#{false}"/>
                <Field name="code"/>
                <Field name="externalNo"/>
                <Field name="name"/>
                <Field name="aliasName" show="#{this.data.type === 'self'}"/>
                <Field name="projectTypes"/>
                <Field name="jfCompanyBO.entityName" label="协议甲方"/>
                <Field name="type"/>
                <Field name="departmentStr"/>
                <Field name="status"/>
                <Field name="category.path" label="分类"/>
                <Field name="yfCompanyBO.entityName" label="协议乙方"/>
                <Field name="bizType"/>
                <Field name="optEmployeesStr"/>
                <Field name="optDepartments"  initValue="#{this.data.optDepartments.map(d => d.departmentName).join(',')}">
                    <RenderType>
                        <Text></Text>
                    </RenderType>
                </Field>
                <Field name="maintainCompleted" helpText="#{tips}"/>
                <Field name="rentPeriod"/>
                <Field name="signAt"/>
                <Field name="effectiveAt"/>
                <Field name="expireAt"/>
                <Field name="coverAreaType"/>
                <Field name="isSupply"/>
                <Field name="mainAgreement.id" show="#{false}"/>
                <Field name="mainAgreement.name" label="关联主协议" show="#{this.data.isSupply}">
                    <RenderType>
                        <Action env="#{{agreementId: this.record.mainAgreement.id}}"
                                openViewType="Dialog"
                                targetView="b2b_contract_AgreementBO_PurchaserAgreementInfo"/>
                    </RenderType>
                </Field>
                <Field name="isSubCompany"/>
                <Field name="relateAgreement.id" show="#{false}"/>
                <Field name="relateAgreement.name" label="关联协议" show="#{this.data.isSubCompany}">
                    <RenderType>
                        <Action env="#{{agreementId: this.record.relateAgreement.id}}"
                                openViewType="Dialog"
                                targetView="b2b_contract_AgreementBO_PurchaserAgreementInfo"/>
                    </RenderType>
                </Field>
                <Field name="operator"/>
                <Field name="department.departmentName" label="经办单位"/>
                <Field name="sourceDict"/>
                <Field name="createdAt"/>
                <Field name="isCenterPay"/>
                <Field name="canRelateDetail"/>
                <Field name="remark"/>
                <Field name="attachment"/>
                <Field name="syncStatus" show="#{false}"/>
                <Field name="syncRemark" show="#{false}"/>
            </GroupField>
            <GroupField title="金额信息" show="#{this.data.type === 'self'}">
                <Field name="taxAmt"/>
                <Field name="taxRateBO.taxRateShow" label="税率"/>
                <Field name="noTaxAmt"/>
                <Field name="taxPrc"/>
            </GroupField>
            <GroupField title="招标信息" show="#{this.data.sourceDict === 'sync'}">
                <Field name="bidTaskCode"/>
                <Field name="bidTaskName"/>
                <Field name="jcBidInfo.categoryName"/>
            </GroupField>
            <GroupField singleColumn="true">
                <Field name="remarks" colSize='all' >
                    <RenderType>
                        <RichText rows="#{12}"></RichText>
                    </RenderType>
                </Field>
            </GroupField>
        </Fields>
        <Actions>
            <Action label="同步" confirm="确认同步？" after="Refresh"
                    show="#{this.data.sourceDict !== 'manual' &amp;&amp; this.data.syncStatus === 'fail' &amp;&amp; getContainerByKey('agreement').data.editPermission "
                    logicFlow="b2b_contract_SinglePullExternalAgreementFlow"/>
            <Action type="Submit" label="维护协议信息"
                    targetView="b2b_contract_AgreementBO_AgreementExtendInfoEdit"
                    env="#{{agreementId:this.data.id}}"
                    show="#{this.data.sourceDict !== 'manual' &amp;&amp; getContainerByKey('agreement').data.editPermission "/>
            <Action label="批量新建清单价格方案"
                    env="#{{agreementId:this.data.id, source: 'PCS'}}"
                    show="#{getContainerByKey('agreement').data.editPermission "
                    targetView="b2b_contract_AgreementBO_BatchCreatePriceSchemeForAgreement"/>
            <Action type="Submit" label="调整适用范围"
                    targetView="b2b_contract_AgreementBO_AgreementCoverAreaEdit"
                    env="#{{agreementId:this.data.id}}"
                    show="#{['enabled','disabled'].includes(this.data.status) &amp;&amp; getContainerByKey('agreement').data.editPermission "/>
        </Actions>
    </Detail>

    <Anchors>
        <Anchor title="联系信息">
            <Table model="b2b_contract_AgreementContactBO" title="联系信息"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="agreementBO = ?"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="person"/>
                    <Field name="certificateType"/>
                    <Field name="certificateNo"/>
                    <Field name="position"/>
                    <Field name="phone"/>
                    <Field name="districtStr"/>
                </Fields>
            </Table>
        </Anchor>
        <Anchor title="付款方案">
            <Table model="b2b_contract_PaymentSchemeBO" title="付款方案"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="agreementBO = ?"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="title"/>
                    <Field name="description"/>
                    <Field name="apply" label="是否被应用" readonly="#{true}" show="#{getContainerByKey('agreement').data?.type==='match_making'}" tips="应用该付款方案下对应的协议价格,将会被商城计算价格优先使用"/>
                </Fields>
            </Table>
        </Anchor>
        <Anchor title="应收账款方案">
            <Table model="b2b_contract_PayableSchemeTO" title="应收账款方案"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   lookupFrom="agreement.payableSchemeList">
                <Fields>
                    <Field name="title"/>
                    <Field name="content"/>
                    <Field name="mark"/>
                </Fields>
            </Table>
        </Anchor>
        <Anchor title="覆盖范围" show="#{getContainerByKey('agreement').data.coverAreaType === 'appoint'}">
            <Table model="b2b_contract_AgreementCoverAreaBO" title="覆盖范围"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="agreementBO = ?"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="region.districtName" label="大区"/>
                    <Field name="districtStr"/>
                    <Field name="limitType"/>
                </Fields>
            </Table>
        </Anchor>
        <Anchor title="经销商">
            <Table model="b2b_contract_AgreementDealerBO" title="经销商"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="agreementBO = ?"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="dealer" label="经销商"/>
                    <Field name="region.districtName" label="大区"/>
                    <Field name="districtStr"/>
                </Fields>
            </Table>
        </Anchor>
        <Anchor title="协议清单" show="#{!!getContainerByKey('agreement').data.canRelateDetail}">
            <Table model="b2b_contract_AgreementDetailBO" title="协议清单"
                   key="PurchaserAgreementDetailList"
                   fuzzySearchable="#{false}" columnOrder="#{false}" virtual="#{true}"
                   dataFunction="b2b_contract_PagingWorkspaceAgreementDetailFunc"
                   showEditableIcon="#{true}" onUpdate = "#{changeSpuBO}" advanced="#{true}"
                   dataParams="{fromSite:{value:'agreementView'},agreementBO:{id:{value:env.agreementId ? env.agreementId : pageContext.record.id}}}" customDerivedData="false">
                <Search>
                    <Fields>
                        <Field name="statusDict"/>
                        <Field name="attribute" label="属性"/>
                        <Field name="spuBO.spuCode" label="标品编码"/>
                    </Fields>
                </Search>
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="source" show="#{false}"/>
                    <Field name="agreementBO" show="#{false}"/>
                    <Field name="agreementBO.type" show="#{false}"/>
                    <Field label="供应商" name="saleEntityBO.entityName"/>
                    <Field name="syncInfo.mateName" label = "原协议清单项">
                        <RenderType>
                            <Action targetView="b2b_contract_AgreementDetailBO_AgreementDetail"
                                    openViewType="Dialog"
                                    env="#{{agreementDetailId: this.record.id}}"/>
                        </RenderType>
                    </Field>
                    <Field name="statusDict"/>
<!--                    <Field label="关联标品" name="spuBO" editable="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO}">-->
<!--                        <RenderType>-->
<!--                            <ModelSelect targetView="item_SpuBO_SingleSelectAgreementSpu"-->
<!--                                         env="#{{agreementId:this.record.agreementBO.id}}"/>-->
<!--                            <MainField showPanel="#{false}" onlyText="#{true}" editable="#{false}"/>-->
<!--                        </RenderType>-->
<!--                    </Field>-->
                    <Field label="关联标品" name="spuBO" columnSize="large" initValue="#{this.record?.spuBO ?? '-'}" editable="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO}">
                        <RenderType>
                            <ModelSelect modalWidth="1000" modalTitle="选择关联标品" model="item_SpuBO" dataFunction="b2b_contract_PagingAgreementSpuFunc"
                                         dataParams="#{{id:{value:this.record.agreementBO.id}}}" selection="radio"
                                         fuzzySearchable="#{false}">
                                <!--        <Alert type="normal" message="#{env.alertMsg}"/>-->
                                <Search>
                                    <Fields>
                                        <Field name="spuCode" label="标品编码"/>
                                        <Field name="name" label="标品名称"/>
                                        <Field name="category">
                                            <RenderType>
                                                <CascadeModelSelect isLeafField="isLeaf" depthLimit="4" parentField="parentCategory"
                                                                    columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                                                    dataSource="md_CategoryBO_CategoryTreeProvider"
                                                                    dataParams="#{{categoryStatus:'ENABLED'}}"
                                                                    model="md_CategoryBO" valueField="id"
                                                                    labelField="categoryName" modalWidth="#{800}"
                                                                    modalTitle="选择分类" searchInputWidth="#{400}"/>
                                            </RenderType>
                                        </Field>
                                        <Field name="thingMaterial"/>
                                        <Field name="thingSize"/>
                                    </Fields>
                                </Search>
                                <Fields>
                                    <Field name="id" show="#{false}"/>
                                    <Field label="标品编码" name="spuCode"/>
                                    <Field label="标品名称" name="name"/>
                                    <Field label="关联物料" name="thing.thingName"/>
                                    <Field label="分类" name="category.path"/>
                                    <!--            <Field name="thingMaterial"/>-->
                                    <!--            <Field name="thingSize"/>-->
                                    <Field label="计量单位" name="thing.unit.unitName"/>
                                    <Field label="属性" name="attributes" columnSize="large">
                                        <RenderType>
                                            <Text format="#{formatAttributes}"/>
                                        </RenderType>
                                    </Field>
                                </Fields>
                            </ModelSelect>
                            <MainField showPanel="#{false}"  onlyText="#{true}" editable="#{false}"/>
                        </RenderType>
                    </Field>
                    <Field label="标品编码" name="spuBO.spuCode"/>
                    <Field name="categoryBO.path" label="分类"/>
                    <Field name="spuBO.thing" show="#{false}"/>
                    <Field name="spuBO.thingName" label="关联物料">
                        <RenderType>
                            <Action targetView="md_thingBO_B2BThingDetail"
                                    openViewType="Dialog"
                                    env="#{{thingId: this.record.spuBO.thing.id}}"/>
                        </RenderType>
                    </Field>

                    <!--                    <Field name="thingMaterial"/>-->
                    <!--                    <Field name="thingSize"/>-->
                    <Field name="spuBO.attributes" label="属性">
                        <RenderType>
                            <Text format="#{formatAttributes}"/>
                        </RenderType>
                    </Field>
                    <Field name="taxRate.taxRateShow" label="税率"/>
                    <Field name="supplyInfo.price">
                        <RenderType>
                            <Number format="0.[0000]"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.taxPrice">
                        <RenderType>
                            <Number format="0.[0000]"/>
                        </RenderType>
                    </Field>
                    <Field name="unit.unitName" label="计量单位"/>
                    <Field name="brands">
                        <RenderType>
                            <MainField onlyText="#{true}"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.quotationBasis" show="#{false}"/>
                    <Field name="priceSchemeBO.id" show="#{false}"/>
                    <Field name="priceSchemeBO.name" label="价格方案">
                        <RenderType>
                            <Action env="#{{priceSchemeId: this.record.priceSchemeBO.id}}"
                                    openViewType="Dialog"
                                    targetView="item_PriceSchemeBO_PriceSchemeDetail"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.supplyPeriod" />
                    <Field name="source" />
                    <Field name="outerCode"/>
                    <Field label="创建人" name="createdBy.username"/>
                    <Field label="创建时间" name="createdAt"/>
                    <!--                    <Field name="supplyInfo.supplyPeriod">-->
                    <!--                        <RenderType>-->
                    <!--                            <Number unit="天"/>-->
                    <!--                        </RenderType>-->
                    <!--                    </Field>-->
                    <!--                    <Field name="statusDict"/>-->
                </Fields>
                <RecordActions>
                    <Action label="detail" targetView="b2b_contract_AgreementDetailBO_AgreementDetail" authViewKey="详情"/>
                    <Action label="edit" show="#{this.record.source==='AUTOMATIC'}"
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailEdit" authViewKey="编辑"/>
                    <Action label="映射标品" action="#{mapSpu}"
                            show="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO &amp;&amp; getContainerByKey('agreement').data.editPermission}"/>
                    <Action label="设置价格方案" action="#{setPriceScheme}"
                            show="#{(this.record.statusDict === 'START_USING' || this.record.statusDict === 'STOP_USING') &amp;&amp; getContainerByKey('agreement').data.editPermission}"/>
                    <Action label="查看价格方案" action="#{viewPriceScheme}"/>
                    <Action label="失效"
                            show="#{this.record.statusDict ==='START_USING' || this.record.statusDict ==='STOP_USING'}"
                            confirm="确认失效么？" logicFunc="b2b_contract_AgreementDetailLoseFunc" after="Refresh" authViewKey="失效"/>
<!--                    <Action label="设置库存状态" openViewType="Dialog" openViewSize="s"-->
<!--                            show="#{this.record.statusDict === 'START_USING'}"-->
<!--                            targetView="b2b_contract_AgreementDetailBO_AgreementInventoryStatusUpdate"/>-->
                    <Action label="删除" show="#{this.record.source === 'AUTOMATIC' &amp;&amp; getContainerByKey('agreement').data.editPermission}"
                            confirm="确认删除么？此操作不可逆，请谨慎操作"
                            logicFunc="b2b_contract_AgreementDetailDeleteFunc" after="Refresh"/>
                </RecordActions>
                <Actions>
                    <Action label="删除" multi="#{true}" confirm="确认删除么？此操作不可逆，请谨慎操作"
                            logicFunc="b2b_contract_AgreementDetailBatchDeleteFunc"
                            after="Refresh" validator="#{validateBatchDelete}" show="#{getContainerByKey('agreement').data.editPermission}"/>
                    <!--<Action label="映射标品" action="#{batchMapSpu}" multi="#{true}"
                            validator="#{validateBatchMapSpu}" show="#{getContainerByKey('agreement').data.editPermission}"/>-->
                    <Action label="设置价格方案" multi="#{true}"
                            action="#{batchSetPriceScheme}" validator="#{validateBatchSetPrice}" show="#{getContainerByKey('agreement').data.editPermission}" />
<!--                    <Action openViewType="Dialog" label="设置库存状态" multi="#{true}" openViewSize="s"-->
<!--                            targetView="b2b_contract_AgreementDetailBO_AgreementInventoryStatusBatchUpdate"-->
<!--                            validator="#{validateBatchSetInventoryStatus}"/>-->
                    <Action label="新建" type="Submit"
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailCreate"
                            show="#{['enabled','expired','disabled'].includes(getContainerByKey('agreement').data.status) &amp;&amp; getContainerByKey('agreement').data.editPermission }"
                            env="#{{agreement:getContainerByKey('agreement').data}}"
                            openViewType="Dialog"/>
                </Actions>
            </Table>
        </Anchor>
        <Anchor title="补充协议" show="#{!getContainerByKey('agreement').data.isSupply}">
            <Table model="b2b_contract_AgreementBO" title="补充协议"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="mainAgreement = ? AND isSupply = true"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="code">
                        <RenderType>
                            <Action env="#{{agreementId: this.record.id}}"
                                    openViewType="Dialog"
                                    targetView="b2b_contract_AgreementBO_PurchaserAgreementInfo"/>
                        </RenderType>
                    </Field>
                    <Field name="externalNo"/>
                    <Field name="name"/>
                    <Field name="effectiveAt"/>
                    <Field name="expireAt"/>
                    <Field name="signAt"/>
                    <Field name="status"/>
                    <Field name="operator"/>
                    <Field name="createdAt"/>
                </Fields>
                <RecordActions>
                    <Action label="详情" openViewType="Dialog" openViewSize="l"
                            targetView="b2b_contract_AgreementBO_PurchaserAgreementInfo"/>
                </RecordActions>
            </Table>
        </Anchor>
        <Anchor title="关联合同"
                show="#{!!getContainerByKey('agreement').data.type &amp;&amp; getContainerByKey('agreement').data.type !== 'self'}">
            <Table model="b2b_contract_YzContractBO" title="关联合同"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataFunction="b2b_contract_PagingYzContractFunc"
                   dataParams="{isSupply:{value:false},agreementBO:{id:{value:env.agreementId ? env.agreementId : pageContext.record.id}}}">
                <Fields>
                    <Field name="contractCode"/>
                    <Field name="externalCode" show="#{false}"/>
                    <Field name="contractName"/>
                    <Field name="contractTypeDict"/>
                    <Field name="contractAmt"/>
                    <Field name="signAt"/>
                    <Field name="contractStatusDict"/>
                    <Field name="isSupply"/>
                    <Field name="subContracts">
                        <RenderType>
                            <ModelInfo>
                                <Fields>
                                    <Field name="contractCode"/>
                                    <Field name="externalCode" show="#{false}"/>
                                    <Field name="contractName"/>
                                    <Field name="contractTypeDict"/>
                                    <Field name="contractAmt"/>
                                    <Field name="signAt"/>
                                    <Field name="contractStatusDict"/>
                                    <Field name="isSupply"/>
                                </Fields>
                            </ModelInfo>
                        </RenderType>
                    </Field>
                </Fields>
                <RecordActions>
                    <Action label="详情" targetView="contract_ContractBO_yzContractInfo"
                            env="#{{ url: this.record.yzPurchaserDetailUrl }}"/>
                </RecordActions>
            </Table>
        </Anchor>
    </Anchors>
</View>