<?xml version="1.0" encoding="UTF-8"?>
<View title="维护协议信息" forModel="b2b_contract_AgreementBO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Form key="agreement" model="b2b_contract_AgreementBO" dataCondition="id = ?"
          dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]"
          onFieldChange="#{agreementChange}" onDataLoaded="#{onDataLoaded}">
        <Alert type="error" message="#{getContainerByKey('agreement').data.syncRemark}"
               show="#{getContainerByKey('agreement').data.syncStatus === 'fail'}"/>
        <Fields>
            <GroupField title="基础信息">
                <Field name="id" show="#{false}"/>
                <Field name="code" readonly="#{true}"/>
                <Field name="externalNo" readonly="#{true}"/>
                <Field name="name" readonly="#{true}">
                    <Validations>
                        <Validation required="#{true}" message="协议名称不能为空"/>
                    </Validations>
                    <RenderType>
                        <Input maxLength="#{255}"/>
                    </RenderType>
                </Field>
                <Field name="status" readonly="#{true}"/>
                <Field name="jfCompanyBO" readonly="#{this.data.useTemplateMaintain}">
                    <RenderType>
                        <ModelSelect targetView="md_EntityBO_SelectEntity"
                                     modalWidth="#{700}"
                                     env="#{{condition:'entityStatus=\'ENABLED\' and merchantType !=\'SUPPLIER\'',purchase:false}}"/>
                    </RenderType>
                    <Validations>
                        <Validation required="#{true}" message="协议甲方不能为空"/>
                    </Validations>
                </Field>
                <Field name="type" readonly="#{true}"/>
                <Field name="departmentId" show="#{false}" submit="#{false}"/>
                <Field name="departments" readonly="#{!this.data.jfCompanyBO || this.data.useTemplateMaintain}" tips="请先选择协议甲方">
                    <Validations>
                        <Validation required="#{true}" message="使用单位不能为空"/>
                    </Validations>
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="6" parentField="department"
                                            columnTitles="#{['一级部门', '二级部门', '三级部门', '四级部门','五级部门','六级部门']}"
                                            dataSource="organization_DepartmentBO_DepartmentTreeProvider"
                                            dataParams="#{{departmentStatusDict:'ENABLED',department:{id:this.data.departmentId},withCurrent:true}}"
                                            model="organization_DepartmentBO" valueField="id" linkSelectMode="#{true}"
                                            labelField="departmentName" modalTitle="选择部门" modalWidth="1000"
                                            searchInputWidth="600"/>
                    </RenderType>
                </Field>
                <Field name="projectTypes" show="#{this.data.type == 'match_making'}" readonly="#{this.data.useTemplateMaintain}">
                    <Validations>
                        <Validation required="#{true}" message="适用项目类型不能为空"/>
                        <Validation validator="#{(rule, types, callback) => {if(getContainerByKey('agreement').data.type != 'virtual' &amp;&amp; types.length &gt; 1)callback('适用项目类型只能选一个')}}"/>
                    </Validations>
                </Field>
                <Field name="projectTypes" show="#{this.data.type == 'self'||this.data.type == 'virtual'}" readonly="#{this.data.useTemplateMaintain}">
                    <Validations>
                        <Validation required="#{true}" message="适用项目类型不能为空"/>
                    </Validations>
                </Field>
                <Field name="category" readonly="#{this.data.useTemplateMaintain}">
                    <Validations>
                        <Validation required="#{true}" message="分类不能为空"/>
                    </Validations>
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="2" leafOnly="#{true}" parentField="parentCategory"
                                            columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                            dataSource="md_CategoryBO_CategoryTreeProvider"
                                            dataParams="#{{categoryStatus:'ENABLED'}}"
                                            model="md_CategoryBO" valueField="id"
                                            labelField="categoryName" modalWidth="#{800}"
                                            modalTitle="选择分类" searchInputWidth="#{400}"/>
                    </RenderType>
                </Field>
                <Field name="category.bulkCategory" label="分类" show = "false"/>
                <Field name="yfCompanyBO">
                    <RenderType>
                        <ModelSelect targetView="md_EntityBO_SelectEntity"
                                     modalWidth="#{700}" env="#{{condition:'entityStatus=\'ENABLED\''}}"/>
                    </RenderType>
                    <Validations>
                        <Validation required="#{true}" message="协议乙方不能为空"/>
                    </Validations>
                </Field>
                <Field name="bizType" readonly="#{this.data.useTemplateMaintain}">
                    <RenderType>
                        <Select disabledValues="#{values => filterBizType(values, this.record)}"/>
                    </RenderType>
                    <Validations>
                        <Validation required="#{true}" message="业务类型不能为空"/>
                        <Validation validator="#{(rule, types, callback) => {checkBizType(rule, types, callback, this.record)}}"/>
                    </Validations>
                </Field>
                <Field name="rentPeriod" show="#{this.data.bizType == 'rent'}"/>
                <Field name="signAt">
                    <Validations>
                        <Validation required="#{true}" message="签约日期不能为空"/>
                    </Validations>
                </Field>
                <Field name="effectiveAt">
                    <Validations>
                        <Validation required="#{true}" message="生效日期不能为空"/>
                    </Validations>
                </Field>
                <Field name="expireAt">
                    <Validations>
                        <Validation required="#{true}" message="终止日期不能为空"/>
                    </Validations>
                    <RenderType>
                        <DatePicker disabledDate="#{(current) => current.isBefore(new Date(),'day')}"/>
                    </RenderType>
                </Field>
                <Field name="coverAreaType">
                    <Validations>
                        <Validation required="#{true}" message="覆盖范围不能为空"/>
                    </Validations>
                    <RenderType>
                        <Radio direction="horizontal"/>
                    </RenderType>
                </Field>
                <Field name="isSupply" readonly="#{true}"/>
                <Field name="mainAgreement.id" show="#{false}"/>
                <Field name="mainAgreement.name" label="关联主协议" readonly="#{true}">
                    <RenderType>
                        <Action env="#{{agreementId: this.record.mainAgreement.id}}"
                                openViewType="Dialog"
                                targetView="b2b_contract_AgreementBO_AgreementInfo"/>
                    </RenderType>
                </Field>
<!--                <Field name="operator"/>-->
                <!-- 计价模式（自营销售使用虚拟框架协议，type='virtual'）（自营采购使用自营采购框架协议，type='sell'）（撮合使用撮合框架协议，type='match_making'） -->
                <Field name="priceMode" show="#{this.data.type === 'self'}"
                       tips="（1）含税模式：根据含税价和税率计算不含税价；\n（2）不含税模式：根据不含税价和税率计算含税价。">
                    <Validations>
                        <Validation required="#{true}" message="计价模式不能为空"/>
                    </Validations>
                </Field>
                <Field name="sourceDict" readonly="#{true}"/>
                <Field name="createdAt" readonly="#{true}"/>
                <Field name="isCenterPay"/>
                <Field name="canRelateDetail"/>
                <Field name="contractCanEditPayScheme" />
                <Field name="remark"/>
                <Field name="attachment"/>
                <Field name="syncStatus" show="#{false}"/>
                <Field name="syncRemark" show="#{false}"/>
                <Field name="useTemplateMaintain" show="#{false}"/>
            </GroupField>
            <GroupField title="金额信息" show="#{this.data.type === 'self'}">
                <Field name="taxAmt">
<!--                    <Validations>-->
<!--                        <Validation required="#{true}" message="含税金额不能为空"/>-->
<!--                    </Validations>-->
                </Field>
                <Field name="taxRateBO">
<!--                    <Validations>-->
<!--                        <Validation required="#{true}" message="税率不能为空"/>-->
<!--                    </Validations>-->
                    <RenderType>
                        <ModelSelect dataFunction="md_PagingTaxRateBOBuiltInFunc"
                                     dataParams="{businessType:{type:'Collection',values:['PURCHASE']},taxRateStatus:'ENABLED'}"/>
                    </RenderType>
                </Field>
                <Field name="noTaxAmt" readonly="#{true}"/>
                <Field name="taxPrc" readonly="#{true}"/>
            </GroupField>
            <GroupField title="审批表单补充信息" show="#{this.data.type === 'self'}">
                <!-- 对接OA后，打开注释 -->
                <Field name="agreementBpmBO.bpmSubjectTitle"/>
                <Field name="agreementBpmBO.supplementAgreement"/>
                <Field name="agreementBpmBO.mainContractName"/>
                <Field name="agreementBpmBO.mainContractCode"/>
                <Field name="agreementBpmBO.innerContractCode"/>
                <Field name="agreementBpmBO.priceCalRule"/>
                <Field name="agreementBpmBO.invoiceTypeBpm"/>
                <Field name="agreementBpmBO.maintenanceRate"/>
                <Field name="agreementBpmBO.performanceBondType"/>
                <Field name="agreementBpmBO.payWay"/>
            </GroupField>

            <GroupField title="招标信息" show="#{this.data.sourceDict === 'sync'}">
                <Field name="bidTaskCode" readonly="#{true}"/>
                <Field name="bidTaskName" readonly="#{true}"/>
                <Field name="jcBidInfo" show="#{false}"/>
                <Field name="jcBidInfo.categoryName" readonly="#{true}"/>
            </GroupField>
            <GroupField singleColumn="true">
                <Field name="remarks" colSize='all' >
                    <RenderType>
                        <RichText rows="#{12}"></RichText>
                    </RenderType>
                </Field>
            </GroupField>
        </Fields>
        <Actions>
            <Action label="取消" layout="Footer" action="GoBack"/>
            <!-- logicFunc="b2b_contract_EditAgreementExtendInfoFunc" -->
            <Action label="确认" type="Submit" layout="Footer"
                    action="#{submitAgreementExtendInfo}"
                    after="Refresh"/>
        </Actions>
    </Form>
    <Anchors>
        <Anchor title="定时账期基本设置" show="#{getContainerByKey('agreement').data.type === 'self'}">
            <TableForm title="定时账期基本设置" show="#{getContainerByKey('agreement').data.type === 'self'}" model="b2b_contract_AgreementRegularReconciliationBO" key="agreementRegularReconciliation"
                       lookupFrom="agreement.regularReconciliations">
                <Fields>
                    <Field name="day" label="每个月定时对账日期（日）">
                        <Validations>
                            <Validation required="#{true}" message="自动对账日期不能为空"/>
                            <Validation validator="#{(rule,day, callback) => {if((day &amp;&amp; (day &gt; 31 || day &lt; 1)) || day ==0) {callback('定时对账日期不能小于1日或超过31日')}}}" message="定时对账日期不能小于1日或超过31日" />

                        </Validations>

                    </Field>
                </Fields>
            </TableForm>
        </Anchor>
        <Anchor title="联系信息">
            <TableForm title="联系信息（非必填）" model="b2b_contract_AgreementContactBO" key="agreementContacts"
                       lookupFrom="agreement.contacts">
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="person">
                        <Validations>
                            <Validation required="#{true}" message="联系人不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="certificateType">
                        <Validations>
                            <Validation required="#{false}" message="证件类型不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="certificateNo">
                        <Validations>
                            <Validation required="#{false}" message="证件号不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="position">
                        <Validations>
                            <Validation required="#{false}" message="职位不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="phone">
                        <Validations>
                            <Validation required="#{true}" message="联系方式不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="accreditType" show = "#{getContainerByKey('agreement').data.type === 'self'}">
                        <Validations>
                            <Validation required="#{true}" message="电签权限不能为空"/>
                        </Validations>
                        <RenderType>
                            <!--表单视图单字典为单选，取值结果为字符串-->
                            <Select />
                        </RenderType>
                    </Field>
                    <Field name="districts">
                        <Validations>
                            <Validation required="#{false}" message="负责城市不能为空"/>
                        </Validations>
                        <RenderType>
                            <CascadeModelSelect isLeafField="isLeaf"
                                                depthLimit="4"
                                                columnTitles="#{['大区','省','市','区']}"
                                                dataSource="md_DistrictBO_DistrictTreeProvider"
                                                dataParams="#{{districtStatus:'ENABLED'}}"
                                                parentField="parentDistrict"
                                                model="md_DistrictBO" valueField="id" labelField="districtName"
                                                linkSelectMode="#{true}" modalTitle="选择地址"/>
                        </RenderType>
                    </Field>
                </Fields>
            </TableForm>
        </Anchor>

        <Anchor title="付款方案">
            <TableForm title="付款方案（必填）" model="b2b_contract_PaymentSchemeBO" key="agreementPaymentSchemes"
                       lookupFrom="agreement.paymentSchemes" showAdd="#{false}" showDelete="#{false}"
                       minDataCount="#{1}">
                <Fields>
                    <Field name="id"  show="#{false}"/>
                    <Field name="code" readonly="#{true}"/>
                    <Field name="title" readonly="#{true}" />
                    <Field name="description" readonly="#{true}"/>
                    <Field name="schemeNodes" show="#{false}"/>
                    <Field name="paymentSchemeTemplateBO.id" show="#{false}"/>
                    <Field name="apply" label="是否被应用" readonly="#{true}" show="#{getContainerByKey('agreement').data.type==='match_making'}" tips="应用该付款方案下对应的协议价格,将会被商城计算价格优先使用"/>
                </Fields>
                <RecordActions>
                    <Action label="详情"  targetView="b2b_contract_PaymentSchemeBO_PaymentSchemeDetail"  openViewType="Dialog"/>
                    <Action label="编辑" action="#{editPaymentScheme}" show="#{!this.record.paymentSchemeTemplateBO?.id}"/>
                    <Action label="删除" action="#{deletePaymentScheme}"/>
                    <Action label="应用" action="#{applyPaymentScheme}" show="#{getContainerByKey('agreement').data.type==='match_making'}"/>
                </RecordActions>
                <Actions>
                    <Action label="新建方案" action="#{addPaymentScheme}"/>
                    <Action label="选择方案模版" action="#{selectPaymentSchemeTemplate}"/>
                </Actions>
            </TableForm>
        </Anchor>

        <Anchor title="#{`应${getContainerByKey('agreement').data.type=='virtual'?'收':'付'}账款方案`}" show="#{['virtual','self'].includes(getContainerByKey('agreement').data.type)}">
            <TableForm title="#{`应${getContainerByKey('agreement').data.type=='virtual'?'收':'付'}账款方案`}"
                       model="b2b_contract_PayableSchemeTO" key="payableScheme"
                       show="#{['virtual','self'].includes(getContainerByKey('agreement').data.type)}"
                       lookupFrom="agreement.payableSchemeList" showAdd="#{false}" showDelete="#{false}">
                <Alert message="应收账款方案为管理自营业务中采购商向供应链公司支付货款的账期及金额，所有应收款均在对账单双方确认后开始计算。" show="#{getContainerByKey('agreement').data.type==='virtual'}"/>
                <Alert message="应付账款方案为管理自营业务中供应链公司向供应商支付货款的账期及金额，所有应付款均在对账单双方确认后开始计算。" show="#{getContainerByKey('agreement').data.type==='self'}"/>
                <Fields>
                    <Field name="code" label="编码" readonly="#{true}"/>
                    <Field name="title" label="节点" readonly="#{true}"/>
                    <Field name="content" label="账期" readonly="#{true}"/>
                    <Field name="schemeNodes" show="#{false}"/>
                    <Field name="mark" readonly="true"/>
                    <Field name="payableSchemeTemplateBO" show="#{false}"/>
                </Fields>
                <RecordActions>
                    <Action label="编辑" action="#{editPayableScheme}" show="#{!this.record.payableSchemeTemplateBO?.id}"/>
                    <Action label="详情"  openViewType="Dialog" targetView="b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeDetail" />
                    <Action label="删除" action="#{deletePayableScheme}"/>
                </RecordActions>
                <Actions>
                    <Action label="新建方案" action="#{addPayableAmtScheme}"/>
                    <Action label="选择方案模版" action="#{selectPayableAmtScheme}" show="#{getContainerByKey('agreement').data.type==='virtual'}"/>
                </Actions>
            </TableForm>
        </Anchor>

        <Anchor title="覆盖范围" show="#{getContainerByKey('agreement').data.coverAreaType === 'appoint'}">
            <TableForm model="b2b_contract_AgreementCoverAreaBO" title="覆盖范围"
                       onFieldChange="#{coverAreaChange}" key="coverAreas"
                       lookupFrom="agreement.coverAreas">
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="region" resetFields="districts">
                        <RenderType>
<!--                            <ModelSelect dataCondition="`districtStatus` = 'ENABLED' AND `type` = 'big_area'"/>-->
                            <ModelSelect targetView="md_DistrictBO_SelectDistrict"
                                         modalWidth="#{700}"
                                         env="#{{condition:'districtStatus=\'ENABLED\' and type =\'big_area\''}}"/>
                        </RenderType>
                        <Validations>
                            <Validation required="#{true}" message="大区不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="districts" readonly="#{!this.record.region}" tips="请先选择大区">
                        <RenderType>
                            <CascadeModelSelect isLeafField="isLeaf"
                                                depthLimit="4"
                                                columnTitles="#{['省','市','区']}"
                                                dataSource="md_DistrictBO_DistrictTreeProvider"
                                                dataParams="#{{districtStatus:'ENABLED',parentDistrict:{id:this.record.region.id}}}"
                                                parentField="parentDistrict"
                                                model="md_DistrictBO" valueField="id" labelField="districtName"
                                                linkSelectMode="#{true}" modalTitle="选择地址"/>
                        </RenderType>
                        <Validations>
                            <Validation required="#{true}" message="省市区不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="limitType">
                        <Validations>
                            <Validation required="#{false}" message="框架限制不能为空"/>
                        </Validations>
                    </Field>
                </Fields>
            </TableForm>
        </Anchor>
        <Anchor title="经销商">
            <TableForm title="经销商（非必填）"
                       model="b2b_contract_AgreementDealerBO" key="dealers"
                       onFieldChange="#{dealerChange}"
                       lookupFrom="agreement.dealers">
                <Alert message="若框架协议出现以下情况需填写此栏，其余情况可不填写： 1、地产类框架协议：签约乙方为品牌方，实际供货方为经销商时需填写具体经销商信息。 2、施工类框架协议：混凝土框架协议中，签约乙方为搅拌站上属母公司，实际供货方及执行合同乙方为**搅拌站时，需填写具体搅拌站信息。"/>
                <Fields>
                    <Field name="dealer">
                        <RenderType>
                            <ModelSelect targetView="md_EntityBO_SelectEntity"
                                         modalWidth="#{700}"
                                         env="#{{condition:'entityStatus=\'ENABLED\' and merchantType =\'SUPPLIER\''}}"/>
                        </RenderType>
                        <Validations>
                            <Validation required="#{true}" message="经销商不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="region" resetFields="districts">
                        <RenderType>
                            <ModelSelect targetView="md_DistrictBO_SelectDistrict"
                                         modalWidth="#{700}"
                                         env="#{{condition:'districtStatus=\'ENABLED\' and type =\'big_area\''}}"/>
<!--                            <ModelSelect dataCondition="`districtStatus` = 'ENABLED' AND `type` = 'big_area'"/>-->
                        </RenderType>
                        <Validations>
                            <Validation required="#{true}" message="大区不能为空"/>
                        </Validations>
                    </Field>
                    <Field name="districts" readonly="#{!this.record.region}" tips="请先选择大区">
                        <RenderType>
                            <CascadeModelSelect isLeafField="isLeaf"
                                                depthLimit="4"
                                                columnTitles="#{['省','市','区']}"
                                                dataSource="md_DistrictBO_DistrictTreeProvider"
                                                dataParams="#{{districtStatus:'ENABLED',parentDistrict:{id:this.record.region.id}}}"
                                                parentField="parentDistrict"
                                                model="md_DistrictBO" valueField="id" labelField="districtName"
                                                linkSelectMode="#{true}" modalTitle="选择地址"/>
                        </RenderType>
                        <Validations>
                            <Validation required="#{true}" message="省市区不能为空"/>
                        </Validations>
                    </Field>
                </Fields>
            </TableForm>
        </Anchor>

        <Anchor title="协议清单" show="#{!!getContainerByKey('agreement').data.canRelateDetail &amp;&amp; !getContainerByKey('agreement').data.isSupply}">
            <!--todo  showEditableIcon="#{true}" onUpdate = "#{agreementDetailListChange}" -->
            <TableForm title="协议清单"
                       model="b2b_contract_AgreementDetailBO" key="agreementDetailList"
                       dataFunction="b2b_contract_WorkspaceAgreementDetailFunc"
                       showDelete = "false" showDeleteOnTitle = "false" virtual="true"
                       dataParams="{fromSite:{value:'agreementView'},agreementBO:{id:{value:env.agreementId ? env.agreementId : pageContext.record.id}}}"
                       onFieldChange="#{agreementDetailListChange}" showAdd="false">
                <Search>
                    <Fields>
                        <Field name="statusDict"/>
                    </Fields>
                </Search>
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="source" show="#{false}"/>
                    <Field name="thingMaterialId" show="#{false}"/>
                    <Field name="agreementBO" label="框架协议" show="#{false}" readonly = "true">
                        <Validations>
                            <Validation required="true" message="请选择框架协议"/>
                        </Validations>
                        <RenderType>
                            <ModelSelect onlyText="#{true}"
                                         targetView="b2b_contract_AgreementBO_SelectAgreement"
                                         env="#{{condition:'{status:{values:[\'enabled\', \'disabled\', \'draft\', \'abnormal\']}}'}}"/>
                        </RenderType>
                    </Field>
                    <Field name="agreementBO.id" show="#{false}" />
                    <Field name="agreementBO.type" show="#{false}"/>
                    <Field label="供应商" name="saleEntityBO" show="#{false}"/>
                    <Field label="供应商" name="saleEntityBO.entityName" readonly = "true"/>
                    <Field name="syncInfo" label = "原协议清单项" show="#{false}"/>
                    <Field name="syncInfo.mateName" label = "原协议清单项" readonly = "true">
                        <RenderType>
                            <Action targetView="b2b_contract_AgreementDetailBO_AgreementDetail"
                                    openViewType="Dialog"
                                    env="#{{agreementDetailId: this.record.id}}"/>
                        </RenderType>
                    </Field>
                    <Field name="statusDict" readonly = "true"/>
                    <Field name="spuBO" label="关联标品" >
                        <Validations>
                            <Validation required="#{!this.record.source==='CLOUD_BUILD'}" message="标品不能为空"/>
                        </Validations>
                        <RenderType>
                            <ModelSelect targetView="item_SpuBO_SingleSelectAgreementSpu"
                                         env="#{{agreementId:this.record.agreementBO.id}}"/>
                        </RenderType>
                    </Field>
                    <Field label="标品编码" name="spuBO.spuCode" readonly="true"/>
                    <Field name="categoryBO" label="分类" show="#{false}"/>
                    <Field name="categoryBO.path" label="分类" readonly = "true"/>
                    <Field name="spuBO.thing" show="#{false}"/>
                    <Field name="spuBO.thingName" label="关联物料" readonly = "true">
                        <RenderType>
                            <Action targetView="md_thingBO_B2BThingDetail"
                                    openViewType="Dialog"
                                    env="#{{thingId: this.record.spuBO.thing.id}}"/>
                        </RenderType>
                    </Field>
                    <Field name="spuBO.attributes" label="属性" readonly = "true">
                        <RenderType>
                            <Text format="#{formatAttributes}"/>
                        </RenderType>
                    </Field>
                    <Field name="taxRate" label="税率" readonly="#{this.record.source==='CLOUD_BUILD'}">
                        <Validations>
                            <Validation required="#{!this.record.source==='CLOUD_BUILD'}" message="请填写税率"/>
                        </Validations>
                        <RenderType>
                            <ModelSelect dataFunction="md_PagingTaxRateBOBuiltInFunc"
                                         dataParams="{businessType:{type:'Collection',values:['PURCHASE']},taxRateStatus:'ENABLED'}"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo" show = "false"/>
                    <Field name="supplyInfo.price" readonly="#{true}">
                        <RenderType>
                            <Number format="0.[0000]"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.taxPrice" readonly="#{this.record.source==='CLOUD_BUILD'}">
                        <RenderType>
                            <InputNumber digits="4" fullWidth="true"/>
                        </RenderType>
                        <Validations>
                            <Validation required="#{!this.record.source==='CLOUD_BUILD'}" message="请填写含税价"/>
                        </Validations>
                    </Field>
                    <Field name="unit" label="计量单位" readonly = "true"/>
                    <Field name="spuBO.rawMaterialContent" label="铜含量（kg/m）" show="#{this.data.category.bulkCategory === true}" readonly="true"/>
                    <Field name="copperBasicPrice" label="参考铜基价" show="#{this.data.category.bulkCategory === true}" readonly="true"/>
                    <Field name="yanmiCopperPrice" label="延米铜价" show="#{this.data.category.bulkCategory === true}" readonly="true"/>
                    <Field name="otherCosts" label="辅材及其他费用" show="#{this.data.category.bulkCategory === true}">
                        <Validations>
                            <Validation required="#{true}" message="辅材及其他费用不能为空"/>
                            <Validation validator="#{(rule, otherCosts, callback) => {if(otherCosts &amp;&amp; otherCosts &lt; 0)callback('辅材及其他费用必须大于0')}}"/>
                        </Validations>
                    </Field>
                    <Field name="purDiscountFactor" label="采购折扣系数" show="#{this.data.category.bulkCategory === true}">
                        <Validations>
                            <Validation required="#{true}" message="采购折扣系数不能为空"/>
                            <Validation validator="#{(rule, purDiscountFactor, callback) => {if(purDiscountFactor != null &amp;&amp; (purDiscountFactor &gt; 1 || purDiscountFactor &lt; 0))callback('采购折扣系数大于0且小于等于1')}}" />
                        </Validations>
                    </Field>
                    <Field label="品牌" name="brandBo" readonly="#{this.record.source==='CLOUD_BUILD'}">
                        <RenderType>
                            <ModelSelect dataCondition="`brandStatus` = 'ENABLED'"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.quotationBasis" show="#{false}"/>
                    <Field name="priceSchemeBO.id" show="#{false}"/>
                    <!-- todo 编辑页面中  如果是撮合协议的期必须满足
                        #{(this.record.statusDict == 'START_USING' || this.record.statusDict == 'STOP_USING')}
                        才存在设置【价格方案】的按钮
                    -->
                    <Field name="priceSchemeBOList" label="价格方案"
                           readonly="#{!(this.record.statusDict == 'START_USING' || this.record.statusDict == 'STOP_USING') &amp;&amp; this.record.source==='CLOUD_BUILD'}"
                           show="#{this.record.agreementBO.type ==='match_making' }">
                        <RenderType>
                            <ModelSelect targetView="b2b_contract_PaymentRelatePriceSchemeCTO_SelectPaymentSchemeDetails"
                                         env="#{{record:this.record}}"
                                         payloadCallback = "#{payloadCallbackPriceSchemeBO}"/>
                        </RenderType>
                    </Field>
                    <!-- todo 编辑页面中 如果不是撮合协议的都能改变 -->
                    <Field name="priceSchemeBO" label="价格方案"
                           readonly="#{this.record.source==='CLOUD_BUILD'}"
                           show="#{this.record.agreementBO.type !=='match_making'}">
                        <RenderType>
                            <ModelSelect targetView="item_PriceSchemeBO_SingleSelectAgreementPriceScheme"
                                         env="#{{spuId:this.record.spuBO.id,agreementId:this.record.agreementBO.id,agreementType:this.record.agreementBO.type}}"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.supplyPeriod" readonly="#{this.record.source==='CLOUD_BUILD'}">
                        <RenderType>
                            <Number min="0" max="999"/>
                        </RenderType>
                    </Field>
                    <Field name="source" readonly = "true"/>
                    <Field name="outerCode" readonly = "true"/>
                    <Field label="创建人" name="createdBy.username" readonly = "true"/>
                    <Field label="创建时间" name="createdAt" readonly = "true"/>
                </Fields>
                <RecordActions>
                    <Action label="detail" targetView="b2b_contract_AgreementDetailBO_AgreementDetail" authViewKey="详情"/>
                    <!-- targetView="b2b_contract_AgreementDetailBO_AgreementDetailEdit" -->
                    <!-- logicFunc="b2b_contract_AgreementDetailEditFunc" -->
                    <Action label="edit" show="#{this.record.source==='AUTOMATIC'}"
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailEdit"
                            authViewKey="编辑" after="Refresh" />
                    <Action label="映射标品" action="#{mapSpu}"
                            show="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO }"
                            openViewType="Dialog"/>
                    <Action label="设置价格方案" action="#{setPriceScheme}"
                            show="#{(this.record.statusDict === 'START_USING' || this.record.statusDict === 'STOP_USING') }"
                            openViewType="Dialog"/>
                    <Action label="查看价格方案" action="#{viewPriceScheme}"/>
                    <Action label="启用"
                            show="#{this.record.statusDict ==='STOP_USING' }"
                            confirm="确认启用么？" logicFunc="b2b_contract_AgreementDetailEnableFunc" after="Refresh" />
                    <Action label="停用"
                            show="#{this.record.statusDict ==='START_USING' }"
                            confirm="确认停用么？" logicFunc="b2b_contract_AgreementDetailDisableFunc" after="Refresh" />
                    <Action label="失效"
                            show="#{this.record.statusDict ==='START_USING' || this.record.statusDict ==='STOP_USING'}"
                            confirm="确认失效么？" logicFunc="b2b_contract_AgreementDetailLoseFunc" after="Refresh" authViewKey="失效"/>
                    <Action label="删除" show="#{this.record.source === 'AUTOMATIC' }"
                            confirm="确认删除么？此操作不可逆，请谨慎操作"
                            validator="#{validateDelete}"
                            logicFunc="b2b_contract_AgreementDetailDeleteFunc" after="Refresh"/>
                </RecordActions>
                <Actions>
                    <Action label="删除" multi="#{true}" action="#{validateBatchDelete}"
                            after="Refresh" />
                    <!--<Action label="映射标品" multi="#{true}" action="#{batchMapSpu}"
                            validator="#{validateBatchMapSpu}" />-->
                    <Action label="设置价格方案" multi="#{true}"
                            action="#{batchSetPriceScheme}" validator="#{validateBatchSetPrice}" />
                    <Action label="新建"
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailCreate"
                            show="#{['enabled','expired','disabled'].includes(getContainerByKey('agreement').data.status)}"
                            env="#{{agreement:getContainerByKey('agreement').data}}"
                            openViewType="Dialog"/>
                </Actions>
            </TableForm>
        </Anchor>

        <Anchor title="协议清单" show="#{!!getContainerByKey('agreement').data.isSupply}">
            <TableForm title="协议清单"
                       model="b2b_contract_AgreementDetailBO" key="supplementAgreementDetailList"
                       dataFunction="b2b_contract_WorkspaceAgreementDetailFunc"
                       showDelete = "false" showDeleteOnTitle = "false" virtual="true"
                       dataParams="{fromSite:{value:'agreementView'},supplementAgreementBO:{id:{value:env.agreementId ? env.agreementId : pageContext.record.id}}}"
                       onFieldChange="#{supplementAgreementDetailListChange}" showAdd="false">
                <Search>
                    <Fields>
                        <Field name="statusDict"/>
                    </Fields>
                </Search>
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="source" show="#{false}"/>
                    <Field name="thingMaterialId" show="#{false}"/>
                    <Field name="agreementBO" label="框架协议" show="#{false}" readonly = "true">
                        <Validations>
                            <Validation required="true" message="请选择框架协议"/>
                        </Validations>
                        <RenderType>
                            <ModelSelect onlyText="#{true}"
                                         targetView="b2b_contract_AgreementBO_SelectAgreement"
                                         env="#{{condition:'{status:{values:[\'enabled\', \'disabled\', \'draft\', \'abnormal\']}}'}}"/>
                        </RenderType>
                    </Field>
                    <Field name="agreementBO.id" show="#{false}" />
                    <Field name="agreementBO.type" show="#{false}"/>
                    <Field label="供应商" name="saleEntityBO" show="#{false}"/>
                    <Field label="供应商" name="saleEntityBO.entityName" readonly = "true"/>
                    <Field name="syncInfo" label = "原协议清单项" show="#{false}"/>
                    <Field name="syncInfo.mateName" label = "原协议清单项" readonly = "true">
                        <RenderType>
                            <Action targetView="b2b_contract_AgreementDetailBO_AgreementDetail"
                                    openViewType="Dialog"
                                    env="#{{agreementDetailId: this.record.id}}"/>
                        </RenderType>
                    </Field>
                    <Field name="statusDict" readonly = "true"/>
                    <Field name="spuBO" label="关联标品" >
                        <Validations>
                            <Validation required="#{!this.record.source==='CLOUD_BUILD'}" message="标品不能为空"/>
                        </Validations>
                        <RenderType>
                            <ModelSelect targetView="item_SpuBO_SingleSelectAgreementSpu"
                                         env="#{{agreementId:this.record.agreementBO.id}}"/>
                        </RenderType>
                    </Field>
                    <Field label="标品编码" name="spuBO.spuCode" readonly="true"/>
                    <Field name="categoryBO" label="分类" show = "false"/>
                    <Field name="categoryBO.path" label="分类" readonly = "true"/>
                    <Field name="spuBO.thing" show="#{false}"/>
                    <Field name="spuBO.thingName" label="关联物料" readonly = "true">
                        <RenderType>
                            <Action targetView="md_thingBO_B2BThingDetail"
                                    openViewType="Dialog"
                                    env="#{{thingId: this.record.spuBO.thing.id}}"/>
                        </RenderType>
                    </Field>
                    <Field name="spuBO.attributes" label="属性" readonly = "true">
                        <RenderType>
                            <Text format="#{formatAttributes}"/>
                        </RenderType>
                    </Field>
                    <Field name="taxRate" label="税率" readonly="#{this.record.source==='CLOUD_BUILD'}">
                        <Validations>
                            <Validation required="#{!this.record.source==='CLOUD_BUILD'}" message="请填写税率"/>
                        </Validations>
                        <RenderType>
                            <ModelSelect dataFunction="md_PagingTaxRateBOBuiltInFunc"
                                         dataParams="{businessType:{type:'Collection',values:['PURCHASE']},taxRateStatus:'ENABLED'}"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo" show = "false"/>
                    <Field name="supplyInfo.price" readonly="#{true}">
                        <RenderType>
                            <Number format="0.[0000]"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.taxPrice" readonly="#{this.record.source==='CLOUD_BUILD'}">
                        <RenderType>
                            <InputNumber digits="4" fullWidth="true"/>
                        </RenderType>
                        <Validations>
                            <Validation required="#{!this.record.source==='CLOUD_BUILD'}" message="请填写含税价"/>
                        </Validations>
                    </Field>
                    <Field name="unit" label="计量单位" show="#{false}"/>
                    <Field name="unit.unitName" label="计量单位" readonly = "true"/>
                    <Field name="spuBO.rawMaterialContent" label="铜含量（kg/m）" show="#{this.data.category.bulkCategory === true}" readonly="true"/>
                    <Field name="copperBasicPrice" label="参考铜基价" show="#{this.data.category.bulkCategory === true}" readonly="true"/>
                    <Field name="yanmiCopperPrice" label="延米铜价" show="#{this.data.category.bulkCategory === true}" readonly="true"/>
                    <Field name="otherCosts" label="辅材及其他费用" show="#{this.data.category.bulkCategory === true}">
                        <Validations>
                            <Validation required="#{true}" message="辅材及其他费用不能为空"/>
                            <Validation validator="#{(rule, otherCosts, callback) => {if(otherCosts &amp;&amp; otherCosts &lt; 0)callback('辅材及其他费用必须大于0')}}"/>
                        </Validations>
                    </Field>
                    <Field name="purDiscountFactor" label="采购折扣系数" show="#{this.data.category.bulkCategory === true}">
                        <Validations>
                            <Validation required="#{true}" message="采购折扣系数不能为空"/>
                            <Validation validator="#{(rule, purDiscountFactor, callback) => {if(purDiscountFactor != null &amp;&amp; (purDiscountFactor &gt; 1 || purDiscountFactor &lt; 0))callback('采购折扣系数大于0且小于等于1')}}" />
                        </Validations>
                    </Field>
                    <Field label="品牌" name="brandBo" readonly="#{this.record.source==='AUTOMATIC'}">
                        <RenderType>
                            <ModelSelect dataCondition="`brandStatus` = 'ENABLED'"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.quotationBasis" show="#{false}"/>
                    <Field name="priceSchemeBO.id" show="#{false}"/>
                    <!-- todo 编辑页面中  如果是撮合协议的期必须满足
                        #{(this.record.statusDict == 'START_USING' || this.record.statusDict == 'STOP_USING')}
                        才存在设置【价格方案】的按钮
                    -->
                    <Field name="priceSchemeBOList" label="价格方案"
                           readonly="#{!(this.record.statusDict == 'START_USING' || this.record.statusDict == 'STOP_USING') &amp;&amp; this.record.source==='CLOUD_BUILD'}"
                           show="#{this.record.agreementBO.type ==='match_making' }">
                        <RenderType>
                            <ModelSelect targetView="b2b_contract_PaymentRelatePriceSchemeCTO_SelectPaymentSchemeDetails"
                                         env="#{{record:this.record}}"
                                         payloadCallback = "#{payloadCallbackSupplementPriceSchemeBO}"/>
                        </RenderType>
                    </Field>
                    <!-- todo 编辑页面中 如果不是撮合协议的都能改变 -->
                    <Field name="priceSchemeBO" label="价格方案"
                           readonly="#{this.record.source==='CLOUD_BUILD'}"
                           show="#{this.record.agreementBO.type !=='match_making'}">
                        <RenderType>
                            <ModelSelect targetView="item_PriceSchemeBO_SingleSelectAgreementPriceScheme"
                                         env="#{{spuId:this.record.spuBO.id,agreementId:this.record.agreementBO.id,agreementType:this.record.agreementBO.type}}"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.supplyPeriod" readonly="#{this.record.source==='CLOUD_BUILD'}">
                        <RenderType>
                            <Number min="0" max="999"/>
                        </RenderType>
                    </Field>
                    <Field name="source" readonly = "true"/>
                    <Field name="outerCode" readonly = "true"/>
                    <Field label="创建人" name="createdBy.username" readonly = "true"/>
                    <Field label="创建时间" name="createdAt" readonly = "true"/>
                </Fields>
                <RecordActions>
                    <Action label="detail" targetView="b2b_contract_AgreementDetailBO_AgreementDetail" authViewKey="详情"/>
                    <!-- targetView="b2b_contract_AgreementDetailBO_AgreementDetailEdit" -->
                    <!-- logicFunc="b2b_contract_AgreementDetailEditFunc"  -->
                    <Action label="edit" show="#{this.record.source==='AUTOMATIC'}"
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailEdit"
                            after="Refresh" authViewKey="编辑"/>
                    <Action label="映射标品" action="#{mapSpu}"
                            show="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO }"
                            openViewType="Dialog"/>
                    <Action label="设置价格方案" action="#{setPriceScheme}"
                            show="#{(this.record.statusDict === 'START_USING' || this.record.statusDict === 'STOP_USING') }"
                            openViewType="Dialog"/>
                    <Action label="查看价格方案" action="#{viewPriceScheme}"/>
                    <Action label="启用"
                            show="#{this.record.statusDict ==='STOP_USING' }"
                            confirm="确认启用么？" logicFunc="b2b_contract_AgreementDetailEnableFunc" after="Refresh" />
                    <Action label="停用"
                            show="#{this.record.statusDict ==='START_USING' }"
                            confirm="确认停用么？" logicFunc="b2b_contract_AgreementDetailDisableFunc" after="Refresh" />
                    <Action label="失效"
                            show="#{this.record.statusDict ==='START_USING' || this.record.statusDict ==='STOP_USING'}"
                            confirm="确认失效么？" logicFunc="b2b_contract_AgreementDetailLoseFunc" after="Refresh" authViewKey="失效"/>
                    <Action label="删除" show="#{this.record.source === 'AUTOMATIC' }"
                            validator="#{validateDelete}"
                            confirm="确认删除么？此操作不可逆，请谨慎操作"
                            logicFunc="b2b_contract_AgreementDetailDeleteFunc" after="Refresh"/>
                </RecordActions>
                <Actions>
                    <Action label="删除" multi="#{true}" action="#{validateBatchDelete}"
                            after="Refresh" />
                    <!--<Action label="映射标品" multi="#{true}" action="#{batchMapSpu}"
                            validator="#{validateBatchMapSpu}" />-->
                    <Action label="设置价格方案" multi="#{true}"
                            action="#{batchSetPriceScheme}" validator="#{validateBatchSetPrice}" />
                    <Action label="新建"
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailCreate"
                            show="#{['enabled','expired','disabled'].includes(getContainerByKey('agreement').data.status)}"
                            env="#{{agreement:getContainerByKey('agreement').data}}"
                            openViewType="Dialog"/>
                </Actions>
            </TableForm>
        </Anchor>









<!--        <Anchor title="协议清单" show="#{!!getContainerByKey('agreement').data.canRelateDetail}">-->
<!--            <Table model="b2b_contract_AgreementDetailBO" title="协议清单"-->
<!--                   fuzzySearchable="#{false}" columnOrder="#{false}"-->
<!--                   dataCondition="agreementBO = ?"-->
<!--                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">-->
<!--                <Fields>-->
<!--                    <Field name="id" show="#{false}"/>-->
<!--                    <Field name="source" show="#{false}"/>-->
<!--                    <Field name="outerAgreementName"/>-->
<!--                    <Field name="spuBO.name" label="关联标品"/>-->
<!--                    <Field name="spuBO.thingName" label="关联物料"/>-->
<!--                    <Field name="categoryBO.path" label="分类"/>-->
<!--                    <Field name="thingMaterial"/>-->
<!--                    <Field name="thingSize"/>-->
<!--                    <Field name="attribute"/>-->
<!--                    <Field name="unit.unitName" label="计量单位"/>-->
<!--                    <Field name="taxRate.taxRateShow" label="税率"/>-->
<!--                    <Field name="supplyInfo.price"/>-->
<!--                    <Field name="supplyInfo.taxPrice"/>-->
<!--                    <Field name="supplyInfo.quotationBasis"/>-->
<!--                    <Field name="priceSchemeBO.name" label="价格方案">-->
<!--                        <RenderType>-->
<!--                            <Action env="#{{priceSchemeId: this.record.priceSchemeBO.id}}"-->
<!--                                    openViewType="Dialog"-->
<!--                                    targetView="item_PriceSchemeBO_PriceSchemeDetail"/>-->
<!--                        </RenderType>-->
<!--                    </Field>-->
<!--                    <Field name="supplyInfo.supplyPeriod">-->
<!--                        <RenderType>-->
<!--                            <Number unit="天"/>-->
<!--                        </RenderType>-->
<!--                    </Field>-->
<!--                    <Field name="statusDict"/>-->
<!--&lt;!&ndash;                    <Field name="inventoryStatus"/>&ndash;&gt;-->
<!--                    <Field name="agreementBO" show="#{false}"/>-->
<!--                </Fields>-->
<!--            </Table>-->
<!--        </Anchor>-->
    </Anchors>


</View>
