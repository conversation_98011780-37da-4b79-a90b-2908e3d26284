<?xml version="1.0" encoding="UTF-8"?>
<View title="编辑协议" forModel="b2b_contract_AgreementBO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Form key="agreement" model="b2b_contract_AgreementBO" dataCondition="id = ?" dataParams="[pageContext.record.id]"
          onFieldChange="#{agreementChange}" onDataLoaded="#{onDataLoaded}">
        <Fields>
            <GroupField title="基础信息">
                <Field name="id" show="#{false}"/>
                <Field name="code" readonly="#{true}"/>
                <Field name="name" readonly="#{this.data.useTemplateMaintain}">
                    <Validations>
                        <Validation required="#{true}" message="协议名称不能为空"/>
                    </Validations>
                    <RenderType>
                        <Input maxLength="#{255}"/>
                    </RenderType>
                </Field>
                <Field name="aliasName" show="#{this.data.type === 'self'}">
                    <RenderType>
                        <Input maxLength="#{255}"/>
                    </RenderType>
                </Field>
                <Field name="jfCompanyBO" readonly="#{this.data.useTemplateMaintain}">
                    <RenderType>
                        <ModelSelect targetView="md_EntityBO_SelectEntity"
                                     modalWidth="#{700}"
                                     env="#{{condition:'entityStatus=\'ENABLED\' and merchantType !=\'SUPPLIER\'',purchase:false}}"/>
                    </RenderType>
                    <Validations>
                        <Validation required="#{true}" message="协议甲方不能为空"/>
                    </Validations>
                </Field>
                <Field name="type" readonly="#{true}"/>
                <Field name="departmentId" show="#{false}" submit="#{false}"/>
                <Field name="departments" readonly="#{!this.data.departmentId || this.data.useTemplateMaintain}" tips="请先选择协议甲方">
                    <Validations>
                        <Validation required="#{true}" message="使用单位不能为空"/>
                    </Validations>
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="6" parentField="department"
                                            columnTitles="#{['一级部门', '二级部门', '三级部门', '四级部门','五级部门','六级部门']}"
                                            dataSource="organization_DepartmentBO_DepartmentTreeProvider"
                                            dataParams="#{{departmentStatusDict:'ENABLED',department:{id:this.data.departmentId},withCurrent:true}}"
                                            model="organization_DepartmentBO" valueField="id" linkSelectMode="#{true}"
                                            labelField="departmentName" modalTitle="选择部门" modalWidth="1000"
                                            searchInputWidth="600"/>
                    </RenderType>
                </Field>
                <Field name="projectTypes" show="#{this.data.type == 'match_making'}" readonly="#{this.data.useTemplateMaintain}">
                    <Validations>
                        <Validation required="#{true}" message="适用项目类型不能为空"/>
                        <Validation validator="#{(rule, types, callback) => {if(getContainerByKey('agreement').data.type != 'virtual' &amp;&amp; types.length &gt; 1)callback('适用项目类型只能选一个')}}"/>
                    </Validations>
                </Field>
                <Field name="projectTypes" show="#{this.data.type == 'self' || this.data.type == 'virtual'}" readonly="#{this.data.useTemplateMaintain}">
                    <Validations>
                        <Validation required="#{true}" message="适用项目类型不能为空"/>
                    </Validations>
                </Field>
                <Field name="category" readonly="#{this.data.useTemplateMaintain}">
                    <Validations>
                        <Validation required="#{true}" message="分类不能为空"/>
                    </Validations>
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="2" leafOnly="#{true}" parentField="parentCategory"
                                            columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                            dataSource="md_CategoryBO_CategoryTreeProvider"
                                            dataParams="#{{categoryStatus:'ENABLED'}}"
                                            model="md_CategoryBO" valueField="id"
                                            labelField="categoryName" modalWidth="#{800}"
                                            modalTitle="选择分类" searchInputWidth="#{400}"/>
                    </RenderType>
                </Field>
                <Field name="yfCompanyBO">
                    <RenderType>
                        <ModelSelect targetView="md_EntityBO_SelectEntity"
                                     modalWidth="#{700}" env="#{{condition:'entityStatus=\'ENABLED\''}}"/>
                    </RenderType>
                    <Validations>
                        <Validation required="#{true}" message="协议乙方不能为空"/>
                    </Validations>
                </Field>
                <Field name="bizType" readonly="#{this.data.useTemplateMaintain}">
                    <RenderType>
                        <Select disabledValues="#{values => filterBizType(values, this.record)}"/>
                    </RenderType>
                    <Validations>
                        <Validation required="#{true}" message="业务类型不能为空"/>
                        <Validation validator="#{(rule, types, callback) => {checkBizType(rule, types, callback, this.record)}}"/>
                    </Validations>
                </Field>
                <Field name="rentPeriod" show="#{this.data.bizType == 'rent'}"/>
                <Field name="coverAreaType">
                    <Validations>
                        <Validation required="#{true}" message="覆盖范围不能为空"/>
                    </Validations>
                    <RenderType>
                        <Radio direction="horizontal"/>
                    </RenderType>
                </Field>
                <Field name="signAt">
                    <Validations>
                        <Validation required="#{true}" message="签约日期不能为空"/>
                    </Validations>
                </Field>
                <Field name="effectiveAt">
                    <Validations>
                        <Validation required="#{true}" message="生效日期不能为空"/>
                    </Validations>
                </Field>
                <Field name="expireAt">
                    <Validations>
                        <Validation required="#{true}" message="终止日期不能为空"/>
                    </Validations>
                    <RenderType>
                        <DatePicker disabledDate="#{(current) => current.isBefore(new Date(),'day')}"/>
                    </RenderType>
                </Field>
                <Field name="isCenterPay"/>
                <Field name="canRelateDetail"/>
                <Field name="contractCanEditPayScheme" />
                <Field name="remark">
                    <RenderType>
                        <Input maxLength="#{150}"/>
                    </RenderType>
                </Field>
                <Field name="attachment">
                    <Validations>
                        <Validation required="#{getContainerByKey('agreement').data.type === 'self'}" message="自营协议附件不可为空"/>
                    </Validations>
                </Field>
                <Field name="useTemplateMaintain" show="#{false}"/>
            </GroupField>

            <GroupField title="审批表单补充信息" show="#{this.data.type === 'self'}">
                <!-- 对接OA后，打开注释 -->
                <Field name="agreementBpmBO.bpmSubjectTitle"/>
                <Field name="agreementBpmBO.supplementAgreement"/>
                <Field name="agreementBpmBO.mainContractName"/>
                <Field name="agreementBpmBO.mainContractCode"/>
                <Field name="agreementBpmBO.innerContractCode"/>
                <Field name="agreementBpmBO.priceCalRule"/>
                <Field name="agreementBpmBO.invoiceTypeBpm"/>
                <Field name="agreementBpmBO.maintenanceRate"/>
                <Field name="agreementBpmBO.performanceBondType"/>
                <Field name="agreementBpmBO.payWay"/>
            </GroupField>

            <GroupField title="金额信息" show="#{this.data.type === 'self'}">
                <Field name="taxAmt">
<!--                    <Validations>-->
<!--                        <Validation required="#{true}" message="含税金额不能为空"/>-->
<!--                    </Validations>-->
                </Field>
                <Field name="taxRateBO">
<!--                    <Validations>-->
<!--                        <Validation required="#{true}" message="税率不能为空"/>-->
<!--                    </Validations>-->
                    <RenderType>
                        <ModelSelect dataFunction="md_PagingTaxRateBOBuiltInFunc"
                                     dataParams="{businessType:{type:'Collection',values:['PURCHASE']},taxRateStatus:'ENABLED'}"/>
                    </RenderType>
                </Field>
                <Field name="noTaxAmt" readonly="#{true}"/>
                <Field name="taxPrc" readonly="#{true}"/>
            </GroupField>

            <GroupField title="补充协议累计金额信息" show="#{this.data.type === 'self' &amp;&amp; this.data.mainAgreement === null}">
                <Field name="supplyTaxAmt" readonly="#{true}"/>
                <Field name="taxRateBO.taxRateShow" label="税率" readonly="#{true}"/>
                <Field name="supplyNoTaxAmt" readonly="#{true}"/>
                <Field name="supplyTaxPrc" readonly="#{true}"/>
            </GroupField>

            <GroupField singleColumn="true">
                <Field name="remarks" colSize='all'>
                    <RenderType>
                        <RichText rows="#{12}"></RichText>
                    </RenderType>
                </Field>
            </GroupField>
        </Fields>
        <Actions>
            <Action label="取消" layout="Footer" action="GoBack"/>
            <Action type="Submit" label="确认" confirm="确认提交？" layout="Footer"
                    logicFunc="b2b_contract_EditAgreementFunc" after="GoBack"/>
        </Actions>
    </Form>
    <TableForm title="定时账期基本设置" show="#{getContainerByKey('agreement').data.type === 'self'}" model="b2b_contract_AgreementRegularReconciliationBO" key="agreementRegularReconciliation"
               lookupFrom="agreement.regularReconciliations">
        <Fields>
            <Field name="day" label="每个月定时对账日期（日）">
                <Validations>
                    <Validation required="#{true}" message="自动对账日期不能为空"/>
                    <Validation validator="#{(rule,day, callback) => {if((day &amp;&amp; (day &gt; 31 || day &lt; 1)) || day ==0) {callback('定时对账日期不能小于1日或超过31日')}}}" message="定时对账日期不能小于1日或超过31日" />

                </Validations>

            </Field>
        </Fields>
    </TableForm>

    <TableForm title="联系信息（非必填）" model="b2b_contract_AgreementContactBO" key="agreementContacts"
               lookupFrom="agreement.contacts">
        <Fields>
            <Field name="id" show="#{false}"/>
            <Field name="person">
                <Validations>
                    <Validation required="#{true}" message="联系人不能为空"/>
                </Validations>
            </Field>
            <Field name="certificateType">
                <Validations>
                    <Validation required="#{false}" message="证件类型不能为空"/>
                </Validations>
            </Field>
            <Field name="certificateNo">
                <Validations>
                    <Validation required="#{false}" message="证件号不能为空"/>
                </Validations>
            </Field>
            <Field name="position">
                <Validations>
                    <Validation required="#{false}" message="职位不能为空"/>
                </Validations>
            </Field>
            <Field name="phone">
                <Validations>
                    <Validation required="#{true}" message="联系方式不能为空"/>
                </Validations>
            </Field>
            <Field name="accreditType" show = "#{getContainerByKey('agreement').data.type === 'self'}">
                <Validations>
                    <Validation required="#{true}" message="电签权限不能为空"/>
                </Validations>
                <RenderType>
                    <!--表单视图单字典为单选，取值结果为字符串-->
                    <Select />
                </RenderType>
            </Field>
            <Field name="districts">
                <Validations>
                    <Validation required="#{false}" message="负责城市不能为空"/>
                </Validations>
                <RenderType>
                    <CascadeModelSelect isLeafField="isLeaf"
                                        depthLimit="4"
                                        columnTitles="#{['大区','省','市','区']}"
                                        dataSource="md_DistrictBO_DistrictTreeProvider"
                                        dataParams="#{{districtStatus:'ENABLED'}}"
                                        parentField="parentDistrict"
                                        model="md_DistrictBO" valueField="id" labelField="districtName"
                                        linkSelectMode="#{true}" modalTitle="选择地址"/>
                </RenderType>
            </Field>
        </Fields>
    </TableForm>
<!--    <TableForm title="付款方案" model="b2b_contract_PaymentSchemeBO" key="agreementPaymentSchemes" show="#{getContainerByKey('agreement').data?.historyVersion ===1 }"-->
<!--               lookupFrom="agreement.paymentSchemes" showAdd="#{false}" showDelete="#{false}"-->
<!--               minDataCount="#{1}">-->
<!--        <Fields>-->
<!--            <Field name="id" show="#{false}"/>-->
<!--            <Field name="title" readonly="#{true}"/>-->
<!--            <Field name="description" readonly="#{true}"/>-->
<!--            <Field name="schemeNodes" show="#{false}"/>-->
<!--        </Fields>-->
<!--        <RecordActions>-->
<!--            <Action label="编辑" action="#{editPaymentScheme}"/>-->
<!--            <Action label="删除" action="#{deletePaymentScheme}"/>-->
<!--        </RecordActions>-->
<!--        <Actions>-->
<!--            <Action label="新建方案" action="#{addPaymentScheme}"/>-->
<!--        </Actions>-->
<!--    </TableForm>-->

    <TableForm title="付款方案（必填）" model="b2b_contract_PaymentSchemeBO" key="agreementPaymentSchemes"
               lookupFrom="agreement.paymentSchemes" showAdd="#{false}" showDelete="#{false}"
               minDataCount="#{1}">
        <Fields>
            <Field name="id"  show="#{false}"/>
            <Field name="code" readonly="#{true}"/>
            <Field name="title" readonly="#{true}"/>
            <Field name="description" readonly="#{true}"/>
            <Field name="schemeNodes" show="#{false}"/>
            <Field name="paymentSchemeTemplateBO.id" show="#{false}"/>
            <Field name="apply" label="是否被应用" readonly="#{true}" show="#{getContainerByKey('agreement').data.type==='match_making'}" tips="应用该付款方案下对应的协议价格,将会被商城计算价格优先使用"/>
        </Fields>
        <RecordActions>
            <Action label="详情"  targetView="b2b_contract_PaymentSchemeBO_PaymentSchemeDetail"  openViewType="Dialog"/>
            <Action label="编辑" action="#{editPaymentScheme}" show="#{!this.record.paymentSchemeTemplateBO?.id}"/>
            <Action label="删除" action="#{deletePaymentScheme}"/>
            <Action label="应用" action="#{applyPaymentScheme}" show="#{getContainerByKey('agreement').data.type==='match_making'}"/>
        </RecordActions>
        <Actions>
            <Action label="新建方案" action="#{addPaymentScheme}"/>
            <Action label="选择方案模版" action="#{selectPaymentSchemeTemplate}"/>
        </Actions>
    </TableForm>

    <TableForm title="#{`应${getContainerByKey('agreement').data.type=='virtual'?'收':'付'}账款方案`}"
               key="payableScheme"  model="b2b_contract_PayableSchemeTO"
               show="#{['virtual','self'].includes(getContainerByKey('agreement').data.type)}"
               lookupFrom="agreement.payableSchemeList" showAdd="#{false}" showDelete="#{false}">
        <Alert message="应收账款方案为管理自营业务中采购商向供应链公司支付货款的账期及金额，所有应收款均在对账单双方确认后开始计算。" show="#{getContainerByKey('agreement').data.type==='virtual'}"/>
        <Alert message="应付账款方案为管理自营业务中供应链公司向供应商支付货款的账期及金额，所有应付款均在对账单双方确认后开始计算。" show="#{getContainerByKey('agreement').data.type==='self'}"/>
        <Fields>
            <Field name="code" label="编码" readonly="#{true}"/>
            <Field name="title" label="节点" readonly="#{true}"/>
            <Field name="content" label="账期" readonly="#{true}"/>
            <Field name="schemeNodes" show="#{false}"/>
            <Field name="mark" readonly="true"/>
            <Field name="payableSchemeTemplateBO" show="#{false}"/>
        </Fields>
        <RecordActions>
            <Action label="编辑" action="#{editPayableScheme}" show="#{!this.record.payableSchemeTemplateBO?.id}"/>
            <Action label="详情"  openViewType="Dialog" targetView="b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeDetail" />
            <Action label="删除" action="#{deletePayableScheme}"/>
        </RecordActions>
        <Actions>
            <Action label="新建方案" action="#{addPayableAmtScheme}"/>
            <Action label="选择方案模版" action="#{selectPayableAmtScheme}" show="#{getContainerByKey('agreement').data.type==='virtual'}"/>
        </Actions>
    </TableForm>

    <TableForm title="覆盖范围" show="#{getContainerByKey('agreement').data.coverAreaType === 'appoint'}"
               model="b2b_contract_AgreementCoverAreaBO" key="coverAreas"
               onFieldChange="#{coverAreaChange}"
               lookupFrom="agreement.coverAreas">
        <Fields>
            <Field name="id" show="#{false}"/>
            <Field name="region" resetFields="districts">
                <RenderType>
                    <ModelSelect targetView="md_DistrictBO_SelectDistrict"
                                 modalWidth="#{700}"
                                 env="#{{condition:'districtStatus=\'ENABLED\' and type =\'big_area\''}}"/>
<!--                    <ModelSelect dataCondition="`districtStatus` = 'ENABLED' AND `type` = 'big_area'"/>-->
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="大区不能为空"/>
                </Validations>
            </Field>
            <Field name="districts" readonly="#{!this.record.region}" tips="请先选择大区">
                <RenderType>
                    <CascadeModelSelect isLeafField="isLeaf"
                                        depthLimit="4"
                                        columnTitles="#{['省','市','区']}"
                                        dataSource="md_DistrictBO_DistrictTreeProvider"
                                        dataParams="#{{districtStatus:'ENABLED',parentDistrict:{id:this.record.region.id}}}"
                                        parentField="parentDistrict"
                                        model="md_DistrictBO" valueField="id" labelField="districtName"
                                        linkSelectMode="#{true}" modalTitle="选择地址"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="省市区不能为空"/>
                </Validations>
            </Field>
            <Field name="limitType">
                <Validations>
                    <Validation required="#{false}" message="框架限制不能为空"/>
                </Validations>
            </Field>
        </Fields>
    </TableForm>
    <TableForm title="经销商（非必填）"
               model="b2b_contract_AgreementDealerBO" key="dealers"
               onFieldChange="#{dealerChange}"
               lookupFrom="agreement.dealers">
        <Alert message="若框架协议出现以下情况需填写此栏，其余情况可不填写： 1、地产类框架协议：签约乙方为品牌方，实际供货方为经销商时需填写具体经销商信息。 2、施工类框架协议：混凝土框架协议中，签约乙方为搅拌站上属母公司，实际供货方及执行合同乙方为**搅拌站时，需填写具体搅拌站信息。"/>
        <Fields>
            <Field name="dealer">
                <RenderType>
                    <ModelSelect targetView="md_EntityBO_SelectEntity"
                                 modalWidth="#{700}"
                                 env="#{{condition:'entityStatus=\'ENABLED\' and merchantType =\'SUPPLIER\''}}"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="经销商不能为空"/>
                </Validations>
            </Field>
            <Field name="region" resetFields="districts">
                <RenderType>
                    <ModelSelect targetView="md_DistrictBO_SelectDistrict"
                                 modalWidth="#{700}"
                                 env="#{{condition:'districtStatus=\'ENABLED\' and type =\'big_area\''}}"/>
<!--                    <ModelSelect dataCondition="`districtStatus` = 'ENABLED' AND `type` = 'big_area'"/>-->
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="大区不能为空"/>
                </Validations>
            </Field>
            <Field name="districts" readonly="#{!this.record.region}" tips="请先选择大区">
                <RenderType>
                    <CascadeModelSelect isLeafField="isLeaf"
                                        depthLimit="4"
                                        columnTitles="#{['省','市','区']}"
                                        dataSource="md_DistrictBO_DistrictTreeProvider"
                                        dataParams="#{{districtStatus:'ENABLED',parentDistrict:{id:this.record.region.id}}}"
                                        parentField="parentDistrict"
                                        model="md_DistrictBO" valueField="id" labelField="districtName"
                                        linkSelectMode="#{true}" modalTitle="选择地址"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="省市区不能为空"/>
                </Validations>
            </Field>
        </Fields>
    </TableForm>
</View>