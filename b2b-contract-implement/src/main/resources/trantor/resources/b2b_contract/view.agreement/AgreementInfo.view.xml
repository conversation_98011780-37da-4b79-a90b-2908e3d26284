<?xml version="1.0" encoding="UTF-8" ?>
<View title="协议详情" forModel="b2b_contract_AgreementBO" type="Detail" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Detail model="b2b_contract_AgreementBO" key="agreement" onDataLoaded="#{onBaseFormLoaded}" show="#{initialFetched}"
            dataFunction="b2b_contract_QueryAgreementInfoFunc" dataParams="{id:env.agreementId ? env.agreementId : pageContext.record.id}">
        <Alert type="error" message="#{getContainerByKey('agreement').data.syncRemark}"
               show="#{getContainerByKey('agreement').data.syncStatus === 'fail'}"/>
        <Fields>
            <GroupField title="基础信息">
                <Field name="id" show="#{false}"/>
                <Field name="code"/>
                <Field name="externalNo"/>
                <Field name="name"/>
                <Field name="aliasName" show="#{this.data.type === 'self'}"/>
                <Field name="projectTypes"/>
                <Field name="jfCompanyBO.entityName" label="协议甲方"/>
                <Field name="departmentStr"/>
                <Field name="status"/>
                <Field name="category.path" label="分类"/>
                <Field name="yfCompanyBO.entityName" label="协议乙方"/>
                <Field name="bizType"/>
                <Field name="optEmployeesStr"/>
                <Field name="optDepartments"  initValue="#{this.data.optDepartments.map(d => d.departmentName).join(',')}">
                    <RenderType>
                        <Text></Text>
                    </RenderType>
                </Field>
                <Field name="maintainCompleted" helpText="#{tips}"/>
                <Field name="rentPeriod"/>
                <Field name="signAt"/>
                <Field name="effectiveAt"/>
                <Field name="expireAt"/>
                <Field name="coverAreaType"/>
                <Field name="isSupply"/>
                <Field name="mainAgreement.id" show="#{false}"/>
                <Field name="mainAgreement.name" label="关联主协议" show="#{this.data.isSupply}">
                    <RenderType>
                        <Action env="#{{agreementId: this.record.mainAgreement.id}}"
                                openViewType="Dialog"
                                targetView="b2b_contract_AgreementBO_AgreementInfo"/>
                    </RenderType>
                </Field>
                <Field name="isSubCompany"/>
                <Field name="relateAgreement.id" show="#{false}"/>
                <Field name="relateAgreement.name" label="关联协议" show="#{this.data.isSubCompany}">
                    <RenderType>
                        <Action env="#{{agreementId: this.record.relateAgreement.id}}"
                                openViewType="Dialog"
                                targetView="b2b_contract_AgreementBO_AgreementInfo"/>
                    </RenderType>
                </Field>
                <Field name="operator.employeeName" label="经办人"/>
                <Field name="department.departmentName" label="经办单位"/>
                <Field name="sourceDict"/>
                <Field name="createdAt"/>
                <Field name="type"/>
                <Field name="isCenterPay"/>
                <Field name="canRelateDetail"/>
                <Field name="contractCanEditPayScheme"/>
                <!-- 计价模式（自营销售使用虚拟框架协议，type='virtual'）（自营采购使用自营采购框架协议，type='sell'）（撮合使用撮合框架协议，type='match_making'） -->
                <Field name="priceMode" show="#{this.data.type === 'self'}"
                       tips="（1）含税模式：根据含税价和税率计算不含税价；\n（2）不含税模式：根据不含税价和税率计算含税价。">
                    <Validations>
                        <Validation required="#{true}" message="计价模式不能为空"/>
                    </Validations>
                </Field>
                <Field name="remark"/>
                <Field name="attachment"/>
                <Field name="syncStatus" show="#{false}"/>
                <Field name="syncRemark" show="#{false}"/>
            </GroupField>

            <GroupField title="审批表单补充信息" show="#{this.data.type === 'self'}">
            <!-- 对接OA后，打开注释 -->
                <Field name="agreementBpmBO.bpmSubjectTitle"/>
                <Field name="agreementBpmBO.supplementAgreement"/>
                <Field name="agreementBpmBO.mainContractName"/>
                <Field name="agreementBpmBO.mainContractCode"/>
                <Field name="agreementBpmBO.innerContractCode"/>
                <Field name="agreementBpmBO.priceCalRule"/>
                <Field name="agreementBpmBO.invoiceTypeBpm"/>
                <Field name="agreementBpmBO.maintenanceRate"/>
                <Field name="agreementBpmBO.performanceBondType"/>
                <Field name="agreementBpmBO.payWay"/>
            </GroupField>

            <GroupField singleColumn="true">
                <Field name="remarks" colSize='all' >
                    <RenderType>
                        <RichText rows="#{12}"></RichText>
                    </RenderType>
                </Field>
            </GroupField>
            <GroupField title="金额信息" show="#{this.data.type === 'self'}" >
                <Field name="taxAmt" tips="协议含税金额=主协议含税金额+补充协议累计含税金额"/>
                <Field name="taxRateBO.taxRateShow" label="税率"/>
                <Field name="noTaxAmt" tips="协议不含税金额=主协议不含税金额+补充协议累计不含税金额"/>
                <Field name="taxPrc" tips="协议税额=主协议税额+补充协议累计税额"/>
            </GroupField>

            <GroupField title="补充协议累计金额信息" show="#{this.data.type === 'self' &amp;&amp; this.data.mainAgreement == null}">
                <Field name="supplyTaxAmt" readonly="#{true}"/>
                <Field name="taxRateBO.taxRateShow" label="税率" readonly="#{true}"/>
                <Field name="supplyNoTaxAmt" readonly="#{true}"/>
                <Field name="supplyTaxPrc" readonly="#{true}"/>
            </GroupField>

            <GroupField title="招标信息" show="#{this.data.sourceDict === 'sync'}">
                <Field name="bidTaskCode"/>
                <Field name="bidTaskName"/>
                <Field name="jcBidInfo.categoryName"/>
            </GroupField>
        </Fields>
        <Actions>
            <Action label="协议签约" action="#{contractJumpout}" />
            <Action label="同步" confirm="确认同步？" after="Refresh"
                    show="#{this.data.sourceDict !== 'manual' &amp;&amp; this.data.syncStatus === 'fail' "
                    logicFlow="b2b_contract_SinglePullExternalAgreementFlow"/>
            <Action label="督办" confirm="确认督办？" after="Refresh"
                    logicFunc="b2b_contract_SendTodoMessageFunc"/>
            <Action label="批量新建清单价格方案"
                    env="#{{agreementId:this.data.id, source: 'OPE'}}"
                    targetView="b2b_contract_AgreementBO_BatchCreatePriceSchemeForAgreement" />
            <Action type="Submit" label="调整适用范围"
                    targetView="b2b_contract_AgreementBO_AgreementCoverAreaEdit"
                    env="#{{agreementId:this.data.id}}"
                    show="#{['enabled','disabled'].includes(this.data.status) "/>

            <!-- todo 新增的维护协议 放弃sourceDict来源状态维护 -->
            <Action type="Submit" label="维护协议"
                    action="#{extendInfoEditOrEdit}"
                    show="#{!['enabled_audit','disabled_audit'].includes(this.record.status) "/>

            <!--
            <Action type="Submit" label="维护协议信息"
                    targetView="b2b_contract_AgreementBO_AgreementExtendInfoEdit"
                    env="#{{agreementId:this.data.id}}"
                    show="#{this.data.sourceDict !== 'manual'}"/>
            -->
            <Action label="通过" confirm="是否确认审批通过？" after="Refresh"
                    show="#{(this.data.status ==='enabled_audit' || this.data.status == 'disabled_audit') "
                    logicFunc="b2b_contract_AbleApproveAgreementFunc"  />
            <!--审批驳回-->
            <Action label="驳回" confirm="是否确认审批驳回？" after="Refresh"
                    show="#{(this.data.status ==='enabled_audit' || this.data.status == 'disabled_audit') "
                    logicFunc="b2b_contract_AbleRejectAgreementFunc" />
        </Actions>
    </Detail>

    <Anchors>
        <Anchor title="定时账期基本设置" show="getContainerByKey('agreement').data.type == 'self'">
            <Table model="b2b_contract_AgreementRegularReconciliationBO" title="定时账期基本设置"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="agreementBO = ?"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="day" label="每个月定时对账日期（日）"/>
                </Fields>
            </Table>
        </Anchor>
        <Anchor title="联系信息">
            <Table model="b2b_contract_AgreementContactBO" title="联系信息"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="agreementBO = ?"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="person"/>
                    <Field name="certificateType"/>
                    <Field name="certificateNo"/>
                    <Field name="position"/>
                    <Field name="phone"/>
                    <Field name="accreditType" show = "#{getContainerByKey('agreement').data.type === 'self'}"/>
                    <Field name="districtStr"/>
                </Fields>
            </Table>
        </Anchor>
        <Anchor title="付款方案" show="">
<!--            <Table model="b2b_contract_PaymentSchemeBO" title="付款方案"-->
<!--                   fuzzySearchable="#{false}" columnOrder="#{false}" key="agreementPaymentSchemes"-->
<!--                   dataCondition="agreementBO = ?"  show="#{getContainerByKey('agreement').data?.historyVersion === 1}"-->
<!--                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">-->
<!--                <Fields>-->
<!--                    <Field name="title"/>-->
<!--                    <Field name="description"/>-->
<!--                </Fields>-->
<!--            </Table>-->
            <Table title="付款方案" model="b2b_contract_PaymentSchemeBO" key="agreementPaymentSchemeV2"
                       dataCondition="agreementBO = ?"
                       dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="id"  show="#{false}"/>
                    <Field name="code" readonly="#{true}"/>
                    <Field name="title" readonly="#{true}"/>
                    <Field name="description" readonly="#{true}"/>
                    <Field name="schemeNodes" show="#{false}"/>
                    <Field name="paymentSchemeTemplateBO.id" show="#{false}"/>
                    <Field name="apply" label="是否被应用" readonly="#{true}" show="#{getContainerByKey('agreement').data?.type==='match_making'}" tips="应用该付款方案下对应的协议价格,将会被商城计算价格优先使用"/>
                </Fields>
                <RecordActions>
                    <Action label="详情"  targetView="b2b_contract_PaymentSchemeBO_PaymentSchemeDetail"  openViewType="Dialog"/>
                </RecordActions>
            </Table>
        </Anchor>
        <Anchor title="#{`应${getContainerByKey('agreement').data.type=='virtual'?'收':'付'}账款方案`}" show="#{['virtual','self'].includes(getContainerByKey('agreement').data.type)}">
            <Table model="b2b_contract_PayableSchemeTO" title="#{`应${getContainerByKey('agreement').data.type=='virtual'?'收':'付'}账款方案`}"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   show="#{['virtual','self'].includes(getContainerByKey('agreement').data.type)}"
                   lookupFrom="agreement.payableSchemeList">
                <Fields>
                    <Field name="title" label="节点"/>
                    <Field name="content" label="账期"/>
                    <Field name="mark"/>
                </Fields>
                <RecordActions>
                    <Action label="详情"  openViewType="Dialog" targetView="b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeDetail" />
                </RecordActions>
            </Table>
        </Anchor>
        <Anchor title="覆盖范围" show="#{getContainerByKey('agreement').data.coverAreaType === 'appoint'}">
            <Table model="b2b_contract_AgreementCoverAreaBO" title="覆盖范围"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="agreementBO = ?"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="region.districtName" label="大区"/>
                    <Field name="districtStr"/>
                    <Field name="limitType"/>
                </Fields>
            </Table>
        </Anchor>
        <Anchor title="经销商">
            <Table model="b2b_contract_AgreementDealerBO" title="经销商"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="agreementBO = ?"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="dealer" label="经销商"/>
                    <Field name="region.districtName" label="大区"/>
                    <Field name="districtStr"/>
                </Fields>
            </Table>
        </Anchor>
        <Anchor title="协议清单" show="#{!!getContainerByKey('agreement').data.canRelateDetail &amp;&amp; !getContainerByKey('agreement').data.isSupply}">
            <Table model="b2b_contract_AgreementDetailBO" title="协议清单" key="agreementDetailList"
                   fuzzySearchable="#{false}" columnOrder="#{false}" virtual="#{true}"
                   customDerivedData="false"
                   dataFunction="b2b_contract_PagingWorkspaceAgreementDetailFunc"
                   showEditableIcon="#{true}" onUpdate = "#{changeSpuBO}" advanced="#{true}"
                   dataParams="{fromSite:{value:'agreementView'},agreementBO:{id:{value:env.agreementId ? env.agreementId : pageContext.record.id}}}">
                <Search>
                    <Fields>
                        <Field name="statusDict"/>
                        <Field name="spuBO.spuCode" label="标品编码"/>
                        <Field name="attribute" label="属性"/>
                    </Fields>
                </Search>
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="source" show="#{false}"/>
                    <Field name="agreementBO" show="#{false}"/>
                    <Field name="agreementBO.type" show="#{false}"/>
                    <Field label="供应商" name="saleEntityBO.entityName"/>
                    <Field name="syncInfo.mateName" label = "原协议清单项">
                        <RenderType>
                            <Action targetView="b2b_contract_AgreementDetailBO_AgreementDetail"
                                    openViewType="Dialog"
                                    env="#{{agreementDetailId: this.record.id}}"/>
                        </RenderType>
                    </Field>
                    <Field name="statusDict"/>
<!--                    <Field label="关联标品" name="spuBO" editable="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO}">-->
<!--                        <RenderType>-->
<!--                            <ModelSelect targetView="item_SpuBO_SingleSelectAgreementSpu"-->
<!--                                         env="#{{agreementId:this.record.agreementBO.id}}"/>-->
<!--                            <MainField showPanel="#{false}" onlyText="#{true}" editable="#{false}"/>                        </RenderType>-->
<!--                    </Field>-->
                    <Field label="关联标品" name="spuBO" columnSize="large" initValue="#{this.record?.spuBO ?? '-'}" editable="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO}">
                        <RenderType>
                            <ModelSelect modalWidth="1000" modalTitle="选择关联标品" model="item_SpuBO" dataFunction="b2b_contract_PagingAgreementSpuFunc"
                                         dataParams="#{{id:{value:this.record.agreementBO.id}}}" selection="radio"
                                         fuzzySearchable="#{false}">
                                <!--        <Alert type="normal" message="#{env.alertMsg}"/>-->
                                <Search>
                                    <Fields>
                                        <Field name="spuCode" label="标品编码"/>
                                        <Field name="name" label="标品名称"/>
                                        <Field name="category">
                                            <RenderType>
                                                <CascadeModelSelect isLeafField="isLeaf" depthLimit="4" parentField="parentCategory"
                                                                    columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                                                    dataSource="md_CategoryBO_CategoryTreeProvider"
                                                                    dataParams="#{{categoryStatus:'ENABLED'}}"
                                                                    model="md_CategoryBO" valueField="id"
                                                                    labelField="categoryName" modalWidth="#{800}"
                                                                    modalTitle="选择分类" searchInputWidth="#{400}"/>
                                            </RenderType>
                                        </Field>
                                        <Field name="thingMaterial"/>
                                        <Field name="thingSize"/>
                                    </Fields>
                                </Search>
                                <Fields>
                                    <Field name="id" show="#{false}"/>
                                    <Field label="标品编码" name="spuCode"/>
                                    <Field label="标品名称" name="name"/>
                                    <Field label="关联物料" name="thing.thingName"/>
                                    <Field label="分类" name="category.path"/>
                                    <!--            <Field name="thingMaterial"/>-->
                                    <!--            <Field name="thingSize"/>-->
                                    <Field label="计量单位" name="thing.unit.unitName"/>
                                    <Field label="属性" name="attributes" columnSize="large">
                                        <RenderType>
                                            <Text format="#{formatAttributes}"/>
                                        </RenderType>
                                    </Field>
                                </Fields>
                            </ModelSelect>
                            <MainField showPanel="#{false}"  onlyText="#{true}" editable="#{false}"/>
                        </RenderType>
                    </Field>
                    <Field label="标品编码" name="spuBO.spuCode"/>
                    <Field name="categoryBO.path" label="分类"/>
                    <Field name="spuBO.thing" show="#{false}"/>
                    <Field name="spuBO.thingName" label="关联物料">
                        <RenderType>
                            <Action targetView="md_thingBO_B2BThingDetail"
                                    openViewType="Dialog"
                                    env="#{{thingId: this.record.spuBO.thing.id}}"/>
                        </RenderType>
                    </Field>

<!--                    <Field name="thingMaterial"/>-->
<!--                    <Field name="thingSize"/>-->
                    <Field name="spuBO.attributes" label="属性">
                        <RenderType>
                            <Text format="#{formatAttributes}"/>
                        </RenderType>
                    </Field>
                    <Field name="taxRate.taxRateShow" label="税率"/>
                    <Field name="supplyInfo.price">
                        <RenderType>
                            <Number format="0.[0000]"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.taxPrice">
                        <RenderType>
                            <Number format="0.[0000]"/>
                        </RenderType>
                    </Field>
                    <Field name="unit.unitName" label="计量单位"/>
                    <Field name="brands">
                        <RenderType>
                            <MainField onlyText="#{true}"/>
                        </RenderType>
                    </Field>
                    <Field name="spuBO.rawMaterialContent" label="铜含量（kg/m）" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="copperBasicPrice" label="参考铜基价" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="yanmiCopperPrice" label="延米铜价" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="otherCosts" label="辅材及其他费用" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="purDiscountFactor" label="采购折扣系数" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="supplyInfo.quotationBasis" show="#{false}"/>
                    <Field name="priceSchemeBO.id" show="#{false}"/>
                    <Field name="priceSchemeBO.name" label="价格方案">
                        <RenderType>
                            <Action env="#{{priceSchemeId: this.record.priceSchemeBO.id}}"
                                    openViewType="Dialog"
                                    targetView="item_PriceSchemeBO_PriceSchemeDetail"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.supplyPeriod" />
                    <Field name="source" />
                    <Field name="outerCode"/>
                    <Field label="创建人" name="createdBy.username"/>
                    <Field label="创建时间" name="createdAt"/>
<!--                    <Field name="supplyInfo.supplyPeriod">-->
<!--                        <RenderType>-->
<!--                            <Number unit="天"/>-->
<!--                        </RenderType>-->
<!--                    </Field>-->
<!--                    <Field name="statusDict"/>-->
                </Fields>
                <RecordActions>
                    <Action label="detail" targetView="b2b_contract_AgreementDetailBO_AgreementDetail" authViewKey="详情"/>
                    <Action label="edit" show="#{this.record.source==='AUTOMATIC' "
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailEdit" authViewKey="编辑"/>
                    <Action label="映射标品" action="#{mapSpu}"
                            show="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO  "
                            openViewType="Dialog"/>
                    <Action label="设置价格方案" action="#{setPriceScheme}"
                            show="#{(this.record.statusDict === 'START_USING' || this.record.statusDict === 'STOP_USING')  "
                            openViewType="Dialog"/>
                    <Action label="查看价格方案" action="#{viewPriceScheme}"/>
                    <Action label="启用"
                            show="#{this.record.statusDict ==='STOP_USING'  "
                            confirm="确认启用么？" logicFunc="b2b_contract_AgreementDetailEnableFunc" after="Refresh" />
                    <Action label="停用"
                            show="#{this.record.statusDict ==='START_USING'  "
                            confirm="确认停用么？" logicFunc="b2b_contract_AgreementDetailDisableFunc" after="Refresh" />
                    <Action label="失效"
                            show="#{(this.record.statusDict ==='START_USING' || this.record.statusDict ==='STOP_USING') "
                            confirm="确认失效么？" logicFunc="b2b_contract_AgreementDetailLoseFunc" after="Refresh" authViewKey="失效"/>
                    <Action label="删除" show="#{this.record.source === 'AUTOMATIC'  "
                            confirm="确认删除么？此操作不可逆，请谨慎操作"
                            logicFunc="b2b_contract_AgreementDetailDeleteFunc" after="Refresh"/>
                </RecordActions>
                <Actions>
                    <Action label="删除" multi="#{true}" confirm="确认删除么？此操作不可逆，请谨慎操作"
                            logicFunc="b2b_contract_AgreementDetailBatchDeleteFunc"
                            after="Refresh" validator="#{validateBatchDelete}" />
                    <!--<Action label="映射标品" multi="#{true}" action="#{batchMapSpu}"
                            validator="#{validateBatchMapSpu}" />-->
                    <Action label="设置价格方案" multi="#{true}"
                            action="#{batchSetPriceScheme}" validator="#{validateBatchSetPrice}" />
                    <Action label="新建" type="Submit"
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailCreate"
                            show="#{['enabled','expired','disabled'].includes(getContainerByKey('agreement').data.status) "
                            env="#{{agreement:getContainerByKey('agreement').data}}"
                            openViewType="Dialog"/>
                </Actions>
            </Table>
        </Anchor>

        <Anchor title="协议清单" show="#{!!getContainerByKey('agreement').data.isSupply}">
            <Table model="b2b_contract_AgreementDetailBO" title="协议清单" key="supplementAgreementDetailList"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   customDerivedData="false"
                   dataFunction="b2b_contract_PagingWorkspaceAgreementDetailFunc"
                   dataParams="{fromSite:{value:'agreementView'},supplementAgreementBO:{id:{value:env.agreementId ? env.agreementId : pageContext.record.id}}}">
                <Search>
                    <Fields>
                        <Field name="statusDict"/>
                        <Field name="spuBO.spuCode" label="标品编码"/>
                        <Field name="attribute" label="属性"/>
                    </Fields>
                </Search>
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="source" show="#{false}"/>
                    <Field name="agreementBO" show="#{false}"/>
                    <Field name="agreementBO.type" show="#{false}"/>
                    <Field label="供应商" name="saleEntityBO.entityName"/>
                    <Field name="syncInfo.mateName" label = "原协议清单项">
                        <RenderType>
                            <Action targetView="b2b_contract_AgreementDetailBO_AgreementDetail"
                                    openViewType="Dialog"
                                    env="#{{agreementDetailId: this.record.id}}"/>
                        </RenderType>
                    </Field>
                    <Field name="statusDict"/>
                    <Field name="spuBO.name" label="关联标品">
                        <RenderType>
                            <Action targetView="item_SpuBO_SpuDetail"
                                    openViewType="Dialog"
                                    env="#{{spuId: this.record.spuBO.id}}"/>
                        </RenderType>
                    </Field>
                    <Field label="标品编码" name="spuBO.spuCode"/>
                    <Field name="categoryBO.path" label="分类"/>
                    <Field name="spuBO.thing" show="#{false}"/>
                    <Field name="spuBO.thingName" label="关联物料">
                        <RenderType>
                            <Action targetView="md_thingBO_B2BThingDetail"
                                    openViewType="Dialog"
                                    env="#{{thingId: this.record.spuBO.thing.id}}"/>
                        </RenderType>
                    </Field>

                    <!--                    <Field name="thingMaterial"/>-->
                    <!--                    <Field name="thingSize"/>-->
                    <Field name="spuBO.attributes" label="属性">
                        <RenderType>
                            <Text format="#{formatAttributes}"/>
                        </RenderType>
                    </Field>
                    <Field name="taxRate.taxRateShow" label="税率"/>
                    <Field name="supplyInfo.price">
                        <RenderType>
                            <Number format="0.[0000]"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.taxPrice">
                        <RenderType>
                            <Number format="0.[0000]"/>
                        </RenderType>
                    </Field>
                    <Field name="unit.unitName" label="计量单位"/>
                    <Field name="spuBO.rawMaterialContent" label="铜含量（kg/m）" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="copperBasicPrice" label="参考铜基价" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="yanmiCopperPrice" label="延米铜价" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="otherCosts" label="辅材及其他费用" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="purDiscountFactor" label="采购折扣系数" show="#{this.data.category.bulkCategory === true}"/>
                    <Field name="brands">
                        <RenderType>
                            <MainField onlyText="#{true}"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.quotationBasis" show="#{false}"/>
                    <Field name="priceSchemeBO.id" show="#{false}"/>
                    <Field name="priceSchemeBO.name" label="价格方案">
                        <RenderType>
                            <Action env="#{{priceSchemeId: this.record.priceSchemeBO.id}}"
                                    openViewType="Dialog"
                                    targetView="item_PriceSchemeBO_PriceSchemeDetail"/>
                        </RenderType>
                    </Field>
                    <Field name="supplyInfo.supplyPeriod" />
                    <Field name="source" />
                    <Field name="outerCode"/>
                    <Field label="创建人" name="createdBy.username"/>
                    <Field label="创建时间" name="createdAt"/>
                    <!--                    <Field name="supplyInfo.supplyPeriod">-->
                    <!--                        <RenderType>-->
                    <!--                            <Number unit="天"/>-->
                    <!--                        </RenderType>-->
                    <!--                    </Field>-->
                    <!--                    <Field name="statusDict"/>-->
                </Fields>
                <RecordActions>
                    <Action label="detail" targetView="b2b_contract_AgreementDetailBO_AgreementDetail" authViewKey="详情"/>
                    <Action label="edit" show="#{this.record.source==='AUTOMATIC'  "
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailEdit" authViewKey="编辑"/>
                    <Action label="映射标品" action="#{subMapSpu}"
                            show="#{this.record.statusDict !== 'LOSE_EFFICACY' &amp;&amp; this.record.source === 'CLOUD_BUILD' &amp;&amp; this.record.agreementBO   "
                            openViewType="Dialog"/>
                    <Action label="设置价格方案" action="#{subSetPriceScheme}"
                            show="#{(this.record.statusDict === 'START_USING' || this.record.statusDict === 'STOP_USING')   "
                            openViewType="Dialog"/>
                    <Action label="查看价格方案" action="#{viewPriceScheme}"/>
                    <Action label="启用"
                            show="#{this.record.statusDict ==='STOP_USING' "
                            confirm="确认启用么？" logicFunc="b2b_contract_AgreementDetailEnableFunc" after="Refresh" />
                    <Action label="停用"
                            show="#{this.record.statusDict ==='START_USING'   "
                            confirm="确认停用么？" logicFunc="b2b_contract_AgreementDetailDisableFunc" after="Refresh" />
                    <Action label="失效"
                            show="#{(this.record.statusDict ==='START_USING' || this.record.statusDict ==='STOP_USING')  "
                            confirm="确认失效么？" logicFunc="b2b_contract_AgreementDetailLoseFunc" after="Refresh" authViewKey="失效"/>
                    <Action label="删除" show="#{this.record.source === 'AUTOMATIC'   "
                            confirm="确认删除么？此操作不可逆，请谨慎操作"
                            logicFunc="b2b_contract_AgreementDetailDeleteFunc" after="Refresh"/>
                </RecordActions>
                <Actions>
                    <Action label="删除" multi="#{true}" confirm="确认删除么？此操作不可逆，请谨慎操作"
                            logicFunc="b2b_contract_AgreementDetailBatchDeleteFunc"
                            after="Refresh" validator="#{validateBatchDelete}" />
                    <!--<Action label="映射标品" multi="#{true}" action="#{subBatchMapSpu}"
                            validator="#{validateBatchMapSpu}" />-->
                    <Action label="设置价格方案" multi="#{true}"
                            action="#{subBatchSetPriceScheme}" validator="#{validateBatchSetPrice}" />
                    <Action label="新建" type="Submit"
                            targetView="b2b_contract_AgreementDetailBO_AgreementDetailCreate"
                            show="#{['enabled','expired','disabled'].includes(getContainerByKey('agreement').data.status)  "
                            env="#{{agreement:getContainerByKey('agreement').data}}"
                            openViewType="Dialog"/>
                </Actions>
            </Table>
        </Anchor>

        <Anchor title="补充协议" show="#{!getContainerByKey('agreement').data.isSupply}">
            <Table model="b2b_contract_AgreementBO" title="补充协议"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataCondition="mainAgreement = ? AND isSupply = true"
                   dataParams="[env.agreementId ? env.agreementId : pageContext.record.id]">
                <Fields>
                    <Field name="id" show="#{false}"/>
                    <Field name="code">
                        <RenderType>
                            <Action env="#{{agreementId: this.record.id}}"
                                    openViewType="Dialog"
                                    targetView="b2b_contract_AgreementBO_AgreementInfo"/>
                        </RenderType>
                    </Field>
                    <Field name="externalNo"/>
                    <Field name="name"/>
                    <Field name="effectiveAt"/>
                    <Field name="expireAt"/>
                    <Field name="signAt"/>
                    <Field name="status"/>
                    <Field name="operator"/>
                    <Field name="createdAt"/>
                </Fields>
                <RecordActions>
                    <Action label="详情" openViewType="Dialog" openViewSize="l"
                            targetView="b2b_contract_AgreementBO_AgreementInfo"/>
                </RecordActions>
            </Table>
        </Anchor>
        <Anchor title="关联合同"
                show="#{!!getContainerByKey('agreement').data.type &amp;&amp; getContainerByKey('agreement').data.type !== 'self'}">
            <Table model="b2b_contract_YzContractBO" title="关联合同"
                   fuzzySearchable="#{false}" columnOrder="#{false}"
                   dataFunction="b2b_contract_PagingYzContractFunc"
                   dataParams="{isSupply:{value:false},agreementBO:{id:{value:env.agreementId ? env.agreementId : pageContext.record.id}}}">
                <Fields>
                    <Field name="contractCode"/>
                    <Field name="externalCode" show="#{false}"/>
                    <Field name="contractName"/>
                    <Field name="contractTypeDict"/>
                    <Field name="contractAmt"/>
                    <Field name="signAt"/>
                    <Field name="contractStatusDict"/>
                    <Field name="isSupply"/>
                    <Field name="subContracts">
                        <RenderType>
                            <ModelInfo>
                                <Fields>
                                    <Field name="contractCode"/>
                                    <Field name="externalCode" show="#{false}"/>
                                    <Field name="contractName"/>
                                    <Field name="contractTypeDict"/>
                                    <Field name="contractAmt"/>
                                    <Field name="signAt"/>
                                    <Field name="contractStatusDict"/>
                                    <Field name="isSupply"/>
                                </Fields>
                            </ModelInfo>
                        </RenderType>
                    </Field>
                </Fields>
                <RecordActions>
                    <Action label="详情" targetView="contract_ContractBO_yzContractInfo"
                            env="#{{ url: this.record.yzAdminDetailUrl }}"/>
                </RecordActions>
            </Table>
        </Anchor>
    </Anchors>
</View>
