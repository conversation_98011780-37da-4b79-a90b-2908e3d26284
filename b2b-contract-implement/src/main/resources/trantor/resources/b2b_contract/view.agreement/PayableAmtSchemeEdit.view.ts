import {Controller} from 'nusi-sdk'

export default class extends Controller {

    // 回到上一个页面
    goBackWithRecord = async ({record}) => {
        console.log("goBackWithRecord ", record)
        this.goBack({
            withPayload: true,
            payload: record
        })
    }

    // 校验方案
    validateScheme = ({data}) => {
        console.log("@@@@@@data", data)
        // 校验节点+起来=100

         let percent = data.schemeNodes.map((detail) => detail.payableRatio).reduce((prev, curr) => prev + curr, 0);
         if (percent != 100) {
            return "应收比例合计不为100%，请调整后确认！";
         }

        return true;
    }


}