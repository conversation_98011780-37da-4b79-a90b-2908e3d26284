import { Controller,state,utils,Toast,triggerLogicFunction,showMessage } from 'nusi-sdk'

export default class extends Controller {

    @state
    showPay = false;

    // 页面加载
    onDataLoaded = (data) => {
        const container = this.getContainerByKey('agreement');
        setTimeout(async () => {
            if (data.jfCompanyBO) {
                const dsResult = await this.getDataSource('DataStore', {
                    modelKey: 'md_CompanyBO',
                    fields: ['id', 'orgId'],
                    condition: {
                        expression: 'id = ?',
                        params: [data.jfCompanyBO.id]
                    }
                });
                container.updateData(
                    {
                        departmentId: dsResult.data[0].orgId
                    }
                )
            }
        }, 500)
    }


    // 协议变更
    agreementChange = async ({ value, fieldName, record }) => {
        console.log('===== agreementChange ', value)
        let agreementContainer = this.getContainerByKey('agreement');
        if (fieldName == 'jfCompanyBO' || fieldName == 'yfCompanyBO') {
            // 含税价格
            const jfCompanyBO = this._.get(record, 'jfCompanyBO');
            // 税率
            const yfCompanyBO = this._.get(record, 'yfCompanyBO');
            if (!!jfCompanyBO && !!yfCompanyBO) {
                const result = await this.triggerLogicFunction('b2b_contract_CalcAgreementTypeFunc', [record]);
                agreementContainer.updateData(
                    {
                        type: result.type
                    }
                )
                if (result.type === "self" || result.type === "virtual") {
                    this.showPay = true
                }
            }
            if (fieldName == 'jfCompanyBO') {
                const dsResult = await this.getDataSource('DataStore', {
                    modelKey: 'md_CompanyBO',
                    fields: ['id', 'orgId'],
                    condition: {
                        expression: 'id = ?',
                        params: [jfCompanyBO.id]
                    }
                });
                agreementContainer.updateData(
                    {
                        departmentId: null,
                        departments: null
                    }
                )
                setTimeout(() => agreementContainer.updateData({ departmentId: dsResult.data[0].orgId }), 500)
            }
        } else if (fieldName == 'taxRateBO' || fieldName === 'taxAmt') {
            // 含税价格
            let taxAmt = this._.get(record, 'taxAmt');
            // 税率
            let taxRate = this._.get(record, 'taxRateBO.taxRate');
            if ((!!taxAmt || taxAmt == 0) && !!taxRate) {
                // 不含税价格
                let noTaxAmt = parseFloat((taxAmt / (1 + taxRate / 100)).toFixed(4));
                let taxPrc = parseFloat((taxAmt - noTaxAmt).toFixed(4));
                agreementContainer.updateData(
                    {
                        noTaxAmt: noTaxAmt,
                        taxPrc: taxPrc
                    }
                )
            }
        } else if (fieldName == 'projectTypes') {
            if (agreementContainer.data.type != 'virtual' && agreementContainer.data.projectTypes.includes("construction") && agreementContainer.data.bizType == 'project_material') {
                agreementContainer.updateData({
                    bizType: null
                });
            }
        }
    }

    coverAreaChange = ({ fieldName, value, index, record }) => {
        console.log(fieldName, value, index)
        const table = this.getContainerByKey('coverAreas')
        if (fieldName === 'region') {
            table.updateData({ ...record, region: undefined }, index)
            setTimeout(() => table.updateData({ ...record, region: value }, index), 500)
        }
    }

    // 经销商变更
    dealerChange = ({ fieldName, value, index, record }) => {
        console.log(fieldName, value, index)
        const table = this.getContainerByKey('dealers')
        if (fieldName === 'region') {
            table.updateData({ ...record, region: undefined }, index)
            setTimeout(() => table.updateData({ ...record, region: value }, index),500)
        }
    }

    // 新增付款方案
    addPaymentScheme = () => {
        this.openView('b2b_contract_PaymentSchemeBO_PaymentSchemeCreate', {
            openViewType: 'Dialog',
            // openViewSize: "xs",
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')
                const schemes = schemeContainer.data

                console.log("=== addPaymentScheme, payload=", payload)
                let schemeTitle = payload.schemeNodes.map(node => translateNodeDict(node.nodeDict) + node.rate + '%').join(" | ");
                schemeContainer.updateData([...schemes,
                {
                    name: schemeTitle,
                    title: schemeTitle,
                    description: payload.description,
                    schemeNodes: payload.schemeNodes,
                    apply: false,
                }
                ])

                this.applyFirstPayment()
            }
        })
    }

    //  选择方案模板
    selectPaymentSchemeTemplate = () => {
        const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')

        let paymentList = schemeContainer.data
            .filter(it=>it['paymentSchemeTemplateBO'] != null)
            .map((payment) => payment['paymentSchemeTemplateBO'])

        console.log('paymentList',paymentList)

        this.openView('b2b_contract_PaymentSchemeTemplateBO_PaymentSchemeSelectListV2', {
            openViewType: 'Dialog',
            // record:  paymentList,
            payloadCallback: (payload) => {
                console.log("=== addPaymentScheme, payload=", payload)

                //  拿出不存在paymentSchemeTemplateBO的数据
                let historyPaymentList = schemeContainer.data.filter(item => item['paymentSchemeTemplateBO']?.id == null);
                let newPaymentList = schemeContainer.data.filter(item => item['paymentSchemeTemplateBO']?.id != null);

                let newPaymentListMap =  newPaymentList.reduce((map, item) => {
                    let templateId = item['paymentSchemeTemplateBO']['id']
                    map[templateId] = item
                    return map
                },{})

                let res = [...schemeContainer.data]

                for (const it of payload.record) {
                    if (!newPaymentListMap[it.id]){
                        let copy  = {...it};
                        copy['paymentSchemeTemplateBO'] = it
                        copy['id'] = null
                        copy['apply'] = false
                        res.push(copy)
                    }
                }

                schemeContainer.updateData(res)

                this.applyFirstPayment()

            }
        })
    }

    // 编辑付款方案
    editPaymentScheme = ({ record }) => {
        const rowId = record._TEMPID
        this.openView('b2b_contract_PaymentSchemeBO_PaymentSchemeEdit', {
            openViewType: 'Dialog',
            // openViewSize: "xs",
            env: {
                scheme: record
            },
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')
                const schemes = schemeContainer.data

                console.log("=== editPaymentScheme, payload=", payload)
                let schemeTitle = payload.schemeNodes.map(node => translateNodeDict(node.nodeDict) + node.rate + '%').join(" | ");
                schemes.forEach((scheme) => {
                    if (scheme._TEMPID === rowId) {
                        scheme.name = schemeTitle
                        scheme.title = schemeTitle
                        scheme.description = payload.description
                        scheme.schemeNodes = payload.schemeNodes
                    }
                })
                schemeContainer.updateData([...schemes])
            }
        })
    }

    // 删除付款方案
    deletePaymentScheme = async ({ record }) => {
        utils.openGlobalLoading()
        // 如果付款方案关联了价格方案二次确认
        let exist = await this.queryExistPriceScheme(record)
        if (exist){
            utils.confirm({
                content: '当前付款方案已经关联了价格方案，删除付款方案将会同步删除协议清单对应的价格方案！',
            }).then((result: boolean) => {
                if (result) {
                    this.deletePaymentSchemeRel(record)
                }
            })
        }else {
            this.deletePaymentSchemeRel(record)
        }
        utils.closeGlobalLoading()
    }

    // 删除，新增，选择模版后，设置第一个付款方案为应用
    applyFirstPayment = ()=>{
        const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')

        //  如果只有一个付款方案，自动设置成应用
        if (schemeContainer?.data?.length==1){
            schemeContainer.data[0].apply = true
            schemeContainer.updateData(schemeContainer.data)
        } else if (schemeContainer?.data?.length>1) {
            let res = schemeContainer.data.every(item => !item.apply)
            if (res){
                schemeContainer.data[0].apply = true
                schemeContainer.updateData(schemeContainer.data)
            }
        }
    }

    deletePaymentSchemeRel = async (record) => {
        const schemeContainer = this.getContainerByKey('agreementPaymentSchemes')
        const schemes = schemeContainer.data

        const rowId = record._TEMPID;
        schemeContainer.updateData([...schemes.filter(scheme => rowId != scheme._TEMPID)])

        this.applyFirstPayment()
    }

    queryExistPriceScheme = async (record)=>{
        if (!record?.id){
            return false
        }

        const agreementContainer = this.getContainerByKey('agreement').data
        const dsResult = await this.getDataSource('DataStore', {
            modelKey: 'b2b_contract_PaymentRelatePriceSchemeBO',
            fields: ['id'],
            condition: {
                expression: 'agreementBO = ? and paymentScheme=?',
                params: [agreementContainer.id,record.id]
            }
        });

        if (dsResult?.data?.length>0){
            return true
        }
        return false
    }

    addPayableAmtScheme = () => {
        const agreementContainer = this.getContainerByKey('agreement')
        let typeMsg =  agreementContainer.data.type ==='virtual'? this.agTx = '收' : this.agTx = '付'
        let type =  agreementContainer.data.type

        this.openView('b2b_contract_PayableSchemeTO_PayableAmtSchemeCreate', {
            openViewType: 'Dialog',
            env: {typeMsg: typeMsg, type: type},
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('payableScheme')
                const data = schemeContainer.data

                let title = payload.schemeNodes.map(item => item.payableRatio + '%').join(" | ");
                let content = payload.schemeNodes.map(item => "对账确认后" + item.payablePeriod +
                    translatePeriodDict(item.periodType)).join(" | ");
                schemeContainer.updateData([...data,
                {
                    title: title,
                    content: content,
                    mark: payload.mark,
                    schemeNodes: payload.schemeNodes
                }])
            }
        })
    }

    selectPayableAmtScheme= () => {
        this.openView('b2b_contract_PayableSchemeTemplateBO_PayableAmtSchemeTemplateSelectList', {
            openViewType: 'Dialog',
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('payableScheme')
                const data = schemeContainer.data

                let existWithCodeList =  data.filter(it=>!!it['code'])
                let existWithoutCodeList =  data.filter(it=>!it['code'])

                let res = [...data]

                let existWithCodeMap =existWithCodeList.reduce((map, item) => {
                    map[item['code']] = item
                    return map;
                },{})

                for (const it of payload.record) {
                    if (!existWithCodeMap[it.code]){
                        let copy  = {...it};

                        copy['payableSchemeTemplateBO'] = it
                        copy['id'] = null
                        res.push(copy)
                    }
                }

                schemeContainer.updateData(res)
            }
        })
    }

    // 编辑应付账款方案
    editPayableScheme = ({ record }) => {
        const agreementContainer = this.getContainerByKey('agreement')
        let typeMsg =  agreementContainer.data.type ==='virtual'? this.agTx = '收' : this.agTx = '付'

        const rowId = record._TEMPID
        console.log("scheme,,,,,,", record)
        this.openView('b2b_contract_PayableSchemeTO_PayableAmtSchemeEdit', {
            openViewType: 'Dialog',
            env: {
                scheme: record,
                typeMsg: typeMsg,
                type: agreementContainer.data.type
            },
            payloadCallback: (payload) => {
                const schemeContainer = this.getContainerByKey('payableScheme')
                const data = schemeContainer.data
                console.log("=== editPayableScheme, payload=", payload)
                console.log("=== editPayableScheme, data=", data)
                let title = payload.schemeNodes.map(item => item.payableRatio + '%').join(" | ");
                let content = payload.schemeNodes.map(item => "对账确认后" + item.payablePeriod + translatePeriodDict(item.periodType)).join(" | ");
                data.forEach((scheme) => {
                    if (scheme._TEMPID === rowId) {
                        scheme.title = title
                        scheme.content = content
                        scheme.mark = payload.mark
                        scheme.schemeNodes = payload.schemeNodes
                    }
                })
                schemeContainer.updateData([...data])
            }
        })
    }

    // 删除应付账款方案
    deletePayableScheme = ({ record }) => {
        const schemeContainer = this.getContainerByKey('payableScheme')
        const data = schemeContainer.data

        const rowId = record._TEMPID;
        schemeContainer.updateData([...data.filter(scheme => rowId != scheme._TEMPID)])
    }

    applyPaymentScheme = ({ index, record })=>{
        utils.confirm({
            content: '是否应用该付款方案下对应的协议价格？',
        }).then((result: boolean) => {
            if (result) {
                let agreementPaymentSchemesContainer = this.getContainerByKey('agreementPaymentSchemes')
                let agreementPaymentSchemes = agreementPaymentSchemesContainer.data

                // 先全置为false
                agreementPaymentSchemes.forEach(obj => {
                    obj.apply = false;
                });
                agreementPaymentSchemes[index].apply = true;
                agreementPaymentSchemesContainer.updateData(agreementPaymentSchemes)
            }
        })
    }



    // 映射标品
    mapSpu = async ({ record }) => {
        console.log("key = agreementDetailList");
        this.openView('item_SpuBO_SingleSelectAgreementSpu', {
            env: { agreementId: record.agreementBO.id , thingMaterialId:record.thingMaterialId },
            openViewType: 'Dialog',
            payloadCallback: async (context) => {
                const container = this.getContainerByKey('agreementDetailList')
                console.log('payloadCallback ', context)
                const selectSpu = context.record[0]
                try {
                    record.spuBO = selectSpu
                    let request = record;
                    let result = await this.triggerLogicFunction('b2b_contract_AgreementAndSpuMappingFunc', [request]);
                    showMessage({
                        level: "Weak",
                        message: "操作成功",
                        type: "Success"
                    })
                    container.refresh()
                    console.log('b2b_contract_AgreementAndSpuMappingFunc result, ', result)
                } catch (ex) {
                    console.log('b2b_contract_AgreementAndSpuMappingFunc 调用失败', ex.message);
                    showMessage({
                        level: "Weak",
                        message: ex.message,
                        type: "Error"
                    })
                }
            }
        })
    }
    // 映射标品
    subMapSpu = async ({ record }) => {
        console.log("key = supplementAgreementDetailList 行内");
        this.openView('item_SpuBO_SingleSelectAgreementSpu', {
            env: { agreementId: record.agreementBO.id , thingMaterialId:record.thingMaterialId },
            openViewType: 'Dialog',
            payloadCallback: async (context) => {
                const container = this.getContainerByKey('supplementAgreementDetailList')
                console.log('payloadCallback ', context)
                const selectSpu = context.record[0]
                try {
                    record.spuBO = selectSpu
                    let request = record;
                    let result = await this.triggerLogicFunction('b2b_contract_AgreementAndSpuMappingFunc', [request]);
                    showMessage({
                        level: "Weak",
                        message: "操作成功",
                        type: "Success"
                    })
                    container.refresh()
                    console.log('b2b_contract_AgreementAndSpuMappingFunc result, ', result)
                } catch (ex) {
                    console.log('b2b_contract_AgreementAndSpuMappingFunc 调用失败', ex.message);
                    showMessage({
                        level: "Weak",
                        message: ex.message,
                        type: "Error"
                    })
                }
            }
        })
    }

    payloadCallbackPriceSchemeBO = async(payload) => {
        console.log("payload" + payload );
        console.log(JSON.stringify(payload));
        if (payload.recordOld.agreementBO.type==='match_making'){
            //payload: {
            //    paymentRelatePriceScheme: paymentRelatePriceScheme,
            //    recordOld:this.env.record
            //}
             const table = this.getContainerByKey('agreementDetailList');
             const newRecord = {...payload.recordOld};
             if (!newRecord.priceSchemeBOList) {
               newRecord.priceSchemeBOList = [];
             };
             newRecord.priceSchemeBOList = payload.paymentRelatePriceScheme
                                               .filter(item=> item.priceScheme)
                                               .map(item => { return item.priceScheme;});
             //获取赋值索引
             const index = table.data.findIndex(line => line._TEMPID === payload.recordOld._TEMPID);
             console.log("newRecord.priceSchemeBOList" + newRecord.priceSchemeBOList);
             table.updateData(newRecord,index);
        }
    }

    payloadCallbackSupplementPriceSchemeBO= async(payload) => {
        console.log("payload" + payload );
        console.log(JSON.stringify(payload));
        if (payload.recordOld.agreementBO.type==='match_making'){
          //payload: {
          //    paymentRelatePriceScheme: paymentRelatePriceScheme,
          //    recordOld:this.env.record
          //}
           const table = this.getContainerByKey('supplementAgreementDetailList');
           const newRecord = {...payload.recordOld};
           if (!newRecord.priceSchemeBOList) {
                newRecord.priceSchemeBOList = [];
           };
           newRecord.priceSchemeBOList = payload.paymentRelatePriceScheme
                                .filter(item=> item.priceScheme)
                                .map(item => { return item.priceScheme;});
           //获取赋值索引
           const index = table.data.findIndex(line => line._TEMPID === payload.recordOld._TEMPID);
           console.log("newRecord.priceSchemeBOList" + newRecord.priceSchemeBOList);
           table.updateData(newRecord,index);
        }
    }





    // 设置价格方案
    setPriceScheme = async ({ record }) => {
        console.log("key = agreementDetailList");
        if (record.agreementBO.type==='match_making'){
            this.openView('b2b_contract_PaymentRelatePriceSchemeCTO_SelectPaymentScheme', {
                openViewType: 'Dialog',
                record:record
            });
            const container = this.getContainerByKey('agreementDetailList');
            container.refresh();
        }else {
            this.openView('item_PriceSchemeBO_SingleSelectAgreementPriceScheme', {
                env: {
                    spuId: record.spuBO.id,
                    agreementId: record.agreementBO.id,
                    agreementType: this.getContainerByKey('agreement').data.type
                },
                openViewType: 'Dialog',
                payloadCallback: async (context) => {
                    const container = this.getContainerByKey('agreementDetailList')
                    console.log('payloadCallback ', context)
                    const selectPriceScheme = context.record[0]
                    try {
                        record.priceSchemeBO = selectPriceScheme
                        let request = record;
                        let result = await this.triggerLogicFunction('b2b_contract_AgreementAndPriceMappingFunc', [request]);
                        showMessage({
                            level: "Weak",
                            message: "操作成功",
                            type: "Success"
                        })
                        container.refresh()
                        console.log('b2b_contract_AgreementAndPriceMappingFunc result, ', result)
                    } catch (ex) {
                        console.log('b2b_contract_AgreementAndPriceMappingFunc 调用失败', ex.message);
                        showMessage({
                            level: "Weak",
                            message: ex.message,
                            type: "Error"
                        })
                    }
                }
            })
        }
    }

    //设置价格方案
    subSetPriceScheme = async ({ record }) => {
        console.log("key = supplementAgreementDetailList 行内");
        if (record.agreementBO.type==='match_making'){
            this.openView('b2b_contract_PaymentRelatePriceSchemeCTO_SelectPaymentScheme', {
                openViewType: 'Dialog',
                record:record
            })
        }else {
            this.openView('item_PriceSchemeBO_SingleSelectAgreementPriceScheme', {
                env: {
                    spuId: record.spuBO.id,
                    agreementId: record.agreementBO.id,
                    agreementType: this.getContainerByKey('agreement').data.type
                },
                openViewType: 'Dialog',
                payloadCallback: async (context) => {
                    const container = this.getContainerByKey('supplementAgreementDetailList')
                    console.log('payloadCallback ', context)
                    const selectPriceScheme = context.record[0]
                    try {
                        record.priceSchemeBO = selectPriceScheme
                        let request = record;
                        let result = await this.triggerLogicFunction('b2b_contract_AgreementAndPriceMappingFunc', [request]);
                        showMessage({
                            level: "Weak",
                            message: "操作成功",
                            type: "Success"
                        })
                        container.refresh()
                        console.log('b2b_contract_AgreementAndPriceMappingFunc result, ', result)
                    } catch (ex) {
                        console.log('b2b_contract_AgreementAndPriceMappingFunc 调用失败', ex.message);
                        showMessage({
                            level: "Weak",
                            message: ex.message,
                            type: "Error"
                        })
                    }
                }
            })
        }
    }

    // 批量映射标品
    batchMapSpu = async ({ record }) => {
        console.log("key = agreementDetailList");
        this.openView('item_SpuBO_SingleSelectAgreementSpu', {
            env: { agreementId: record[0].agreementBO.id ,thingMaterialId:record[0].thingMaterialId },
            openViewType: 'Dialog',
            payloadCallback: async (context) => {
                const container = this.getContainerByKey('agreementDetailList')

                console.log('payloadCallback ', context)
                const selectSpu = context.record[0]
                try {
                    let request = {
                        "details": record,
                        "spuBO": selectSpu
                    }
                    let result = await this.triggerLogicFunction('b2b_contract_AgreementAndSpuBatchMappingFunc', [request]);
                    container.refresh()
                    console.log('b2b_contract_AgreementAndSpuBatchMappingFunc result, ', result)
                } catch (ex) {
                    console.log('b2b_contract_AgreementAndSpuBatchMappingFunc 调用失败', ex.message);
                    showMessage({
                        level: "Weak",
                        message: ex.message,
                        type: "Error"
                    })
                }
            }
        })
    }
    // 批量映射标品
    subBatchMapSpu = async ({ record }) => {
        this.openView('item_SpuBO_SingleSelectAgreementSpu', {
            env: { agreementId: record[0].agreementBO.id , thingMaterialId:record[0].thingMaterialId },
            openViewType: 'Dialog',
            payloadCallback: async (context) => {
                const container = this.getContainerByKey('supplementAgreementDetailList')

                console.log('payloadCallback ', context)
                const selectSpu = context.record[0]
                try {
                    let request = {
                        "details": record,
                        "spuBO": selectSpu
                    }
                    let result = await this.triggerLogicFunction('b2b_contract_AgreementAndSpuBatchMappingFunc', [request]);
                    container.refresh()
                    console.log('b2b_contract_AgreementAndSpuBatchMappingFunc result, ', result)
                } catch (ex) {
                    console.log('b2b_contract_AgreementAndSpuBatchMappingFunc 调用失败', ex.message);
                    showMessage({
                        level: "Weak",
                        message: ex.message,
                        type: "Error"
                    })
                }
            }
        })
    }

    // 批量映射标
    batchSetPriceScheme = async ({ record }) => {
        console.log("key = agreementDetailList");
        if (record[0].agreementBO?.type==='match_making'){
            this.openView('b2b_contract_PaymentRelatePriceSchemeCTO_SelectPaymentScheme', {
                openViewType: 'Dialog',
                record:record
            })
        }else {
            this.openView('item_PriceSchemeBO_SingleSelectAgreementPriceScheme', {
                env: {
                    spuId: record[0].spuBO.id, agreementId: record[0].agreementBO.id,
                    agreementType: this.getContainerByKey('agreement').data.type
                },
                openViewType: 'Dialog',
                payloadCallback: async (context) => {
                    const container = this.getContainerByKey('agreementDetailList')
                    console.log('payloadCallback ', context)
                    const selectPriceScheme = context.record[0]
                    try {
                        let request = {
                            "details": record,
                            "priceSchemeBO": selectPriceScheme
                        }
                        let result = await this.triggerLogicFunction('b2b_contract_AgreementAndPriceBatchMappingFunc', [request]);
                        showMessage({
                            level: "Weak",
                            message: "操作成功",
                            type: "Success"
                        })
                        container.refresh()
                        console.log('b2b_contract_AgreementAndPriceBatchMappingFunc result, ', result)
                    } catch (ex) {
                        console.log('b2b_contract_AgreementAndPriceBatchMappingFunc 调用失败', ex.message);
                        showMessage({
                            level: "Weak",
                            message: ex.message,
                            type: "Error"
                        })
                    }
                }
            })
        }
    }



    // 协议清单批量删除校验
    validateBatchDelete = async (record) => {
        console.log("2个协议清单共用方法");
        let data = record.data;
        if (data.length == 0) {
            return "至少选择一行数据"
        }
        return true;
    }

    // 映射标品批量映射校验
    validateBatchMapSpu = async (record) => {
        console.log("key = agreementDetailList");
        let data = record.data;
        if (data.length == 0) {
            return "至少选择一行数据"
        }
        var agreementIdSet = new Set()
        for (let i = 0; i < data.length; i++) {
            let detail = data[i];
            if (!detail.agreementBO) {
                return "关联协议不能为空"
            }
            agreementIdSet.add(detail.agreementBO.id)
            if (agreementIdSet.size > 1) {
                return "仅支持同一协议下的清单批量映射，请先根据协议筛选清单"
            }
        }
        return true;
    }

    // 批量设置价格方案校验
    validateBatchSetPrice = async (record) => {
        console.log("key = agreementDetailList");
        let data = record.data;
        if (data.length == 0) {
            return "至少选择一行数据"
        }
        var agreementIdSet = new Set()
        for (let i = 0; i < data.length; i++) {
            let detail = data[i];
            if (!detail.agreementBO) {
                return "关联协议不能为空"
            }
            agreementIdSet.add(detail.agreementBO.id)
            if (agreementIdSet.size > 1) {
                return "仅支持同一协议下的清单批量设置价格方案，请先根据协议筛选清单"
            }
        }
        return true;
    }

    //查看价格方案
    viewPriceScheme = async ({record})=>{
            console.log("2个协议清单行内共用方法");
            if (record?.agreementBO?.type==='match_making'){
                this.openView('b2b_contract_PaymentRelatePriceSchemeBO_SelectPaymentSchemeDetail', {
                    openViewType: 'Dialog',
                    record:record
                })
            }else {
                if (!record?.priceSchemeBO?.id){
                    showMessage({
                        level: "Weak",
                        message: "未设置价格方案",
                        type: "Error"
                    })
                }else {
                    this.openView('item_PriceSchemeBO_PriceSchemeDetail', {
                        openViewType: 'Blank',
                        record:{id:record.priceSchemeBO.id}
                    })
                }
            }
        }

    //协议清单变更
    agreementDetailListChange = async({ fieldName, value, index, record }) => {
        console.log("========agreementDetailListChange=========");
        console.log(fieldName, value, index,record);
        const table = this.getContainerByKey('agreementDetailList');
        //框架协议
        if (fieldName === 'agreementBO') {
            //重选框架协议之后需要重置标品和价格协议
            // 清空spu和价格方案
            agreementUpdate(table,value,index,record);
        }
        //标品选择
        if (fieldName === 'spuBO') {
            //重选标品之后需要将价格方案置空
            spuBOUpdate(table,value,index,record);
        }
        //税率
        if (fieldName === 'taxRate' || fieldName === 'supplyInfo.taxPrice') {
            console.log(fieldName, record)
            // 含税价格
            let taxAmt = record.supplyInfo.taxPrice.value;
            // 税率
            let taxRate = record.taxRate.taxRate;
            if ((!!taxAmt || taxAmt == 0) && !!taxRate) {
                // 不含税价格
                let noTaxAmt = parseFloat((taxAmt / (1 + taxRate / 100)).toFixed(4));
                // 更新到表格
                const newRecord = {...record};
                if (!newRecord.supplyInfo) {
                  newRecord.supplyInfo = {};
                };
                if (!newRecord.supplyInfo.price) {
                  newRecord.supplyInfo.price = {};
                };
                newRecord.supplyInfo.price.value = noTaxAmt;
                table.updateData(newRecord,index);
            }
        }
        //价格方案
        if (fieldName === 'priceSchemeBO') {
            if (record.agreementBO.type !=='match_making'){
                console.log('payloadCallback ', value);
                const selectPriceScheme = value;
                try {
                    if (!record.priceSchemeBO) {
                        record.priceSchemeBO = {};
                    }
                    record.priceSchemeBO = selectPriceScheme;
                    let request = record;
                    let result = await this.triggerLogicFunction('b2b_contract_AgreementAndPriceMappingFunc', [request]);
                    console.log('b2b_contract_AgreementAndPriceMappingFunc result, ', result)
                    showMessage({
                        level: "Weak",
                        message: "操作成功",
                        type: "Success"
                    })
                    const newRecord = {...record};
                    if (!newRecord.priceSchemeBO) {
                        newRecord.priceSchemeBO = {};
                    };
                    // 如果value是falsey值，则使用空对象{}
                    newRecord.priceSchemeBO = value || {};
                    // 如果value是真值，则使用value.id，否则使用null
                    newRecord.priceSchemeBO.id = value ? value.id : null;
                    table.updateData(newRecord,index);
                    table.refresh();
                } catch (ex) {
                    console.log('b2b_contract_AgreementAndPriceMappingFunc 调用失败', ex.message);
                    showMessage({
                        level: "Weak",
                        message: ex.message,
                        type: "Error"
                    })
                }
            }
        }
    }

    //协议清单变更
    supplementAgreementDetailListChange = async ({ fieldName, value, index, record }) => {
        console.log(fieldName, value, index);
        const table = this.getContainerByKey('supplementAgreementDetailList');
        //框架协议
        if (fieldName === 'agreementBO') {
            //重选框架协议之后需要重置标品和价格协议
            // 清空spu和价格方案
            agreementUpdate(table,value,index,record);
        }
        //标品选择
        if (fieldName === 'spuBO') {
            //重选标品之后需要将价格方案置空
            spuBOUpdate(table,value,index,record);
        }
        //税率
        if (fieldName === 'taxRate' || fieldName === 'supplyInfo.taxPrice') {
            console.log(fieldName, record)
            // 含税价格
            let taxAmt = record.supplyInfo.taxPrice.value;
            // 税率
            let taxRate = record.taxRate.taxRate;
            if ((!!taxAmt || taxAmt == 0) && !!taxRate) {
                // 不含税价格
                let noTaxAmt = parseFloat((taxAmt / (1 + taxRate / 100)).toFixed(4));
                // 更新到表格
                const newRecord = {...record};
                if (!newRecord.supplyInfo) {
                  newRecord.supplyInfo = {};
                };
                if (!newRecord.supplyInfo.price) {
                  newRecord.supplyInfo.price = {};
                };
                newRecord.supplyInfo.price.value = noTaxAmt;
                table.updateData(newRecord,index);
            }
        }
        //价格方案
        if (fieldName === 'priceSchemeBO') {
            if (record.agreementBO.type !=='match_making'){
                console.log('payloadCallback ', value);
                const selectPriceScheme = value;
                try {
                    if (!record.priceSchemeBO) {
                        record.priceSchemeBO = {};
                    }
                    record.priceSchemeBO = selectPriceScheme;
                    let request = record;
                    let result = await this.triggerLogicFunction('b2b_contract_AgreementAndPriceMappingFunc', [request]);
                    console.log('b2b_contract_AgreementAndPriceMappingFunc result, ', result)
                    showMessage({
                        level: "Weak",
                        message: "操作成功",
                        type: "Success"
                    })
                    const newRecord = {...record};
                    if (!newRecord.priceSchemeBO) {
                        newRecord.priceSchemeBO = {};
                    };
                    // 如果value是falsey值，则使用空对象{}
                    newRecord.priceSchemeBO = value || {};
                    // 如果value是真值，则使用value.id，否则使用null
                    newRecord.priceSchemeBO.id = value ? value.id : null;
                    table.updateData(newRecord,index);
                    table.refresh();
                } catch (ex) {
                    console.log('b2b_contract_AgreementAndPriceMappingFunc 调用失败', ex.message);
                    showMessage({
                        level: "Weak",
                        message: ex.message,
                        type: "Error"
                    })
                }
            }
        }
    }


    public formatAttributes(value) {
        return value?.map(attr => {
            const { attributeName, attributeValueType, attributeValueTO, unitName } = attr;
            let attrValue;
            if (attributeValueType === 'TEXT') {
                attrValue = attributeValueTO?.textValue;
            }
            if (attributeValueType === 'NUMBER') {
                attrValue = attributeValueTO?.numberValue;
            }
            if (attrValue === undefined) {
                attrValue = '无'
            }
            return `${attributeName}: ${attrValue}${unitName || ''}`
        }).join(' | ');
    }

    public formatYanmiCopperPrice(value) {
        return value?.map(obj => obj?.spuBO?.rawMaterialContent * obj?.referenceBasePriceBO?.copperBasicPrice / 1000 + obj?.otherCosts);;
    }

    submitAgreementAllEdit = async ({ record }) => {
    	console.log(record);
    	//打的form表单
    	const table = this.getContainerByKey('agreement');
    	const tableDetail = [];
    	if( !!this.getContainerByKey('agreement').data.canRelateDetail && !this.getContainerByKey('agreement').data.isSupply){
    		console.log("agreementDetailList");
    		tableDetail = this.getContainerByKey('agreementDetailList').data;
    		console.log(tableDetail);
    	}

    	if(!!this.getContainerByKey('agreement').data.isSupply){
    		//
    		console.log("supplementAgreementDetailList");
    		tableDetail = this.getContainerByKey('supplementAgreementDetailList').data;
    		console.log(tableDetail);
    	}
    	const newRecord = {...record};
        if (!newRecord.details) {
            newRecord.details = [];
        };
    	newRecord.details = tableDetail;
    	console.log(newRecord);
    	try {
            await this.triggerLogicFunction('b2b_contract_EditAgreementFunc', [newRecord]);
            this.goBack();
    	} catch (e) {
            Toast.error(e.message);
        }
    }

    checkBizType = (rule, types, callback, record) => {
        if (this.getContainerByKey('agreement').data.type == 'match_making' && record.projectTypes && record.projectTypes.includes("construction") && types === "project_material") {
            callback("撮合协议的施工项目无工程物资业务");
        }
    }

    filterBizType = (values, record) => {
        if (this.getContainerByKey('agreement').data.type == 'match_making' && record.projectTypes && record.projectTypes.includes("construction")) {
            return values.filter(item => item === 'project_material');
        }
        return [];
    }




}

// 手动翻译，无法获取trantor资源
function translateNodeDict(nodeDict) {
    switch (nodeDict) {
        case 'advance':
            return '预付款'
        case 'progress':
            return '进度款'
        case 'settlement':
            return '结算款'
        case 'complete':
            return '竣工款'
        case 'guarantee':
            return '保修金'
        case 'special':
            return '特殊付款方式'
        default:
            return "";
    }
}


function translatePeriodDict(nodeDict) {
    switch (nodeDict) {
        case 'DAY':
            return '天'
        case 'MONTH':
            return '月'

        default:
            return "";
    }
}

/*  重选框架协议之后需要重置标品和价格协议,税率不清空 */
function agreementUpdate(table,value,index,record){
    if (!newRecord.agreementBO) {
      newRecord.agreementBO = {};
    };
    //关联分类
    newRecord.agreementBO = value;
    //关联分类
    newRecord.spuBO = null;
    //关联物料
    newRecord.spuBO.thing = null;
    //关联物料
    newRecord.spuBO.thingName = null;
    //关联属性
    newRecord.spuBO.attributes = null;
    //计量单位
    newRecord.unit = null;
    //规格型号
    newRecord.spuBO.thingSize = null;
    //编码
    newRecord.spuBO.spuCode = null;
    //材质
    newRecord.spuBO.thingMaterial = null;
    //名字
    newRecord.spuBO.name = null;
    newRecord.spuBO.rawMaterialContent = null;
    newRecord.priceSchemeBO = null;
    table.updateData(newRecord,index);
}

function spuBOUpdate(table,value,index,record){
    const newRecord = {...record};
    if (!newRecord.spuBO) {
      newRecord.spuBO = {};
    };
    newRecord.spuBO =  value;
    if (!newRecord.categoryBO) {
      newRecord.categoryBO = {};
    };
    //关联分类
    newRecord.categoryBO = value.category;
    //关联分类
    newRecord.categoryBO.path = value.category.path;
    if (!newRecord.spuBO.thing) {
      newRecord.spuBO.thing = {};
    };
    //关联物料
    newRecord.spuBO.thing = value.thing;
    //关联物料
    newRecord.spuBO.thingName =  value.thing.thingName;
    //关联属性
    newRecord.spuBO.attributes =  value.attributes;
    if (!newRecord.unit) {
      newRecord.unit = {};
    };
    //计量单位
    newRecord.unit = value.thing.unit;
    //规格型号
    newRecord.spuBO.thingSize = value.thingSize;
    //编码
    newRecord.spuBO.spuCode = value.spuCode;
    //材质
    newRecord.spuBO.thingMaterial = value.thingMaterial;
    //名字
    newRecord.spuBO.name = value.name;
    //价格方案
    if (record.agreementBO.type ==='match_making'){
        if (record.statusDict == 'START_USING' || record.statusDict == 'STOP_USING'){
            //撮合类型协议，符合该条件才可以修改
            newRecord.priceSchemeBOList =  [];
        }
    }else{
        //非撮合协议随意变更
        newRecord.priceSchemeBO =  {};
    }
    newRecord.spuBO.rawMaterialContent = value.rawMaterialContent;
    let rawMaterialContent = value.rawMaterialContent;
    let copperBasicPrice = record.copperBasicPrice;
    if (!!rawMaterialContent  && !!copperBasicPrice) {
        // 不含税价格
        let a = parseFloat((rawMaterialContent * copperBasicPrice / 1000).toFixed(2));
        newRecord.yanmiCopperPrice = a;
    }
    table.updateData(newRecord,index);
}

