trantor:
  mainModule: contract
  embed-modules:
    - b2b_contract
  enabled-module-keys: b2b_contract
  security:
    white-list:
      - /api/trantor/func/b2b_contract_QueryAgreementBOBuiltInFunc
      - /api/trantor/func/b2b_contract_PagingAgreementBOBuiltInFunc
      - /api/trantor/func/b2b_contract_QueryAgreementDetailBOBuiltInFunc
      - /api/trantor/func/b2b_contract_QueryAgreementContactBOBuiltInFunc
      - /api/trantor/func/b2b_contract_QueryPaymentSchemeBOBuiltInFunc
      - /api/trantor/func/b2b_contract_QueryAgreementCoverAreaBOBuiltInFunc
      - /api/trantor/func/b2b_contract_QueryContractDetailV2Func
      - /api/trantor/func/b2b_contract_QueryEntityListByAgreementFunc
      - /api/trantor/func/b2b_contract_GetDictEnumFunc
      - /api/trantor/func/b2b_contract_QueryCompanyByCategoryAndAreaFunc
      - /api/trantor/func/b2b_contract_QueryCustomerDiscountFactorFunc
      - /api/trantor/func/b2b_contract_QueryPreferentialPolicyFunc
      - /api/trantor/func/b2b_contract_QuerySkuPayableSchemeByProjectFunc
      - /api/trantor/func/b2b_contract_SelfSpcCalEndpointFunc
      - /api/trantor/func/b2b_contract_QueryThirdPartyServiceGoodsByAgreementFunc
      - /api/trantor/func/b2b_contract_QuerySelfServiceGoodsByAgreementFunc
      - /api/trantor/func/b2b_contract_ExternalPaymentSchemeCreateFunc
      - /api/trantor/func/b2b_contract_ExternalPaymentSchemeDeleteFunc
      - /api/trantor/func/b2b_contract_ExternalPaymentSchemeUpdateFunc
      - /api/trantor/func/b2b_contract_PagingPaymentSchemeTemplateFunc
      - /api/trantor/func/b2b_contract_QueryContractAmtRuleFunc
      - /api/trantor/func/b2b_contract_CreateContractLineForNcpFunc
      - /api/trantor/func/b2b_contract_CheckContractThingForIPMFunc
      - /api/trantor/func/b2b_contract_CheckWordSafeFunc
      - /api/trantor/func/b2b_contract_ReadDiChanCozeFunc

#fadada:
#  host: ${SRM_CONTRACT_FADADA_HOST:http://***************:8070/}
#  appId: ${SRM_CONTRACT_FADADA_APPID:100000}
#  appSecret: ${SRM_CONTRACT_FADADA_APPSECRET:deIBcbciA51dhh21f803Ie74}
#法大大相关环境变量配置, 默认值均为法大大测试平台
fadada:
  appId: ${FADADA_APPID:80000529}
  appSecret: ${FADADA_APPSECRET:SNC0YT1ORUPEAYIHKHPGHCDLUGNUVOZK}
  openCorpId: ${FADADA_OPENCORPID:434aa027bcac447db751c257b7e4364e}
  apiUrl: ${FADADA_APIURL:https://uat-api.fadada.com/api/v5}
  smsReplaceUrl: ${FADADA_SMSREPLACEURL:https://test.fdd9.cn/}
  smsTemplateId: ${FADADA_SMSTEMPLATEID:FADADA_SIGNATURE_AUTH}
  smsCorpReplaceUrl: ${FADADA_SMSREPLACEURL_CORP:https://80000529.uat-e.fadada.com/authorizeui/corp/login?authSerial=}
  smsCorpTemplateId: ${FADADA_SMSTEMPLATEID_CORP:FADADA_SIGNATURE_AUTH_CORP}


bpm:
  host: ${BPM_SUBMIT_PAGE_HOST:http://localhost:8080}
  dalaranUrl: ${BPM_CREATE_DALARAN_URL:http://localhost:8080}
  approvalDetailDalaranUrl: ${BPM_APPROVAL_DETAIL_DALARAN_URL:http://localhost:8080}
  contractInvalidDalaranUrl: ${BPM_CONTRACT_INVALID_DALARAN_URL:http://localhost:8080}
  contractDelayDalaranUrl: ${BPM_CONTRACT_DELAY_DALARAN_URL:http://localhost:8080}

sign:
  dalaranUrl: ${ELECTRONIC_SIGN_DALARAN_URL:http://localhost:8080}
  autoDalaranUrl: ${ELECTRONIC_SIGN_AUTO_DALARAN_URL:http://localhost:8080}
  checkIfSupplierCanSignOnlineDalaranUrl: ${CHECK_IF_SUPPLIER_CAN_SIGN_ONLINE_DALARAN_URL:http://localhost:8080}

archive:
  dalaranUrl: ${ARCHIVE_FILE_DALARAN_URL:http://localhost:8080}

push:
  dalaranUrl: ${PUSH_SAP_DALARAN_URL:http://localhost:8080}

openOffice:
  host: open-office-457826a929.project-499-prod.svc.cluster.local
  port: 8100

partyBMapping: ${PARTY_B_MAPPING:}


yj:
  batchAddPool: ${ADD_POOL:https://y-mall-biz-server.zctt.cscec1bre.com/open/intentionPool/batchAdd}

# MQ
terminus:
  mqServerAddress: ${MQ_SERVER_ADDRESS:127.0.0.1:9876}
  producerGroup: ${PRODUCER_GROUP:ep_contract_producer_group}
  consumerGroup: ${CONSUMER_GROUP:ep_contract_consumer_group}
  clientType: ROCKETMQ

notice:
  YzSendNoSignNoticeCode: ${YZ_SEND_NO_SIGN_NOTICE_CODE:STATION_LETTER1686726636964}
  template:
    agreementExpiredTemplateCode: ${AGREEMENT_EXPIRED_TEMPLATE_CODE:STATION_LETTER1682563771624}
    agreementEnableApplyTemplateCode: ${AGREEMENT_ENABLE_APPLY_TEMPLATE_CODE:STATION_LETTER1694080978831}
    agreementDisableApplyTemplateCode: ${AGREEMENT_DISABLE_APPLY_TEMPLATE_CODE:STATION_LETTER1694081007286}

# 合同/协议清单导入导出模板中，包含的属性列
contract.import.attribute:
  names: ${CONTRACT_IMPORT_ATTR_NAMES:定尺长度}
  prefix: ${CONTRACT_IMPORT_ATTR_PREFIX:属性：}